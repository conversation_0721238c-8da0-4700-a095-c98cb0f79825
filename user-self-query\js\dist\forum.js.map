{"version": 3, "file": "forum.js", "mappings": "MACA,IAAIA,EAAsB,CCA1BA,EAAyBC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,WAC7B,IAAOF,EAAiB,QACxB,IAAM,EAEP,OADAD,EAAoBI,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GCLRF,EAAwB,CAACM,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXP,EAAoBS,EAAEF,EAAYC,KAASR,EAAoBS,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3ER,EAAwB,CAACc,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFf,EAAyBM,IACH,oBAAXa,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeL,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeL,EAAS,aAAc,CAAEe,OAAO,M,+BCLvD,MAAM,EAA+BC,OAAOC,KAAKC,OAAO,a,aCAzC,SAASC,EAAgBhB,EAAGiB,GAMzC,OALAD,EAAkBf,OAAOiB,gBAAkB,SAAyBlB,EAAGiB,GAErE,OADAjB,EAAEmB,UAAYF,EACPjB,GAGFgB,EAAgBhB,EAAGiB,GCNSJ,OAAOC,KAAKC,OAAe,OCAhE,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,mB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,wB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,4B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,qB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,gB,aCMnCK,EAAAA,SAAAA,GCLN,IAAwBC,EAAUC,E,kDAAAA,E,GAAVD,E,GAC5Bd,UAAYN,OAAOsB,OAAOD,EAAWf,WAC9Cc,EAASd,UAAUiB,YAAcH,EACjCH,EAAeG,EAAUC,G,2BDGzBG,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACbC,KAAKC,UAAY,aACjBD,KAAKE,MAAQC,GAAAA,CAAO,IACpBH,KAAKI,QAAS,EACdJ,KAAKK,SAAW,KAChBL,KAAKM,QAAU,EACfN,KAAKO,OAAS,GAEd,IACMC,GADiBC,IAAIC,MAAMC,UAAU,qBAAuB,IAC9BC,MAAM,KAC1CZ,KAAKa,QAAUb,KAAKc,QAAQN,I,EAI9BO,KAAA,WAAO,WACL,OACE,SAAKC,UAAU,aACZC,IAAAA,UAAAA,OAED,SAAKD,UAAU,aACb,SAAKA,UAAU,oBACb,SAAKA,UAAU,yBACb,YAAKE,GAAAA,CAAUD,IAAAA,UAAAA,eAAmCE,aAGpD,SAAKC,MAAM,0CACT,SAAKA,MAAM,0CACT,SAAKA,MAAM,qCAAoC,WAAOC,KAAK,SAASC,YAAY,YAAYN,UAAU,cAAcO,KAAMvB,KAAKE,SAC/H,aACGsB,IAAAA,UAAiB,CACdC,GAAI,qBACJL,MAAM,8HACNJ,UAAW,yBACXU,QAAS,WACP,EAAKC,UAGT,MAGDH,IAAAA,UAAiB,CACdC,GAAI,qBACJL,MAAM,gHACNJ,UAAW,SACXU,QAAS,WACP,EAAKE,UAGT,MAGDJ,IAAAA,UAAiB,CACdJ,MAAM,2IACNK,GAAI,sBACJT,UAAW,yBACXU,QAAS,WACP,EAAKG,WAGT,MAGDL,IAAAA,UAAiB,CAChBJ,MAAM,qFACNJ,UAAW,yBACXU,QAAS,WACP,EAAKI,YAGT,QAMJ,aACE,SAAKL,GAAG,oBAAoBL,MAAM,6HAIpC,SAAKA,MAAM,wCACT,mCACA,2C,EAUdW,SAAA,WACEC,EAAE,sBAAsBC,S,EAG1BH,QAAA,WACE,IAAII,EAAQC,SAASC,cACrBF,EAAMG,WAAWF,SAASG,eAAe,sBACzCC,OAAOC,eAAeC,kBACtBF,OAAOC,eAAeE,SAASR,GAC/BC,SAASQ,YAAY,QACrBJ,OAAOC,eAAeC,kBACtBG,MAAM,U,EAGRhB,MAAA,WACE5B,KAAKI,QAAS,EACd4B,EAAE,sBAAsBa,OAAO,kEAC/Bb,EAAE,uBAAuBc,OACzBd,EAAE,wBAAwBe,Q,EAG5BlB,OAAA,WACE7B,KAAKI,QAAS,EACd4B,EAAE,sBAAsBa,OAAO,oEAC/Bb,EAAE,uBAAuBe,OACzBf,EAAE,wBAAwBc,Q,EAG5BnB,MAAA,WACE,IAAIzB,EAAQF,KAAKE,QAGjB,IAAgB,IAFCF,KAAKgD,YAAY9C,GAGhC0C,MAAM,kBACH,CACH5C,KAAK+B,WACL,IAAIkB,EAAW3E,OAAO4E,KAAKlD,KAAKO,QAAQ4C,OACxCnD,KAAKoD,SAASH,EAAS,KACvBjB,EAAE,uBAAuBe,OACzBf,EAAE,uBAAuBc,S,EAI7BM,SAAA,SAASH,EAASI,GAChB,IAAIC,EAAQtD,UAEeuD,IAAxBvD,KAAKO,OAAO0C,KACbjD,KAAKO,OAAO0C,GAAY,GAG1BO,YAAW,WACT,IAAkB,IAAfF,EAAMlD,OACPkD,EAAMF,SAASH,OACZ,CACH,IAAI3C,EAAUgD,EAAM/C,OAAO0C,GACvBQ,EAAOH,EAAMI,UAAU,EAAE,GAC7B1B,EAAE,sBAAsBa,OAAOS,EAAMK,UAAUrD,EAAQmD,IACvDzB,EAAE,sBAAsB4B,UAAU,UAE/BtD,IAAUgD,EAAMjD,UACjB2B,EAAE,uBAAuBe,OACzBf,EAAE,uBAAuBc,OACzBd,EAAE,wBAAwBc,SAE1BQ,EAAM/C,OAAO0C,KACbK,EAAMF,SAASH,YAGXM,IAARF,EAAkBA,EAAMC,EAAMI,UAAU,IAAK,O,EAGjDC,UAAA,SAAUrD,EAAQmD,GAChB,IAAII,EAAO,SAAQ,IAAIC,MAAOC,eAAe,SAAS,SAASzD,EAAQ,GAAG,UA2B1E,OAzBGA,IAAUN,KAAKK,SAChBwD,EAAO,sDAEG,IAAPJ,GAAkC,IAAtBzD,KAAKa,QAAQsC,SAC1BM,EAAO,GAGC,IAAPA,EACDI,GAAM,gDACQ,IAAPJ,GACPI,GAAM,4BAA4B7D,KAAKa,QAAQ,GAAG,8BAA8Bb,KAAKa,QAAQ,GAAG,SAChGgD,GAAM,6CACNA,GAAM,oDACN7D,KAAKa,QAAQmD,SACC,IAAPP,EACPI,GAAM,kDACQ,IAAPJ,IACPI,GAAM,8CACNA,GAAM,4CAIVA,GAAM,kFAGC,SAFPA,GAAM,0DAEc,U,EAGtBH,UAAA,SAAUO,EAAKC,GACb,OAAOC,KAAKC,MAAMD,KAAKE,UAAYH,EAAMD,EAAM,GAAKA,I,EAGtDnD,QAAA,SAAQwD,GAGN,IAFA,IAAkCC,EAA9BC,EAAeF,EAAMnB,OAEF,GAAhBqB,GAAmB,CACxBD,EAAcJ,KAAKC,MAAMD,KAAKE,SAAWG,GACzCA,IAFwB,MAIoB,CAC1CF,EAAMC,GAAcD,EAAME,IAD3BF,EAAME,GAJiB,KAIFF,EAAMC,GAJJ,KAQ1B,OAAOD,G,EAGTtB,YAAA,SAAYyB,GAIV,QAHS,6BACOC,KAAKD,I,EAxNJhF,CAA+BkF,KEFpDlE,IAAAA,aAAAA,IAAqB,mBAAmB,WACtCA,IAAAA,OAAAA,cAA8B,CAC1BmE,KAAM,iBACNC,UAAWpF,O", "sources": ["webpack://@wusong8899/flarum-daily-check-in/webpack/bootstrap", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/compat get default export", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/define property getters", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/hasOwnProperty shorthand", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/make namespace object", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['forum/app']\"", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['extend']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Page']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/IndexPage']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/helpers/listItems']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Button']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['utils/Stream']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/UserSelfQueryIndexPage.js", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/index.js"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['forum/app'];", "export default function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['extend'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Page'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/IndexPage'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/helpers/listItems'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Button'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['utils/Stream'];", "import Page from 'flarum/components/Page';\nimport IndexPage from 'flarum/components/IndexPage';\nimport listItems from 'flarum/common/helpers/listItems';\nimport Button from 'flarum/components/Button';\nimport Stream from 'flarum/utils/Stream';\n\nexport default class UserSelfQueryIndexPage extends Page {\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.bodyClass = 'App--index';\n    this.phone = Stream(\"\");\n    this.paused = false;\n    this.maxCount = 1986;\n    this.counter = 0;\n    this.thread = {};\n\n    const urlListSetting = app.forum.attribute(\"userSelfQueryUrl\") || \"\";\n    const urlListSplit = urlListSetting.split(\",\");\n    this.urlList = this.shuffle(urlListSplit);\n\n  }\n\n  view() {\n    return (\n      <div className=\"IndexPage\">\n        {IndexPage.prototype.hero()}\n\n        <div className=\"container\">\n          <div className=\"sideNavContainer\">\n            <nav className=\"IndexPage-nav sideNav\">\n              <ul>{listItems(IndexPage.prototype.sidebarItems().toArray())}</ul>\n            </nav>\n\n            <div style=\"padding: 25px 0px 0px 0px;width: 100%;\">\n              <div style=\"padding-bottom: 20px;position:relative\">\n                <div style=\"display: inline-block;width: 80%;\"><input type=\"number\" placeholder=\"请输入查询的手机号\" className=\"FormControl\" bidi={this.phone} /></div>\n                <div>\n                  {Button.component({\n                      id: 'userSelfQueryStart',\n                      style:'width: 66px;position: absolute;top: 0px;right: 70px;font-weight: normal !important;background: rgb(230, 96, 27) !important;',\n                      className: 'Button Button--primary',\n                      onclick: () => {\n                        this.start();\n                      }\n                    },\n                    \"开始\"\n                  )}\n\n                  {Button.component({\n                      id: 'userSelfQueryPause',\n                      style:'display:none;width: 66px;display:none;position: absolute;top: 0px;right: 70px;font-weight: normal !important;',\n                      className: 'Button',\n                      onclick: () => {\n                        this.pause();\n                      }\n                    },\n                    \"暂停\"\n                  )}\n\n                  {Button.component({\n                      style:'display:none;width: 66px;position: absolute;top: 0px;right: 70px;font-weight: normal !important;background: rgb(230, 96, 27) !important;',\n                      id: 'userSelfQueryResume',\n                      className: 'Button Button--primary',\n                      onclick: () => {\n                        this.resume();\n                      }\n                    },\n                    \"继续\"\n                  )}\n\n                  {Button.component({\n                    style:'width: 66px;position: absolute;top: 0px;right: 0px;font-weight: normal !important;',\n                    className: 'Button Button--primary',\n                    onclick: () => {\n                      this.copyLog();\n                    }\n                  },\n                  \"复制\"\n                )}\n                </div>\n              </div>\n\n\n              <div>\n                <div id=\"userSelfQueryLogs\" style=\"border:1px solid rgba(255,255,255,.2);height: 400px;overflow-y: auto;text-align: left;padding: 15px;border-radius: 4px;\">\n                </div>\n              </div>\n\n              <div style=\"text-align:center;padding-top: 10px;\">\n                <div>当前0.5测试版存在BUG 正在更新</div>\n                <div>开发0.8更新后使用反馈投稿社区留言致电</div>\n              </div>\n\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  clearLog(){\n    $(\"#userSelfQueryLogs\").empty();\n  }\n\n  copyLog(){\n    var range = document.createRange();\n    range.selectNode(document.getElementById(\"userSelfQueryLogs\"));\n    window.getSelection().removeAllRanges(); \n    window.getSelection().addRange(range); \n    document.execCommand(\"copy\");\n    window.getSelection().removeAllRanges();\n    alert(\"已复制日志\");\n  }\n\n  pause(){\n    this.paused = true;\n    $(\"#userSelfQueryLogs\").append(\"<div>已暂停</div><div><hr style='border-top:1px solid #313952' />\");\n    $(\"#userSelfQueryPause\").hide();\n    $(\"#userSelfQueryResume\").show();\n  }\n\n  resume(){\n    this.paused = false;\n    $(\"#userSelfQueryLogs\").append(\"<div>已恢复查询</div><div><hr style='border-top:1px solid #313952' />\");\n    $(\"#userSelfQueryPause\").show();\n    $(\"#userSelfQueryResume\").hide();\n  }\n\n  start(){\n    let phone = this.phone();\n    let checkPhone = this.checkMobile(phone);\n\n    if(checkPhone===false){\n      alert(\"请输入正确的手机号码\")\n    }else{\n      this.clearLog();\n      let threadID = Object.keys(this.thread).length;\n      this.runQuery(threadID,500);\n      $(\"#userSelfQueryPause\").show();\n      $(\"#userSelfQueryStart\").hide();\n    }\n  }\n\n  runQuery(threadID,delay){\n    let _this = this;\n\n    if(this.thread[threadID]===undefined){\n      this.thread[threadID] = 0;\n    }\n\n    setTimeout(function(){\n      if(_this.paused===true){\n        _this.runQuery(threadID);\n      }else{\n        let counter = _this.thread[threadID];\n        let seed = _this.randomInt(1,4);\n        $(\"#userSelfQueryLogs\").append(_this.getResult(counter,seed));\n        $('#userSelfQueryLogs').scrollTop(99999999);\n\n        if(counter===_this.maxCount){\n          $(\"#userSelfQueryStart\").show();\n          $(\"#userSelfQueryPause\").hide();\n          $(\"#userSelfQueryResume\").hide();\n        }else{\n          _this.thread[threadID]++;\n          _this.runQuery(threadID);\n        }\n      }\n    },delay!==undefined?delay:_this.randomInt(3000,4000));\n  }\n\n  getResult(counter,seed){\n    let text = \"<div>\"+new Date().toLocaleString('sv-SE')+\" 正在查询\"+(counter+1)+\"个</div>\";\n\n    if(counter===this.maxCount){\n      text = \"<div>查询完毕感谢您的使用</div><div>反馈相关事宜投稿站点请添加POP群组</div>\";\n    }else{\n      if(seed===2 && this.urlList.length===0){\n        seed = 1;\n      }\n\n      if(seed===1){\n        text+=\"<div style='color:white'>您的手机号暂未收到邀请，谢谢</div>\";\n      }else if(seed===2){\n        text+=\"<a target='_blank' href='\"+this.urlList[0]+\"'><div style='color:green'>\"+this.urlList[0]+\"</div>\";\n        text+=\"<div style='color:green'>您是尊贵特邀会员，谢谢</div>\";\n        text+=\"<div style='color:green'>请尽快前往特邀网址，避免过期</div></a>\";\n        this.urlList.shift();\n      }else if(seed===3){\n        text+=\"<div style='color:red'>您的手机号并非特邀会员，请勿重复提交</div>\";\n      }else if(seed===4){\n        text+=\"<div style='color:white'>您的手机号已提交申请彩金</div>\";\n        text+=\"<div style='color:white'>信息请勿重复提交</div>\";\n      }\n    }\n\n    text+=\"<div><a target='_blank' href='https://老哥.com'>https://老哥.com 免费供应技术支持</a></div>\";\n    text+=\"<div><hr style='border-top:1px solid #313952' /></div>\";\n\n    return \"<div>\"+text+\"</div>\";\n  }\n\n  randomInt(min, max) {\n    return Math.floor(Math.random() * (max - min + 1) + min);\n  }\n\n  shuffle(array) {\n    let currentIndex = array.length,  randomIndex;\n\n    while (currentIndex != 0) {\n      randomIndex = Math.floor(Math.random() * currentIndex);\n      currentIndex--;\n\n      [array[currentIndex], array[randomIndex]] = [\n        array[randomIndex], array[currentIndex]];\n    }\n\n    return array;\n  }\n\n  checkMobile(mobile) {\n    var re = /^1[3,4,5,6,7,8,9][0-9]{9}$/;\n    var result = re.test(mobile);\n\n    if(!result) {\n      return false;\n    }else{\n      return true;\n    }\n  }\n\n}\n", "import setPrototypeOf from \"./setPrototypeOf.js\";\nexport default function _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  setPrototypeOf(subClass, superClass);\n}", "import app from 'flarum/forum/app';\r\nimport { extend } from 'flarum/extend';\r\nimport UserSelfQueryIndexPage from './components/UserSelfQueryIndexPage';\r\n\r\napp.initializers.add('user-self-query', () => {\r\n  app.routes['userSelfQuery'] = {\r\n      path: '/userSelfQuery',\r\n      component: UserSelfQueryIndexPage,\r\n  };\r\n\r\n});"], "names": ["__webpack_require__", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "flarum", "core", "compat", "_setPrototypeOf", "p", "setPrototypeOf", "__proto__", "UserSelfQueryIndexPage", "subClass", "superClass", "create", "constructor", "oninit", "vnode", "this", "bodyClass", "phone", "Stream", "paused", "maxCount", "counter", "thread", "urlListSplit", "app", "forum", "attribute", "split", "urlList", "shuffle", "view", "className", "IndexPage", "listItems", "toArray", "style", "type", "placeholder", "bidi", "<PERSON><PERSON>", "id", "onclick", "start", "pause", "resume", "copyLog", "clearLog", "$", "empty", "range", "document", "createRange", "selectNode", "getElementById", "window", "getSelection", "removeAllRanges", "addRange", "execCommand", "alert", "append", "hide", "show", "checkMobile", "threadID", "keys", "length", "run<PERSON><PERSON><PERSON>", "delay", "_this", "undefined", "setTimeout", "seed", "randomInt", "getResult", "scrollTop", "text", "Date", "toLocaleString", "shift", "min", "max", "Math", "floor", "random", "array", "randomIndex", "currentIndex", "mobile", "test", "Page", "path", "component"], "sourceRoot": ""}