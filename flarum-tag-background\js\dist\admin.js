(function(t,c,r,l,g,d,u,h){"use strict";const o=class o extends u{constructor(){super(...arguments),this.loading=!1}oninit(n){super.oninit(n),this.loading=!1,this.tagData=this.attrs.tagData,this.backgroundUrl=h(this.tagData.attribute("wusong8899BackgroundURL"))}className(){return"Modal--small"}title(){return t.translator.trans("wusong8899-tag-background.admin.set-background")}content(){return m("div",{className:"Modal-body"},m("div",{className:"Form"},m("div",{className:"Form-group",style:"text-align: center;"},m("div",{className:"ModuleItemSettingsLabel"},t.translator.trans("wusong8899-tag-background.admin.item-background-url")),m("input",{maxlength:"200",className:"FormControl",bidi:this.backgroundUrl})),m("div",{className:"Form-group",style:"text-align: center;"},r.component({style:"min-width:66px;",className:"Button Button--primary",disabled:this.loading,onclick:()=>{this.saveData()}},t.translator.trans("wusong8899-tag-background.admin.save"))," ",r.component({style:"min-width:66px;background: rgba(0,0,0,0.1);",className:"Button",disabled:this.loading,onclick:()=>{this.hide()}},t.translator.trans("wusong8899-tag-background.admin.cancel")))))}saveData(){this.loading=!0;const n=this.backgroundUrl(),e=this.tagData.id();t.request({url:`${t.forum.attribute("apiUrl")}/tagBackgroundSetImage`,method:"POST",body:{tagID:e,backgroundUrl:n}}).then(a=>{this.hide(),t.store.pushPayload(a),console.log(t.store.getById("tags",e)),m.redraw()}).catch(()=>{this.loading=!1})}};o.isDismissibleViaBackdropClick=!1,o.isDismissibleViaCloseButton=!0;let s=o;class k extends c{constructor(){super(...arguments),this.loading=!1}oninit(n){super.oninit(n),this.loading=!0,t.tagList.load(["parent"]).then(()=>{this.loading=!1,m.redraw()})}content(n){if(this.loading)return m(l,null);const e=g(t.store.all("tags").filter(a=>!a.parent()));return m("div",{className:"tagBackgroundSettingsGroups",style:"text-align: left;padding: 20px;"},e.map(a=>{const i=a.attribute("wusong8899BackgroundURL"),b=`background:url(${i});background-size: cover;background-position: center;background-repeat: no-repeat;`;return m("div",{className:"tagBackgroundContainer"},m("div",{className:"tagBackgroundItemContainer"},d(a),m("span",{className:"tagBackgroundItemName TagListItem-name"},a.name()),m("div",{style:"padding-top: 10px;display: flex;justify-content: center;align-items: center;"},i&&m("div",{style:b,className:"tagBackgroundImage",onclick:()=>t.modal.show(s,{tagData:a})}),!i&&m("div",{className:"tagBackgroundImage"},r.component({style:"min-width: 66px;font-size: 12px;font-weight: normal;",className:"Button",onclick:()=>{t.modal.show(s,{tagData:a})}},t.translator.trans("wusong8899-tag-background.admin.set-background"))))))}))}}t.initializers.add("wusong8899-tag-background",()=>{t.extensionData.for("wusong8899-tag-background").registerPage(k)})})(flarum.core.compat["admin/app"],flarum.core.compat["admin/components/ExtensionPage"],flarum.core.compat["common/components/Button"],flarum.core.compat["common/components/LoadingIndicator"],flarum.core.compat["tags/common/utils/sortTags"],flarum.core.compat["tags/common/helpers/tagIcon"],flarum.core.compat["common/components/Modal"],flarum.core.compat["common/utils/Stream"]);
//# sourceMappingURL=admin.js.map

module.exports={};