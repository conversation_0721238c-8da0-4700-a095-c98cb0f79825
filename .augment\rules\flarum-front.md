---
type: "agent_requested"
description: "本规则旨在指导 AI 编码代理进行 Flarum 扩展的前端开发。所有规则适用于单个 Flarum 扩展的代码库。"
---

# Flarum 扩展前端开发规则

## 1. 目的与适用范围

本规则旨在指导 AI 编码代理进行 Flarum 扩展的前端开发。所有规则适用于单个 Flarum 扩展的代码库。

- **主要范围**: 扩展根目录下的 `js/` 子目录，以及用于注册前端资源的 `extend.php` 文件。
- **作用域差异**:
  - `js/src/forum`: 影响公共论坛界面的代码。
  - `js/src/admin`: 影响后台管理界面的代码。
  - `js/src/common`: `forum` 和 `admin` 共享的通用逻辑、组件或工具函数。
  - `extend.php`: 用于在 PHP 后端注册前端资源（JS/CSS）。
- **参考范围**: 项目根目录下的 `vendor/flarum/core` 目录及其子包，可用于查询 Flarum 核心的组件、工具类和类型定义，但 **禁止直接修改**。

## 2. 结构与格式

- **代码组织**: 严格遵循 Flarum 推荐的前端目录结构。
- **原子化规则**: 每条规则都应清晰、独立且可验证。
- **简洁性**: 保持规则精炼，避免冗余信息。

## 3. 工具使用与工作循环

- **代理角色**: 你是一名 Flarum 扩展前端开发者。
- **环境约束**:
  - **本地环境**: 已安装 Node.js 和 `pnpm`。
  - **包管理器**: **必须使用 `pnpm`**，禁止使用 `npm` 或 `yarn`。
  - **可用命令**: `pnpm`, `composer`。你可以在扩展的根目录或 `js/` 子目录中执行命令。
- **工作循环**: 严格遵循 **计划 → 执行 → 验证 → 迭代** 循环。
  - **计划 (Plan)**: 对每个任务，首先拆解成小步骤。
  - **执行 (Execute)**: 每次只执行一个步骤或调用一个工具。例如，先创建文件，再写入内容。
  - **验证 (Verify)**: 每一步操作后，验证其结果是否符合预期。
  - **验收标准**: 在开始任务前，明确定义任务完成的标志。例如：“成功添加一个在帖子操作菜单中显示的按钮，点击后会弹出一个提示框”。

## 4. 语言/框架专属约定

### 4.1 技术栈

- **语言**: TypeScript (首选), JavaScript (ESM)
- **框架**: Mithril.js (通过 Flarum 的 `flarum/common/Component` 基类使用)
- **构建工具**: Webpack (通过 `flarum-webpack-config` 配置)
- **样式**: LESS / CSS

### 4.2 依赖管理

- **路径**: 在扩展的 `js/` 目录下运行 `pnpm` 命令。
- **安装依赖**: `pnpm install`。
- **添加依赖**:
  - 运行时依赖: `pnpm add <package-name>`
  - 开发依赖: `pnpm add -D <package-name>`
- **类型定义**: 必须将 `flarum-tsconfig` 添加为开发依赖：`pnpm add -D flarum-tsconfig`。
- **核心类型同步**: 在 **扩展根目录** 运行 `composer update`，以下载最新的 Flarum 核心代码到 `vendor/` 目录，从而使 `tsconfig.json` 中的路径映射生效，获得 IDE 类型提示。

### 4.3 常用命令

- **开发模式**: 在 `js/` 目录下运行 `pnpm run dev`，它会编译资源并监视文件变化。
- **生产构建**: 在发布或完成开发前，在 `js/` 目录下运行 `pnpm run build`，以生成优化和压缩后的文件。
- **类型检查**: 通常通过 IDE 与 `tsconfig.json` 的集成来实时进行。若有 `type-check` 脚本，也需执行。

### 4.4 核心 Flarum API

- **组件化**:
  - 所有自定义组件必须继承自 `flarum/common/Component`。
  - 在重写生命周期方法（如 `oninit`, `oncreate` 等）时，必须在第一行调用 `super.method(vnode)`。
- **UI 修改**:
  - **禁止直接操作 DOM**。应通过修改 `ItemList` 或重写组件的 `view()` 方法来改变 UI。
  - 使用 `flarum/common/extend` 中的 `extend` 和 `override` 工具函数来“猴子补丁” Flarum 核心组件的方法。
    - `extend(Component.prototype, 'methodName', callback)`: 在原方法执行后，对返回值（如果是可变对象）进行修改。
    - `override(Component.prototype, 'methodName', (original, ...args) => { ... })`: 完全重写方法，可以通过 `original(...args)` 调用原始实现。
- **模块导入**:
  - Flarum 核心模块通过 `flarum/` 前缀导入，例如: `import Component from 'flarum/common/Component';`。
  - 可扩展的其他 Flarum 插件模块（如 `tags`）通过 `flarum/{extension-name}/` 前缀导入，例如: `import TagHero from 'flarum/tags/components/TagHero';`。
- **资源注册**:
  - 所有编译后的 JS 和 CSS 资源**必须**在扩展的 `extend.php` 文件中通过 `Frontend` Extender 注册。
  - `(new Extend\Frontend('forum'))->js(__DIR__.'/js/dist/forum.js')`
  - `(new Extend\Frontend('admin'))->css(__DIR__.'/less/admin.less')`

## 5. 安全与合规

- **输入校验**: 对所有用户输入或外部数据保持警惕，即使在前端也要进行基本的格式校验。
- **XSS 防护**:
  - **默认不信任 HTML**。在使用 Mithril 的 `m.trust()` 或 `oncreate` 中操作 `innerHTML` 时必须格外小心，确保内容是经过严格过滤的。
  - 优先使用 Mithril 的虚拟 DOM 来渲染文本，它会默认进行转义。
- **凭证管理**: 严禁在前端代码中硬编码任何 API 密钥、密码或其他敏感凭证。

## 6. 质量门槛与 CI

在声明任务完成之前，**必须按顺序**在 `js/` 目录中成功执行以下命令：

1.  **格式化 (若有)**: `pnpm run format` (如果 `package.json` 中配置了 Prettier 等工具)。
2.  **类型检查 (若有)**: `pnpm run type-check` (如果 `package.json` 中配置了 `tsc --noEmit`)。
3.  **生产构建**: `pnpm run build`。

**如果任何一个步骤失败，必须停止，报告错误，并提供修复计划。**

## 7. 代码风格与仓库规范

- **命名**:
  - **文件**: 使用 `kebab-case.js` 或 `kebab-case.ts`。
  - **组件类**: `PascalCase`。
  - **变量/函数**: `camelCase`。
- **目录结构**: 严格遵守 Flarum 官方推荐的目录结构。
  - `js/src/forum/components/MyComponent.tsx`
  - `js/src/forum/utils/my-helper.ts`
  - `js/src/forum/index.ts` (作为该前端应用的入口)
- **注释**:
  - 为复杂的逻辑、组件的公共 API (props) 提供 JSDoc 风格的注释。
  - `//` 用于单行解释，`/** ... */` 用于多行或文档块。
- **提交信息**: 遵循 Conventional Commits 规范 (例如: `feat(forum): add new button to discussion page`)。

## 8. 非目标与拒绝策略

- **非目标**:
  - 修改 Flarum 核心代码 (`vendor/flarum/core`)。
  - 执行 PHP 后端逻辑开发（除 `extend.php` 中的资源注册外）。
  - 数据库迁移或修改。
- **拒绝条件**:
  - 当任务描述模糊，如“美化一下界面”时，应请求提供具体的设计稿或明确的修改点。
  - 当请求直接操作 DOM 时，应解释为何要使用 Flarum 的 `extend/override` 机制，并请求以该方式重述需求。
  - 当请求的功能与 Flarum 的数据流或组件生命周期冲突时，应提出风险并请求澄清。

## 9. 最小示例

### 示例 1: 如何新增一个模块并通过质量门槛

**任务**: 在 `forum` 前端添加一个工具函数 `sayHello`，并在主入口调用它。

**验收标准**:

1.  `js/src/forum/utils/greeting.ts` 文件被创建，并导出一个名为 `sayHello` 的函数。
2.  `js/src/forum/index.ts` 导入并调用 `sayHello`。
3.  `pnpm run build` 命令成功执行，没有错误。
4.  浏览器控制台在论坛页面加载时输出 "Hello, Flarum!"。

**代理工作流**:

1.  **计划**:
    - 在 `js/` 目录运行 `pnpm install` 确保依赖最新。
    - 创建 `js/src/forum/utils/` 目录。
    - 创建并写入 `js/src/forum/utils/greeting.ts` 文件。
    - 修改 `js/src/forum/index.ts` 来导入和使用新函数。
    - 运行 `pnpm run build` 进行验证。
2.  **执行**:

    - `mkdir -p js/src/forum/utils`
    - `touch js/src/forum/utils/greeting.ts`
    - 写入 `greeting.ts`: `export const sayHello = (name: string) => console.log(\`Hello, \${name}!\`);`
    - 修改 `index.ts`:

      ```typescript
      import { extend } from "flarum/common/extend";
      import IndexPage from "flarum/forum/components/IndexPage";
      import { sayHello } from "./utils/greeting";

      app.initializers.add("my-extension-id", () => {
        console.log("My extension is alive!");
        sayHello("Flarum");
      });
      ```

3.  **验证**:
    - 在 `js/` 目录中运行 `pnpm run build`。
    - 检查命令是否成功退出且无错误。
4.  **迭代**: 如果构建失败，阅读错误信息并修复代码（例如，修复类型错误）。

### 示例 2: 如何添加一个 UI 元素

**任务**: 在帖子控件中添加一个“Hello”按钮。

**验收标准**:

1.  在每篇帖子的操作菜单（Like、Reply 旁边）出现一个标有“Hello”的新按钮。
2.  点击按钮后，浏览器会弹出一个 `alert`，内容为“Hello from post ID: { postId }”。
3.  `pnpm run build` 命令成功。

**代理工作流**:

1.  **计划**:
    - 确定要扩展的组件 (`flarum/forum/components/PostControls`) 和方法 (`controlItems`)。
    - 在 `js/src/forum/index.ts` 中使用 `extend` 来修改 `PostControls.prototype.controlItems`。
    - 在回调中，向 `items` 这个 `ItemList` 实例中添加一个新的 `Button` 组件。
    - 运行 `pnpm run build`。
2.  **执行**:

    - 修改 `js/src/forum/index.ts`:

    ```typescript
    import { extend } from "flarum/common/extend";
    import PostControls from "flarum/forum/utils/PostControls";
    import Button from "flarum/common/components/Button";

    app.initializers.add("my-extension-id-post-button", () => {
      extend(PostControls, "controls", function (items, post) {
        items.add(
          "hello",
          <Button
            icon="fas fa-hand-spock"
            onclick={() => alert(`Hello from post ID: ${post.id()}`)}
          >
            Hello
          </Button>
        );
      });
    });
    ```

3.  **验证**:
    - 在 `js/` 目录运行 `pnpm run build`。
    - 刷新 Flarum 论坛，查看帖子控件，点击新按钮，验证 `alert` 功能。
