/* Buttons Customization Styles - Independent Extension */

/* Main container for buttons customization */
.buttonCustomizationContainer {
  display: block;
  width: 100%;
  margin-top: 15px;
  margin-bottom: 15px;
  padding: 15px;
  background: var(--body-bg);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Header for buttons section */
.buttons-customization-header {
  font-size: 18px;
  font-weight: bold;
  color: var(--heading-color);
  margin-bottom: 15px;
  text-align: center;
  padding-bottom: 10px;
  border-bottom: 2px solid var(--border-color);
}

/* Wrapper for buttons layout */
.buttons-customization-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
  align-items: stretch;
}

/* Individual button item */
.custom-button-item {
  flex: 1 1 auto;
  min-width: 120px;
  max-width: 200px;
  transition: all 0.3s ease;
}

.custom-button-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Button link styling */
.custom-button-link {
  display: block;
  text-decoration: none;
  color: inherit;
  width: 100%;
  height: 100%;
}

.custom-button-link:hover {
  text-decoration: none;
  color: inherit;
}

/* Button content container */
.custom-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px 12px;
  background: var(--control-bg);
  border: 2px solid var(--border-color);
  border-radius: 8px;
  transition: all 0.3s ease;
  min-height: 80px;
}

.custom-button-content:hover {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

/* Button text styling */
.custom-button-text {
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  line-height: 1.4;
  margin-top: 5px;
  word-break: break-word;
}

/* Icon styling */
.custom-button-content i {
  font-size: 20px;
  margin-bottom: 8px;
  opacity: 0.8;
}

.custom-button-content:hover i {
  opacity: 1;
  transform: scale(1.1);
}

/* Responsive design */
@media (max-width: 768px) {
  .buttonCustomizationContainer {
    margin: 10px 5px;
    padding: 12px;
  }

  .buttons-customization-wrapper {
    gap: 8px;
  }

  .custom-button-item {
    min-width: 100px;
    max-width: 150px;
  }

  .custom-button-content {
    padding: 12px 8px;
    min-height: 70px;
  }

  .custom-button-text {
    font-size: 13px;
  }

  .custom-button-content i {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .buttons-customization-wrapper {
    flex-direction: column;
    align-items: center;
  }

  .custom-button-item {
    width: 100%;
    max-width: 250px;
  }

  .custom-button-content {
    flex-direction: row;
    text-align: left;
    justify-content: flex-start;
    padding: 15px;
  }

  .custom-button-text {
    margin-top: 0;
    margin-left: 12px;
    text-align: left;
  }

  .custom-button-content i {
    margin-bottom: 0;
    margin-right: 0;
  }
}

/* Animation classes */
.buttons-fade-in {
  animation: buttonsCustomizationFadeIn 0.3s ease-out;
}

@keyframes buttonsCustomizationFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading state */
.buttons-customization-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  color: var(--muted-color);
}

.buttons-customization-loading::after {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid var(--muted-color);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Empty state */
.buttons-customization-empty {
  text-align: center;
  padding: 40px 20px;
  color: var(--muted-color);
  font-style: italic;
}

/* Error state */
.buttons-customization-error {
  text-align: center;
  padding: 20px;
  background: var(--danger-bg);
  color: var(--danger-color);
  border-radius: 6px;
  margin: 10px 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .custom-button-content {
    border-width: 3px;
  }

  .custom-button-content:hover {
    border-width: 3px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .custom-button-item,
  .custom-button-content,
  .custom-button-content i {
    transition: none;
  }

  .custom-button-item:hover {
    transform: none;
  }

  .custom-button-content:hover i {
    transform: none;
  }

  .buttons-fade-in {
    animation: none;
  }
}