wusong8899-tag-tiles:
  admin:
    SocialKickUrl: Kick 链接
    SocialKickUrlHelp: 输入您的 Kick 频道链接（留空则隐藏此按钮）
    SocialKickIcon: Kick 图标链接
    SocialKickIconHelp: 输入 Kick 图标图片的链接（留空则使用默认图标）
    SocialFacebookUrl: Facebook 链接
    SocialFacebookUrlHelp: 输入您的 Facebook 页面链接（留空则隐藏此按钮）
    SocialFacebookIcon: Facebook 图标链接
    SocialFacebookIconHelp: 输入 Facebook 图标图片的链接（留空则使用默认图标）
    SocialTwitterUrl: Twitter 链接
    SocialTwitterUrlHelp: 输入您的 Twitter 个人资料链接（留空则隐藏此按钮）
    SocialTwitterIcon: Twitter 图标链接
    SocialTwitterIconHelp: 输入 Twitter 图标图片的链接（留空则使用默认图标）
    SocialYouTubeUrl: YouTube 链接
    SocialYouTubeUrlHelp: 输入您的 YouTube 频道链接（留空则隐藏此按钮）
    SocialYouTubeIcon: YouTube 图标链接
    SocialYouTubeIconHelp: 输入 YouTube 图标图片的链接（留空则使用默认图标）
    SocialInstagramUrl: Instagram 链接
    SocialInstagramUrlHelp: 输入您的 Instagram 个人资料链接（留空则隐藏此按钮）
    SocialInstagramIcon: Instagram 图标链接
    SocialInstagramIconHelp: 输入 Instagram 图标图片的链接（留空则使用默认图标）
    SocialIconHelp: 输入图标图片的链接（留空则使用默认图标）

    # 高级轮播配置
    AdvancedSplideMinSlidesForLoop: 循环模式最小幻灯片数
    AdvancedSplideMinSlidesForLoopHelp: "启用循环模式所需的最小幻灯片数量（默认：2）"
    AdvancedSplideEnableAutoplay: 启用自动播放
    AdvancedSplideEnableAutoplayHelp: 启用自动幻灯片切换
    AdvancedSplideAutoplayInterval: 自动播放间隔（毫秒）
    AdvancedSplideAutoplayIntervalHelp: "幻灯片切换间隔时间，单位毫秒（默认：3000）"
    AdvancedSplideEnableLoopMode: 启用循环模式
    AdvancedSplideEnableLoopModeHelp: 启用无限循环模式，实现连续滑动
    AdvancedSplideTransitionSpeed: 过渡速度（毫秒）
    AdvancedSplideTransitionSpeedHelp: "幻灯片过渡动画持续时间，单位毫秒（默认：800）"
    AdvancedSplideGap: 幻灯片间距
    AdvancedSplideGapHelp: "幻灯片之间的间距（默认：10px）"
    AdvancedSplidePauseOnMouseEnter: 鼠标悬停时暂停
    AdvancedSplidePauseOnMouseEnterHelp: 当鼠标悬停在轮播器上时暂停自动播放
    AdvancedSplideEnableGrabCursor: 启用抓取光标
    AdvancedSplideEnableGrabCursorHelp: 悬停在幻灯片上时显示抓取光标
    AdvancedSplideEnableFreeMode: 启用自由模式
    AdvancedSplideEnableFreeModeHelp: 允许自由滑动，不会自动对齐到幻灯片
  forum:
    loading: 加载中...
    error: 发生错误
    no-data: 暂无数据
