(function(s,f,C,I,p,N,a,o,y,e,x,R,k,_,D,A,E){"use strict";const l=class l extends N{oninit(t){super.oninit(t)}className(){return"Modal--small"}title(){return s.translator.trans("wusong8899-user-submission.forum.submit-success")}content(){return m("div",{className:"Modal-body"},m("div",{className:"Form"},m("div",{className:"Form-group",style:"text-align: center;"},o.component({className:"Button Button--primary",onclick:()=>{this.hide()}},s.translator.trans("wusong8899-user-submission.lib.ok")))))}};l.isDismissibleViaBackdropClick=!1,l.isDismissibleViaCloseButton=!0;let v=l;const c=class c extends N{constructor(){super(...arguments),this.loading=!1}oninit(t){super.oninit(t),this.loading=!1,this.amount=a(""),this.platform=a(""),this.platformAccount=a(""),this.userAccount=a("")}className(){return"Modal--small"}title(){return s.translator.trans("wusong8899-user-submission.forum.item-header")}content(){return m("div",{className:"Modal-body"},m("div",{className:"Form"},m("div",{className:"Form-group",style:"text-align: center;"},m("div",null,m("div",{className:"userSubmissionDataLabel"},s.translator.trans("wusong8899-user-submission.lib.list-amount")),m("input",{type:"number",min:"0",disabled:this.loading,required:!0,className:"FormControl",bidi:this.amount})),m("div",{className:"userSubmissionDataLabel"},s.translator.trans("wusong8899-user-submission.lib.list-platform")),m("input",{maxLength:"500",disabled:this.loading,required:!0,className:"FormControl",bidi:this.platform}),m("div",{className:"userSubmissionDataLabel"},s.translator.trans("wusong8899-user-submission.lib.list-platformAccount")),m("input",{maxLength:"500",disabled:this.loading,required:!0,className:"FormControl",bidi:this.platformAccount}),m("div",{className:"userSubmissionDataLabel"},s.translator.trans("wusong8899-user-submission.lib.list-userAccountFull")),m("input",{maxLength:"500",disabled:this.loading,required:!0,className:"FormControl",bidi:this.userAccount})),m("div",{className:"Form-group",style:"text-align: center;"},o.component({style:"min-width:66px;",className:"Button Button--primary",type:"submit",loading:this.loading},s.translator.trans("wusong8899-user-submission.lib.ok"))," ",o.component({style:"min-width:66px;",className:"Button",loading:this.loading,onclick:()=>{this.hide()}},s.translator.trans("wusong8899-user-submission.lib.cancel")))))}onsubmit(t){t.preventDefault(),this.loading=!0,s.store.createRecord("userSubmissionList").save({amount:this.amount(),platform:this.platform(),platformAccount:this.platformAccount(),userAccount:this.userAccount()}).then(i=>{s.store.pushPayload(i),this.loading=!1,s.modal.show(v)}).catch(()=>{this.loading=!1})}};c.isDismissibleViaBackdropClick=!1,c.isDismissibleViaCloseButton=!0;let u=c;const r=class r extends p{oncreate(t){super.oncreate(t),this.setupWidget()}view(){return m("div",{className:"user-submission-widget"},m("div",{className:"user-submission-header"},m("img",{className:"user-submission-icon",src:"https://i.mji.rip/2025/08/15/102ee6e187aa177ddfe02364dc82208d.png",alt:"User Submission"}),m("span",{className:"user-submission-title"},s.translator.trans("wusong8899-user-submission.forum.item-header"))),m("div",{className:"user-submission-input-container"},m("div",{className:"user-submission-input-overlay",onclick:this.handleInputClick.bind(this)}),m("div",{className:"Search-input"},m("input",{disabled:!0,className:"FormControl",type:"search",placeholder:s.translator.trans("wusong8899-user-submission.forum.item-input-placeholder")}))))}setupWidget(){const t=setInterval(()=>{const i=$(r.CONTAINER_SELECTOR);i.length>0&&(clearInterval(t),this.insertWidget(i))},r.POLL_INTERVAL)}insertWidget(t){if(!t.hasClass(r.WIDGET_CLASS)){const i=this.renderWidgetHtml();$(i).insertAfter(t),t.addClass(r.WIDGET_CLASS),this.attachEventHandlers()}}renderWidgetHtml(){return`
      <div style="display: flex;align-items: center;font-weight: bold;font-size: 14px;">
        <img style="width:22px;" src="https://i.mji.rip/2025/08/15/102ee6e187aa177ddfe02364dc82208d.png" />&nbsp;&nbsp;${s.translator.trans("wusong8899-user-submission.forum.item-header")}
      </div>
      <div style="padding-top: 10px;position:relative">
        <div class="UserSubmissionApplicationInput" style="position: absolute;height: 37px;width: 100%;z-index: 1;"></div>
        <div style="width:100%" class="Search-input">
          <input disabled style="width: 100%;font-size:12px;" class="FormControl" type="search" placeholder="${s.translator.trans("wusong8899-user-submission.forum.item-input-placeholder")}" />
        </div>
      </div>
    `}attachEventHandlers(){$(".UserSubmissionApplicationInput").on("click",this.handleInputClick.bind(this))}handleInputClick(){s.session.user?s.modal.show(u):s.modal.show(y)}};r.POLL_INTERVAL=10,r.WIDGET_CLASS="UserSubmissionApplication",r.CONTAINER_SELECTOR=".splideTagContainer";let w=r;class L extends e{}Object.assign(L.prototype,{id:e.attribute("id"),amount:e.attribute("amount"),platform:e.attribute("platform"),platform_account:e.attribute("platform_account"),user_account:e.attribute("user_account"),submission_user_id:e.attribute("submission_user_id"),review_user_id:e.attribute("review_user_id"),review_result:e.attribute("review_result"),assigned_at:e.attribute("assigned_at"),reviewed_at:e.attribute("reviewed_at"),fromUser:e.hasOne("fromUser"),reviewUser:e.hasOne("reviewUser")});class F extends p{view(){const{itemData:t}=this.attrs,i=t.amount(),d=t.id(),j=t.platform(),V=t.platform_account(),z=t.user_account(),b=t.review_result(),S=s.translator.trans(b==="approved"?"wusong8899-user-submission.lib.list-submission-accept":"wusong8899-user-submission.lib.list-submission-decline"),H=t.assigned_at(),h=t.reviewed_at();let g="userSubmissionApplicationContainer ";return h===null?g+="userSubmissionApplicationReviewing":b==="approved"?g+="userSubmissionApplicationAccepted":g+="userSubmissionApplicationDeclined",m("div",{className:g},m("div",null,m("b",null,s.translator.trans("wusong8899-user-submission.lib.list-id"),": "),d," | ",m("b",null,s.translator.trans("wusong8899-user-submission.lib.list-assignedAt"),": "),H),m("div",null,m("b",null,s.translator.trans("wusong8899-user-submission.lib.list-amount"),": "),i," | ",m("b",null,s.translator.trans("wusong8899-user-submission.lib.list-platform"),": "),j," | ",m("b",null,s.translator.trans("wusong8899-user-submission.lib.list-platformAccount"),": "),V," | ",m("b",null,s.translator.trans("wusong8899-user-submission.lib.list-userAccount"),": "),z),h&&m("div",null,m("b",null,s.translator.trans("wusong8899-user-submission.lib.list-reviewResult"),": "),b==="rejected"&&m("span",{style:"color:red"},S," | "),b==="approved"&&m("span",{style:"color:green"},S," | "),m("b",null,s.translator.trans("wusong8899-user-submission.lib.list-reviewAt"),": "),h),!h&&m("div",null,m("b",null,s.translator.trans("wusong8899-user-submission.lib.list-reviewResult"),": "),m("span",{style:"color:grey"},s.translator.trans("wusong8899-user-submission.lib.list-submission-reviewing"))),m("div",{style:"margin-top: 10px; text-align: right;"},o.component({className:"Button Button--danger Button--small",icon:"fas fa-trash",onclick:()=>this.deleteItem(t)},s.translator.trans("wusong8899-user-submission.forum.delete-submission"))))}deleteItem(t){confirm(s.translator.trans("wusong8899-user-submission.forum.confirm-delete"))&&s.request({method:"DELETE",url:s.forum.attribute("apiUrl")+"/userSubmissionList/"+t.id()}).then(()=>{window.location.reload()}).catch(()=>{alert(s.translator.trans("wusong8899-user-submission.forum.delete-failed"))})}}class T extends p{constructor(){super(...arguments),this.loading=!0,this.moreResults=!1,this.userSubmissionList=[]}oninit(t){super.oninit(t),this.loading=!0,this.moreResults=!1,this.userSubmissionList=[],this.loadResults()}view(){let t;return this.loading&&(t=k.component({size:"large"})),m("div",null,m("ul",{style:"padding:0px;list-style-type: none;"},this.userSubmissionList.map(i=>m("li",{key:i.id(),style:"margin-top:5px;background: var(--body-bg);"},F.component({itemData:i})))),!this.loading&&this.userSubmissionList.length===0&&m("div",null,m("div",{style:"font-size:1.4em;color: var(--muted-more-color);text-align: center;line-height: 100px;"},s.translator.trans("wusong8899-user-submission.lib.list-empty"))),!t&&this.hasMoreResults()&&m("div",{style:"text-align:center;padding:20px"},m(o,{className:"Button Button--primary",disabled:this.loading,loading:this.loading,onclick:()=>this.loadMore()},s.translator.trans("wusong8899-user-submission.lib.list-load-more"))),t&&m("div",{className:"UserSubmission-loadMore"},t))}hasMoreResults(){return this.moreResults}loadMore(){this.loading=!0,this.loadResults(this.userSubmissionList.length)}parseResults(t){return this.moreResults=!!t.payload?.links?.next,this.userSubmissionList.push(...t),this.loading=!1,m.redraw(),t}loadResults(t=0){return s.store.find("userSubmissionApplicationList",{page:{offset:t}}).catch(()=>[]).then(this.parseResults.bind(this))}}class U extends x{oninit(t){super.oninit(t),this.loadUser(m.route.param("username"))}content(){if(s.session.user){const t=s.session.user.id(),i=this.user.id();if(t===i)return m("div",null,T.component({params:{user:this.user}}))}return null}}function B(){s.routes["user.userSubmissionApplication"]={path:"/u/:username/userSubmissionApplication",component:U},f.extend(x.prototype,"navItems",function(n){if(s.session.user){const t=s.session.user.id(),i=this.user.id();t===i&&n.add("userSubmissionApplication",R.component({href:s.route("user.userSubmissionApplication",{username:this.user.username()}),icon:"fas fa-file-signature"},[s.translator.trans("wusong8899-user-submission.forum.application-list-title")]),10)}})}class P extends _{icon(){return null}href(){return s.route("user.userSubmissionApplication",{username:s.session.user.username()})}content(){return this.attrs.notification.subject(),s.translator.trans("wusong8899-user-submission.forum.notification-submission-result-title")}excerpt(){const t=this.attrs.notification.subject(),i=t.review_result(),d=t.id();if(i==="approved")return s.translator.trans("wusong8899-user-submission.forum.notification-submission-result-success",{id:d});if(i==="rejected")return s.translator.trans("wusong8899-user-submission.forum.notification-submission-result-failed",{id:d})}}class O extends D{oninit(t){super.oninit(t)}view(){return m("div",{className:"MoneyLeaderboardPage"},A.prototype.hero(),m("div",{className:"container"},m("div",{className:"sideNavContainer"},m("nav",{className:"IndexPage-nav sideNav"},m("ul",null,E(A.prototype.sidebarItems().toArray()))),m("div",{className:"user-submission-header"},m("img",{className:"user-submission-icon",src:"https://mutluresim.com/images/2023/03/26/ViOux.png",alt:"User Submission"})," ",s.translator.trans("wusong8899-user-submission.forum.item-header")),m("div",{className:"user-submission-input-container"},m("div",{className:"user-submission-input-overlay",onclick:()=>this.openModal()}),m("div",{className:"Search-input"},m("input",{disabled:!0,className:"FormControl",type:"search",placeholder:s.translator.trans("wusong8899-user-submission.forum.item-input-placeholder")}))))))}openModal(){s.session.user?s.modal.show(u):s.modal.show(y)}}s.initializers.add("wusong8899-user-submission",()=>{s.routes.userSubmission={path:"/userSubmission",component:O},s.store.models.userSubmissionList=L,s.notificationComponents.userSubmissionList=P,f.extend(I.prototype,"view",function(){s.current.get("routeName")==="tags"&&new w().oncreate({})}),f.extend(C.prototype,"notificationTypes",function(n){n.add("userSubmissionList",{name:"userSubmissionList",icon:"fas fa-file-signature",label:s.translator.trans("wusong8899-user-submission.forum.notification-submission-result")})}),B()})})(flarum.core.compat["forum/app"],flarum.core.compat.extend,flarum.core.compat["components/NotificationGrid"],flarum.core.compat["forum/components/HeaderPrimary"],flarum.core.compat.Component,flarum.core.compat["components/Modal"],flarum.core.compat["utils/Stream"],flarum.core.compat["components/Button"],flarum.core.compat["components/LogInModal"],flarum.core.compat.Model,flarum.core.compat["components/UserPage"],flarum.core.compat["components/LinkButton"],flarum.core.compat["components/LoadingIndicator"],flarum.core.compat["components/Notification"],flarum.core.compat["components/Page"],flarum.core.compat["components/IndexPage"],flarum.core.compat["common/helpers/listItems"]);
//# sourceMappingURL=forum.js.map

module.exports={};