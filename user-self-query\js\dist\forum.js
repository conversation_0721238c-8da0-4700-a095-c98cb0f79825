(()=>{var e={n:t=>{var o=t&&t.__esModule?()=>t.default:()=>t;return e.d(o,{a:o}),o},d:(t,o)=>{for(var r in o)e.o(o,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:o[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};(()=>{"use strict";e.r(t);const o=flarum.core.compat["forum/app"];var r=e.n(o);function n(e,t){return n=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},n(e,t)}flarum.core.compat.extend;const i=flarum.core.compat["components/Page"];var s=e.n(i);const a=flarum.core.compat["components/IndexPage"];var u=e.n(a);const l=flarum.core.compat["common/helpers/listItems"];var d=e.n(l);const p=flarum.core.compat["components/Button"];var c=e.n(p);const h=flarum.core.compat["utils/Stream"];var y=e.n(h),f=function(e){var t,o;function r(){return e.apply(this,arguments)||this}o=e,(t=r).prototype=Object.create(o.prototype),t.prototype.constructor=t,n(t,o);var i=r.prototype;return i.oninit=function(t){e.prototype.oninit.call(this,t),this.bodyClass="App--index",this.phone=y()(""),this.paused=!1,this.maxCount=1986,this.counter=0,this.thread={};var o=(app.forum.attribute("userSelfQueryUrl")||"").split(",");this.urlList=this.shuffle(o)},i.view=function(){var e=this;return m("div",{className:"IndexPage"},u().prototype.hero(),m("div",{className:"container"},m("div",{className:"sideNavContainer"},m("nav",{className:"IndexPage-nav sideNav"},m("ul",null,d()(u().prototype.sidebarItems().toArray()))),m("div",{style:"padding: 25px 0px 0px 0px;width: 100%;"},m("div",{style:"padding-bottom: 20px;position:relative"},m("div",{style:"display: inline-block;width: 80%;"},m("input",{type:"number",placeholder:"请输入查询的手机号",className:"FormControl",bidi:this.phone})),m("div",null,c().component({id:"userSelfQueryStart",style:"width: 66px;position: absolute;top: 0px;right: 70px;font-weight: normal !important;background: rgb(230, 96, 27) !important;",className:"Button Button--primary",onclick:function(){e.start()}},"开始"),c().component({id:"userSelfQueryPause",style:"display:none;width: 66px;display:none;position: absolute;top: 0px;right: 70px;font-weight: normal !important;",className:"Button",onclick:function(){e.pause()}},"暂停"),c().component({style:"display:none;width: 66px;position: absolute;top: 0px;right: 70px;font-weight: normal !important;background: rgb(230, 96, 27) !important;",id:"userSelfQueryResume",className:"Button Button--primary",onclick:function(){e.resume()}},"继续"),c().component({style:"width: 66px;position: absolute;top: 0px;right: 0px;font-weight: normal !important;",className:"Button Button--primary",onclick:function(){e.copyLog()}},"复制"))),m("div",null,m("div",{id:"userSelfQueryLogs",style:"border:1px solid rgba(255,255,255,.2);height: 400px;overflow-y: auto;text-align: left;padding: 15px;border-radius: 4px;"})),m("div",{style:"text-align:center;padding-top: 10px;"},m("div",null,"当前0.5测试版存在BUG 正在更新"),m("div",null,"开发0.8更新后使用反馈投稿社区留言致电"))))))},i.clearLog=function(){$("#userSelfQueryLogs").empty()},i.copyLog=function(){var e=document.createRange();e.selectNode(document.getElementById("userSelfQueryLogs")),window.getSelection().removeAllRanges(),window.getSelection().addRange(e),document.execCommand("copy"),window.getSelection().removeAllRanges(),alert("已复制日志")},i.pause=function(){this.paused=!0,$("#userSelfQueryLogs").append("<div>已暂停</div><div><hr style='border-top:1px solid #313952' />"),$("#userSelfQueryPause").hide(),$("#userSelfQueryResume").show()},i.resume=function(){this.paused=!1,$("#userSelfQueryLogs").append("<div>已恢复查询</div><div><hr style='border-top:1px solid #313952' />"),$("#userSelfQueryPause").show(),$("#userSelfQueryResume").hide()},i.start=function(){var e=this.phone();if(!1===this.checkMobile(e))alert("请输入正确的手机号码");else{this.clearLog();var t=Object.keys(this.thread).length;this.runQuery(t,500),$("#userSelfQueryPause").show(),$("#userSelfQueryStart").hide()}},i.runQuery=function(e,t){var o=this;void 0===this.thread[e]&&(this.thread[e]=0),setTimeout((function(){if(!0===o.paused)o.runQuery(e);else{var t=o.thread[e],r=o.randomInt(1,4);$("#userSelfQueryLogs").append(o.getResult(t,r)),$("#userSelfQueryLogs").scrollTop(99999999),t===o.maxCount?($("#userSelfQueryStart").show(),$("#userSelfQueryPause").hide(),$("#userSelfQueryResume").hide()):(o.thread[e]++,o.runQuery(e))}}),void 0!==t?t:o.randomInt(3e3,4e3))},i.getResult=function(e,t){var o="<div>"+(new Date).toLocaleString("sv-SE")+" 正在查询"+(e+1)+"个</div>";return e===this.maxCount?o="<div>查询完毕感谢您的使用</div><div>反馈相关事宜投稿站点请添加POP群组</div>":(2===t&&0===this.urlList.length&&(t=1),1===t?o+="<div style='color:white'>您的手机号暂未收到邀请，谢谢</div>":2===t?(o+="<a target='_blank' href='"+this.urlList[0]+"'><div style='color:green'>"+this.urlList[0]+"</div>",o+="<div style='color:green'>您是尊贵特邀会员，谢谢</div>",o+="<div style='color:green'>请尽快前往特邀网址，避免过期</div></a>",this.urlList.shift()):3===t?o+="<div style='color:red'>您的手机号并非特邀会员，请勿重复提交</div>":4===t&&(o+="<div style='color:white'>您的手机号已提交申请彩金</div>",o+="<div style='color:white'>信息请勿重复提交</div>")),o+="<div><a target='_blank' href='https://老哥.com'>https://老哥.com 免费供应技术支持</a></div>","<div>"+(o+="<div><hr style='border-top:1px solid #313952' /></div>")+"</div>"},i.randomInt=function(e,t){return Math.floor(Math.random()*(t-e+1)+e)},i.shuffle=function(e){for(var t,o=e.length;0!=o;){t=Math.floor(Math.random()*o),o--;var r=[e[t],e[o]];e[o]=r[0],e[t]=r[1]}return e},i.checkMobile=function(e){return!!/^1[3,4,5,6,7,8,9][0-9]{9}$/.test(e)},r}(s());r().initializers.add("user-self-query",(function(){r().routes.userSelfQuery={path:"/userSelfQuery",component:f}}))})(),module.exports=t})();
//# sourceMappingURL=forum.js.map