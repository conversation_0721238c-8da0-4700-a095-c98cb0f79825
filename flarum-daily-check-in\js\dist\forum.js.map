{"version": 3, "file": "forum.js", "mappings": "MACA,IAAIA,EAAsB,CCA1BA,EAAyBC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,WAC7B,IAAOF,EAAiB,QACxB,IAAM,EAEP,OADAD,EAAoBI,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GCLRF,EAAwB,CAACM,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXP,EAAoBS,EAAEF,EAAYC,KAASR,EAAoBS,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3ER,EAAwB,CAACc,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFf,EAAyBM,IACH,oBAAXa,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeL,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeL,EAAS,aAAc,CAAEe,OAAO,M,+BCLvD,MAAM,EAA+BC,OAAOC,KAAKC,OAAe,OCA1D,EAA+BF,OAAOC,KAAKC,OAAO,8B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,qB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,2B,aCAzC,SAASC,EAAgBhB,EAAGiB,GAMzC,OALAD,EAAkBf,OAAOiB,gBAAkB,SAAyBlB,EAAGiB,GAErE,OADAjB,EAAEmB,UAAYF,EACPjB,GAGFgB,EAAgBhB,EAAGiB,GCLb,SAASG,EAAeC,EAAUC,GAC/CD,EAASd,UAAYN,OAAOsB,OAAOD,EAAWf,WAC9Cc,EAASd,UAAUiB,YAAcH,EACjCH,EAAeG,EAAUC,GCJ3B,MAAM,EAA+BT,OAAOC,KAAKC,OAAO,oB,aCAnBF,OAAOC,KAAKC,OAAO,gB,ICInCU,EAAAA,SAAAA,G,oFACnBC,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,I,EAGfC,UAAA,WACE,MAAO,mC,EAGTC,MAAA,WACE,OAAQ,SAAKD,UAAU,uCAAuCE,IAAIC,WAAWC,MAAM,0C,EAGrFC,QAAA,WAEE,IAAMC,EAAyBJ,IAAIK,QAAQC,KAAKC,UAAU,0BACpDC,EAAgCR,IAAIS,MAAMF,UAAU,iCACpDG,EAAsCV,IAAIS,MAAMF,UAAU,uCAC1DI,EAAyBX,IAAIS,MAAMF,UAAU,0BAI/CK,EAAa,GACbC,EAAuB,8BACvBC,EAAsB,8BAY1B,MAVmC,KAAhCN,IACDK,EAAuB,mCAGA,SAXsDE,IAAnDf,IAAIS,MAAMF,UAAU,+BAWuB,KAAtCG,IAE/BE,GADYZ,IAAIS,MAAMF,UAAU,8BAAgC,WACzCS,QAAQ,UAAWL,GAC1CG,EAAsB,iCAItB,SAAKhB,UAAU,cACb,SAAKA,UAAWe,GAAuBL,EAA8BQ,QAAQ,SAAUZ,IACvF,SAAKN,UAAWgB,GAAsBJ,EAAoCM,QAAQ,WAAYJ,M,EAvCjFjB,CAA2BsB,KCA3BtB,EAAAA,SAAAA,G,oFACnBC,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,I,EAGfC,UAAA,WACE,MAAO,mC,EAGTC,MAAA,WACE,OAAQ,SAAKD,UAAU,sCAAsCE,IAAIC,WAAWC,MAAM,yC,EAGpFC,QAAA,WAEE,OACE,SAAKL,UAAU,cACb,SAAKA,UAAU,kCAAkCE,IAAIC,WAAWC,MAAM,0C,EAjBzDP,CAA2BsB,KCGhDjB,IAAIkB,aAAaC,IAAI,iBAAiB,YACpCC,EAAAA,EAAAA,QAAOC,IAAAA,UAAqB,gBAAgB,SAASC,GACnD,GAAsB,OAAnBtB,IAAIK,QAAQC,OAAqD,IAAtCN,IAAIS,MAAMF,UAAU,gBAAuB,CACvE,IASIgB,EATEC,EAAYxB,IAAIyB,QAAQnD,IAAI,aAE5B8B,EAAyBJ,IAAIK,QAAQC,KAAKC,UAAU,0BACpDmB,EAAa1B,IAAIK,QAAQC,KAAKC,UAAU,cACxCoB,EAAuB3B,IAAIK,QAAQC,KAAKC,UAAU,wBAElDqB,GADa5B,IAAIK,QAAQC,KAAKC,UAAU,cACRP,IAAIS,MAAMF,UAAU,kCACtDsB,EAA8B7B,IAAIK,QAAQC,KAAKC,UAAU,+BAGzDuB,GAFkB9B,IAAIK,QAAQC,KAAKC,UAAU,mBAElC,iBAQf,GANe,QAAZiB,IAC4D,IAA1DK,EAA4BE,QAAQ,qBACnCD,EAAW,oBAID,IAAbJ,EAAkB,CACnBH,EAAoBvB,IAAIC,WAAWC,MAAM,gCACzCoB,EAAMH,IAAIW,EAAUE,IAAAA,UAAiB,CACnCC,KAAM,kBACNnC,UAAW,+BACXoC,cAAe,qBACfC,GAAG,gBACHC,QAAS,WACPpC,IAAIK,QAAQC,KAAK+B,KAAK,CAACX,YAAW,EAAMtB,wBAA8C,IAAvBuB,EAA4BvB,EAAuB,EAAE,IAAIkC,MAAK,WAC3H,IAAM9B,EAAgCR,IAAIS,MAAMF,UAAU,iCACpDG,EAAsCV,IAAIS,MAAMF,UAAU,uCAC1DgC,EAAoBvC,IAAIK,QAAQC,KAAKC,UAAU,cAErD,GAAmC,KAAhCC,GAA4E,KAAtCE,EACvC,GAAmC,IAAhCkB,EACD,IAAuB,IAApBW,EAA0B,CAC3B,IAAM5B,EAAyBX,IAAIS,MAAMF,UAAU,0BAC7CiC,OAAyEzB,IAAnDf,IAAIS,MAAMF,UAAU,6BAG5CK,EAAa,GAEjB,GAAmC,KAAhCJ,EACD,KAAMiC,EAAqBjC,EAA8BQ,QAAQ,SAAUhB,IAAIK,QAAQC,KAAKC,UAAU,2BAC1EP,IAAI0C,OAAOC,KAAKC,IAAO,CAAEC,KAAM,WAAaJ,GAG1E,IAAyB,IAAtBD,GAAoE,KAAtC9B,EAAyC,CAExEE,GADYZ,IAAIS,MAAMF,UAAU,8BAAgC,WACzCS,QAAQ,UAAWL,GAE1C,IAAMmC,EAA2BpC,EAAoCM,QAAQ,WAAYJ,GACvDZ,IAAI0C,OAAOC,KAAKC,IAAO,CAAEC,KAAM,WAAaC,SAGrD9C,IAAI0C,OAAOC,KAAKC,IAAO,CAAEC,KAAM,SAAW7C,IAAIC,WAAWC,MAAM,uCAAuC,IAAIF,IAAIC,WAAWC,MAAM,6CAErH,IAAhC0B,KACgB,IAApBW,EACDvC,IAAI+C,MAAMJ,KAAKK,GAEfhD,IAAI+C,MAAMJ,KAAKM,SAMxB1B,GAAmB,IAEtB,IAAM2B,EAAmBlD,IAAIS,MAAMF,UAAU,oBACvC4C,EAAwBnD,IAAIS,MAAMF,UAAU,yBAE5B,IAAnB2C,GACDE,YAAW,WACTC,EAAE,kBAAkBC,UACpBH,QAGJ5B,EAAoBnB,GAAwB,EAAEJ,IAAIC,WAAWC,MAAM,qCAAsC,CAACqD,MAAOnD,IAAyBJ,IAAIC,WAAWC,MAAM,sCAAuC,CAACqD,MAAOnD,IAC9MkB,EAAMH,IAAIW,EAAUE,IAAAA,UAAiB,CACnCC,KAAM,wBACNnC,UAAW,8BACXoC,cAAe,qBACfsB,UAAU,GACTjC,GAAmB,Y", "sources": ["webpack://@wusong8899/flarum-daily-check-in/webpack/bootstrap", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/compat get default export", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/define property getters", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/hasOwnProperty shorthand", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/make namespace object", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['extend']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['forum/components/IndexPage']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Button']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/components/Alert']\"", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Modal']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['utils/Stream']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/checkInSuccessModal.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/checkInFailedModal.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/index.js"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['extend'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['forum/components/IndexPage'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Button'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/Alert'];", "export default function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}", "import setPrototypeOf from \"./setPrototypeOf.js\";\nexport default function _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  setPrototypeOf(subClass, superClass);\n}", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Modal'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['utils/Stream'];", "import Modal from 'flarum/components/Modal';\nimport Button from 'flarum/components/Button';\nimport Stream from 'flarum/utils/Stream';\n\nexport default class checkInResultModal extends Modal {\n  oninit(vnode) {\n    super.oninit(vnode);\n  }\n\n  className() {\n    return 'checkInResultModal Modal--small';\n  }\n\n  title() {\n    return (<div className=\"checkInResultModal successTitleText\">{app.translator.trans('wusong8899-checkin.forum.check-in-success')}</div>);\n  }\n\n  content() {\n    //\n    const totalContinuousCheckIn = app.session.user.attribute(\"totalContinuousCheckIn\");\n    const forumCheckinSuccessPromptText = app.forum.attribute(\"forumCheckinSuccessPromptText\");\n    const forumCheckinSuccessPromptRewardText = app.forum.attribute(\"forumCheckinSuccessPromptRewardText\");\n    const forumCheckinRewarMoney = app.forum.attribute(\"forumCheckinRewarMoney\");\n    const moneyExtensionExist = app.forum.attribute('antoinefr-money.moneyname')!==undefined;\n\n    let moneyName = \"\";\n    let rewardText = \"\";\n    let successTextClassName = \"checkInResultModal hideText\";\n    let rewardTextClassName = \"checkInResultModal hideText\";\n\n    if(forumCheckinSuccessPromptText!==\"\"){\n      successTextClassName = \"checkInResultModal successText\";\n    }\n\n    if(moneyExtensionExist===true && forumCheckinSuccessPromptRewardText!==\"\"){\n      moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n      rewardText = moneyName.replace('[money]', forumCheckinRewarMoney);\n      rewardTextClassName = \"checkInResultModal rewardText\";\n    }\n\n    return (\n      <div className=\"Modal-body\">\n        <div className={successTextClassName}>{forumCheckinSuccessPromptText.replace('[days]', totalContinuousCheckIn)}</div>\n        <div className={rewardTextClassName}>{forumCheckinSuccessPromptRewardText.replace('[reward]', rewardText)}</div>\n      </div>\n    );\n  }\n}\n", "import Modal from 'flarum/components/Modal';\nimport Button from 'flarum/components/Button';\nimport Stream from 'flarum/utils/Stream';\n\nexport default class checkInResultModal extends Modal {\n  oninit(vnode) {\n    super.oninit(vnode);\n  }\n\n  className() {\n    return 'checkInResultModal Modal--small';\n  }\n\n  title() {\n    return (<div className=\"checkInResultModal failedTitleText\">{app.translator.trans('wusong8899-checkin.forum.check-in-failed')}</div>);\n  }\n\n  content() {\n    //\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"checkInResultModal successText\">{app.translator.trans('wusong8899-checkin.forum.try-again-later')}</div>\n      </div>\n    );\n  }\n}\n", "import { extend } from 'flarum/extend';\r\nimport IndexPage from 'flarum/forum/components/IndexPage';\r\nimport Button from 'flarum/components/Button';\r\nimport Alert from 'flarum/common/components/Alert';\r\nimport checkInSuccessModal from './components/checkInSuccessModal';\r\nimport checkInFailedModal from './components/checkInFailedModal';\r\n\r\napp.initializers.add('wusong8899-checkin', () => {\r\n  extend(IndexPage.prototype, 'sidebarItems', function(items) {\r\n    if(app.session.user!==null && app.forum.attribute('allowCheckIn')===true){\r\n      const routeName = app.current.get('routeName');\r\n\r\n      const totalContinuousCheckIn = app.session.user.attribute(\"totalContinuousCheckIn\");\r\n      const canCheckin = app.session.user.attribute(\"canCheckin\");\r\n      const canCheckinContinuous = app.session.user.attribute(\"canCheckinContinuous\");\r\n      const serverData = app.session.user.attribute(\"serverDate\");\r\n      const forumCheckinSuccessPromptType = app.forum.attribute(\"forumCheckinSuccessPromptType\");\r\n      let checkInCompatibleExtensions = app.session.user.attribute(\"checkInCompatibleExtensions\");\r\n      let lastCheckinTime = app.session.user.attribute(\"lastCheckinTime\");\r\n      let checkinButtonText;\r\n      let itemName = \"forum-checkin\";\r\n\r\n      if(routeName===\"tag\"){\r\n        if(checkInCompatibleExtensions.indexOf(\"fof-follow-tags\")!==-1){\r\n            itemName = \"forum-checkin-1\";\r\n        }\r\n      }\r\n\r\n      if(canCheckin===true){\r\n        checkinButtonText = app.translator.trans('wusong8899-checkin.forum.check-in');\r\n        items.add(itemName, Button.component({\r\n          icon: 'fas fa-calendar',\r\n          className: 'Button CheckInButton--yellow',\r\n          itemClassName: 'App-primaryControl',\r\n          id:\"checkInButton\",\r\n          onclick: () => {\r\n            app.session.user.save({canCheckin:false,totalContinuousCheckIn:canCheckinContinuous===true?totalContinuousCheckIn+1:1}).then(() => {\r\n              const forumCheckinSuccessPromptText = app.forum.attribute(\"forumCheckinSuccessPromptText\");\r\n              const forumCheckinSuccessPromptRewardText = app.forum.attribute(\"forumCheckinSuccessPromptRewardText\");\r\n              const canCheckinRecheck = app.session.user.attribute(\"canCheckin\");\r\n\r\n              if(forumCheckinSuccessPromptText!==\"\" || forumCheckinSuccessPromptRewardText!==\"\"){\r\n                if(forumCheckinSuccessPromptType===1){\r\n                  if(canCheckinRecheck===false){\r\n                    const forumCheckinRewarMoney = app.forum.attribute(\"forumCheckinRewarMoney\");\r\n                    const moneyExtensionExist = app.forum.attribute('antoinefr-money.moneyname')!==undefined;\r\n\r\n                    let moneyName = \"\";\r\n                    let rewardText = \"\";\r\n\r\n                    if(forumCheckinSuccessPromptText!==\"\"){\r\n                      const checkInSuccessText = forumCheckinSuccessPromptText.replace('[days]', app.session.user.attribute(\"totalContinuousCheckIn\"));\r\n                      const successTextAlertKey = app.alerts.show(Alert, { type: 'success' }, checkInSuccessText);\r\n                    }\r\n\r\n                    if(moneyExtensionExist===true && forumCheckinSuccessPromptRewardText!==\"\"){\r\n                      moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\r\n                      rewardText = moneyName.replace('[money]', forumCheckinRewarMoney);\r\n\r\n                      const checkInSuccessRewardText = forumCheckinSuccessPromptRewardText.replace('[reward]', rewardText);\r\n                      const successRewardTextAlertKey = app.alerts.show(Alert, { type: 'success' }, checkInSuccessRewardText);\r\n                    }\r\n                  }else{\r\n                    const failedTextAlertKey = app.alerts.show(Alert, { type: 'error' }, app.translator.trans('wusong8899-checkin.forum.check-in-failed')+\" \"+app.translator.trans('wusong8899-checkin.forum.try-again-later'));\r\n                  }\r\n                }else if(forumCheckinSuccessPromptType===2){\r\n                  if(canCheckinRecheck===false){\r\n                    app.modal.show(checkInSuccessModal);\r\n                  }else{\r\n                    app.modal.show(checkInFailedModal);\r\n                  }\r\n                }\r\n              }\r\n            });\r\n          }\r\n        }, checkinButtonText),50);\r\n\r\n        const forumAutoCheckIn = app.forum.attribute(\"forumAutoCheckin\");\r\n        const forumAutoCheckInDelay = app.forum.attribute(\"forumAutoCheckinDelay\");\r\n\r\n        if(forumAutoCheckIn===1){\r\n          setTimeout(function(){\r\n            $(\"#checkInButton\").click();\r\n          },forumAutoCheckInDelay);\r\n        }\r\n      }else{\r\n        checkinButtonText = totalContinuousCheckIn<=1?app.translator.trans('wusong8899-checkin.forum.checked-in-day', {count: totalContinuousCheckIn}):app.translator.trans('wusong8899-checkin.forum.checked-in-days', {count: totalContinuousCheckIn});\r\n        items.add(itemName, Button.component({\r\n          icon: 'fas fa-calendar-check',\r\n          className: 'Button CheckInButton--green',\r\n          itemClassName: 'App-primaryControl',\r\n          disabled: true\r\n        }, checkinButtonText),50);\r\n      }\r\n    }\r\n  });\r\n});"], "names": ["__webpack_require__", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "flarum", "core", "compat", "_setPrototypeOf", "p", "setPrototypeOf", "__proto__", "_inherits<PERSON><PERSON>e", "subClass", "superClass", "create", "constructor", "checkInResultModal", "oninit", "vnode", "className", "title", "app", "translator", "trans", "content", "totalContinuousCheckIn", "session", "user", "attribute", "forumCheckinSuccessPromptText", "forum", "forumCheckinSuccessPromptRewardText", "forumCheckinRewarMoney", "rewardText", "successTextClassName", "rewardTextClassName", "undefined", "replace", "Modal", "initializers", "add", "extend", "IndexPage", "items", "checkinButtonText", "routeName", "current", "can<PERSON><PERSON><PERSON>n", "canCheckinContinuous", "forumCheckinSuccessPromptType", "checkInCompatibleExtensions", "itemName", "indexOf", "<PERSON><PERSON>", "icon", "itemClassName", "id", "onclick", "save", "then", "canCheckinRecheck", "moneyExtensionExist", "checkInSuccessText", "alerts", "show", "<PERSON><PERSON>", "type", "checkInSuccessRewardText", "modal", "checkInSuccessModal", "checkInFailedModal", "forumAutoCheckIn", "forumAutoCheckInDelay", "setTimeout", "$", "click", "count", "disabled"], "sourceRoot": ""}