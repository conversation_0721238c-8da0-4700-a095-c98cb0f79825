export type Environment = 'development' | 'staging' | 'production' | 'test';

export interface AppConfig {
  extensionId: string;
  translationPrefix: string;
}

export interface SliderDomConfig {
  containerId: string; // e.g., 'splideAdContainer'
  splideClass: string; // e.g., 'adSplide'
}

export interface SplideOptionsConfig {
  gap: string;
  type: 'slide' | 'loop' | 'fade';
  focus: 'center' | number;
  perPage: number;
  pagination: boolean;
  arrows: boolean;
}

export interface SliderConfig {
  maxSlides: number;
  defaultTransitionTime: number; // ms
  checkTime: number; // ms, small polling interval
  dataCheckInterval: number; // ms, UI/data polling
  dom: SliderDomConfig;
  splide: SplideOptionsConfig;
}

export interface UIConfig {
  headerIconId: string;
  headerIconUrl: string;
}

export interface RootConfig {
  env: Environment;
  app: AppConfig;
  slider: SliderConfig;
  ui: UIConfig;
}

// Additional types for better type safety
export interface SlideData {
  slideNumber: number;
  image: string;
  link: string;
}

export interface ErrorLogEntry {
  timestamp: Date;
  error: Error;
  context: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface MobileConfig {
  spaceBetween: number;
  slidesPerView: number;
}

export type EventType = 'touchend' | 'click';

export type NotificationType = 'error' | 'warning' | 'info';

// Flarum-specific types
export interface FlarumVnode {
  dom?: HTMLElement;
  [key: string]: unknown;
}

export type FlarumComponentAttrs = Record<string, unknown>;

export interface FlarumApp {
  forum: {
    attribute: (key: string) => unknown;
  };
  session: {
    user?: unknown;
  };
  translator: {
    trans: (key: string, params?: Record<string, unknown>) => string;
  };
}

// DOM utility types
export interface DOMElementOptions {
  className?: string;
  id?: string;
  [key: string]: unknown;
}

export interface StylesObject {
  [property: string]: string | number;
}

// Splide-related types
export interface SplideInstance {
  destroy: () => void;
  mount: () => void;
  [key: string]: unknown;
}

// Enhanced Splide configuration types
export interface SplideBreakpointConfig {
  perPage: number;
  gap: string;
}

export type SplideBreakpoints = Record<number, SplideBreakpointConfig>;

export interface SplideAutoplayConfig {
  interval: number;
  pauseOnHover: boolean;
}

export interface SplideFullConfig {
  type: 'slide' | 'loop' | 'fade';
  autoplay: boolean | SplideAutoplayConfig;
  gap: string;
  focus: 'center' | number;
  perPage: number;
  breakpoints: SplideBreakpoints;
  pagination: boolean;
  arrows: boolean;
  speed: number;
}

// Slide data types
export interface SlideDataRaw {
  imageSrc: string;
  imageLink: string;
  slideIndex: number;
}

export interface ProcessedSlideData {
  imageSrc: string;
  imageLink: string;
  slideIndex: number;
  isValid: boolean;
}

// Configuration calculation types
export interface ResponsiveConfig {
  mobile: SplideBreakpointConfig;
  tablet: SplideBreakpointConfig;
  desktop: SplideBreakpointConfig;
}

export interface ConfigCalculationResult {
  enableLoop: boolean;
  responsiveConfig: ResponsiveConfig;
  finalConfig: SplideFullConfig;
}

// Module interfaces
export interface ISplideConfigManager {
  calculateConfiguration(slideCount: number, transitionTime: number): ConfigCalculationResult;
  validateConfiguration(config: SplideFullConfig): ValidationResult;
}

export interface ISplideeDOMBuilder {
  createContainer(): HTMLElement;
  createSplideElement(container: HTMLElement): HTMLElement;
  createSplideTrack(splide: HTMLElement): HTMLElement;
  createSlide(imageSrc: string, imageLink: string): HTMLElement;
  createPagination(splide: HTMLElement): void;
  createNavigation(splide: HTMLElement): void;
  appendToDOM(container: HTMLElement): void;
  cleanup(): void;
}

export interface ISlideDataManager {
  fetchSlideData(): ProcessedSlideData[];
  validateSlideData(data: SlideDataRaw[]): ValidationResult;
  getTransitionTime(): number;
}

export interface ISplideLifecycleManager {
  initialize(config: SplideFullConfig): SplideInstance | undefined;
  destroy(): void;
  isInitialized(): boolean;
  getInstance(): SplideInstance | undefined;
}

// Admin types
export interface ExtensionData {
  registerSetting: (config: SettingConfig | (() => unknown)) => void;
}

export interface SettingConfig {
  setting: string;
  type: string;
  label: string;
  help?: string;
}

export interface SlideDataInternal {
  id: number;
  link: string;
  image: string;
}

// Component-specific types
export interface DynamicSlideSettingsComponentAttrs extends FlarumComponentAttrs {
  extensionId: string;
  maxSlides?: number;
}
