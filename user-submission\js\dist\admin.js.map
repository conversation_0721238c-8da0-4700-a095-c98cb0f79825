{"version": 3, "file": "admin.js", "sources": ["../src/admin/components/UserSubmissionReviewModal.tsx", "../src/admin/components/UserSubmissionListItem.tsx", "../src/admin/components/SettingsPage.tsx", "../src/forum/model/UserSubmission.ts", "../src/admin/index.ts"], "sourcesContent": ["import app from 'flarum/admin/app';\r\nimport Modal from 'flarum/components/Modal';\r\nimport Button from 'flarum/components/Button';\r\nimport { UserSubmissionData } from '../../types';\r\n\r\ninterface UserSubmissionReviewModalAttrs {\r\n  itemData: UserSubmissionData;\r\n}\r\n\r\nexport default class UserSubmissionReviewModal extends Modal<UserSubmissionReviewModalAttrs> {\r\n  static isDismissibleViaBackdropClick = false;\r\n  static isDismissibleViaCloseButton = true;\r\n\r\n  private itemData!: UserSubmissionData;\r\n  private loading: boolean = false;\r\n\r\n  oninit(vnode: any) {\r\n    super.oninit(vnode);\r\n    this.itemData = this.attrs.itemData;\r\n    this.loading = false;\r\n  }\r\n\r\n  className() {\r\n    return 'Modal--small';\r\n  }\r\n\r\n  title() {\r\n    return app.translator.trans('wusong8899-user-submission.admin.submission-review');\r\n  }\r\n\r\n  content() {\r\n    //\r\n    return (\r\n      <div className=\"Modal-body\">\r\n        <div className=\"Form-group\" style=\"text-align: center;\">\r\n          {Button.component(\r\n            {\r\n              style: 'min-width:66px;',\r\n              className: 'Button Button--primary',\r\n              disabled: this.loading,\r\n              onclick: (e: Event) => {\r\n                this.reviewConfirm(e, 'approved');\r\n              }\r\n            },\r\n            app.translator.trans('wusong8899-user-submission.lib.accept')\r\n          )}&nbsp;\r\n          {Button.component(\r\n            {\r\n              style: 'min-width:66px;',\r\n              className: 'Button Button--danger',\r\n              disabled: this.loading,\r\n              onclick: (e: Event) => {\r\n                this.reviewConfirm(e, 'rejected');\r\n              }\r\n            },\r\n            app.translator.trans('wusong8899-user-submission.lib.decline')\r\n          )}&nbsp;\r\n          {Button.component(\r\n            {\r\n              style: 'min-width:66px;',\r\n              className: 'Button',\r\n              disabled: this.loading,\r\n              onclick: () => {\r\n                this.hide();\r\n              }\r\n            },\r\n            app.translator.trans('wusong8899-user-submission.lib.cancel')\r\n          )}\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  private reviewConfirm(e: Event, value: 'approved' | 'rejected'): void {\r\n    e.preventDefault();\r\n\r\n    this.loading = true;\r\n    this.itemData.save({\r\n      reviewResult: value,\r\n    })\r\n    .then(() => {\r\n      this.hide();\r\n      this.loading = false;\r\n    })\r\n    .catch(() => {\r\n      this.loading = false;\r\n    });\r\n  }\r\n}\r\n", "import app from 'flarum/admin/app';\r\nimport Component, { ComponentAttrs } from \"flarum/Component\";\r\nimport Button from 'flarum/components/Button';\r\nimport UserSubmissionReviewModal from './UserSubmissionReviewModal';\r\nimport username from \"flarum/helpers/username\";\r\nimport { UserSubmissionData } from '../../types';\r\n\r\ninterface UserSubmissionListItemAttrs extends ComponentAttrs {\r\n  itemData: UserSubmissionData;\r\n}\r\n\r\nexport default class UserSubmissionListItem extends Component<UserSubmissionListItemAttrs> {\r\n\r\n  view() {\r\n    const { itemData } = this.attrs;\r\n\r\n    const amount = itemData.amount();\r\n    const platform = itemData.platform();\r\n    const platformAccount = itemData.platform_account();\r\n    const userAccount = itemData.user_account();\r\n    const fromUser = itemData.fromUser();\r\n    const reviewResult = itemData.review_result();\r\n    const reviewResultText = app.translator.trans(\r\n      reviewResult === 'approved'\r\n        ? 'wusong8899-user-submission.lib.list-submission-accept'\r\n        : 'wusong8899-user-submission.lib.list-submission-decline'\r\n    );\r\n    const assignedAt = itemData.assigned_at();\r\n    const reviewedAt = itemData.reviewed_at();\r\n\r\n    // const bidText = moneyName.replace('[money]', bidValue);\r\n\r\n    return (\r\n      <div className=\"biddingRankSettingContainer\">\r\n        <div style=\"float:right\">\r\n          <div style=\"display: flex; gap: 5px; align-items: center;\">\r\n            {reviewedAt && (\r\n              <div>\r\n                {Button.component({\r\n                  style: \"font-weight:bold;\",\r\n                  disabled: true,\r\n                  className: 'Button',\r\n                },\r\n                app.translator.trans('wusong8899-user-submission.admin.list-reviewed')\r\n                )}\r\n              </div>\r\n            )}\r\n            {!reviewedAt && (\r\n              <div>\r\n                {Button.component({\r\n                  style: \"font-weight:bold;\",\r\n                  className: 'Button Button--primary',\r\n                  onclick: () => {\r\n                    this.reviewItem(itemData)\r\n                  }\r\n                },\r\n                app.translator.trans('wusong8899-user-submission.admin.list-review')\r\n                )}\r\n              </div>\r\n            )}\r\n            <div>\r\n              {Button.component({\r\n                className: 'Button Button--danger',\r\n                icon: 'fas fa-trash',\r\n                title: app.translator.trans('wusong8899-user-submission.admin.delete-submission'),\r\n                onclick: () => {\r\n                  this.deleteItem(itemData)\r\n                }\r\n              })}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div>\r\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-username')}: </b>\r\n          {username(fromUser)}&nbsp;|&nbsp;\r\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-amount')}: </b>\r\n          {amount}&nbsp;|&nbsp;\r\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-assignedAt')}: </b>\r\n          {assignedAt}\r\n        </div>\r\n        <div>\r\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-platform')}: </b>\r\n          {platform}&nbsp;|&nbsp;\r\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-platformAccount')}: </b>\r\n          {platformAccount}&nbsp;|&nbsp;\r\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-userAccount')}: </b>\r\n          {userAccount}\r\n        </div>\r\n        {reviewedAt && (\r\n          <div>\r\n            <b>{app.translator.trans('wusong8899-user-submission.lib.list-reviewResult')}: </b>\r\n            {reviewResultText}&nbsp;|&nbsp;\r\n            <b>{app.translator.trans('wusong8899-user-submission.lib.list-reviewAt')}: </b>\r\n            {reviewedAt}\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  private reviewItem(itemData: UserSubmissionData): void {\r\n    app.modal.show(UserSubmissionReviewModal, { itemData });\r\n  }\r\n\r\n  private deleteItem(itemData: UserSubmissionData): void {\r\n    if (confirm(app.translator.trans('wusong8899-user-submission.admin.confirm-delete'))) {\r\n      app.request({\r\n        method: 'DELETE',\r\n        url: app.forum.attribute('apiUrl') + '/userSubmissionList/' + itemData.id(),\r\n      })\r\n        .then(() => {\r\n          // Refresh the page or remove the item from the list\r\n          window.location.reload();\r\n        })\r\n        .catch(() => {\r\n          alert(app.translator.trans('wusong8899-user-submission.admin.delete-failed'));\r\n        });\r\n    }\r\n  }\r\n}\r\n", "import app from 'flarum/admin/app';\r\nimport ExtensionPage from 'flarum/components/ExtensionPage';\r\nimport LoadingIndicator from 'flarum/components/LoadingIndicator';\r\nimport Button from 'flarum/components/Button';\r\nimport UserSubmissionListItem from './UserSubmissionListItem';\r\nimport { UserSubmissionData } from '../../types';\r\n\r\nexport default class UserSubmissionSettingsPage extends ExtensionPage {\r\n  private loading: boolean = true;\r\n  private moreResults: boolean = false;\r\n  private userSubmissionList: UserSubmissionData[] = [];\r\n\r\n  oninit(attrs: any) {\r\n    super.oninit(attrs);\r\n    this.loading = true;\r\n    this.moreResults = false;\r\n    this.userSubmissionList = [];\r\n    this.loadResults();\r\n  }\r\n\r\n  content() {\r\n    let loading: any;\r\n\r\n    if (this.loading) {\r\n      loading = LoadingIndicator.component({ size: \"large\" });\r\n    }\r\n\r\n    return (\r\n      <div className=\"ExtensionPage-settings FlarumBadgesPage\">\r\n        <div className=\"container\">\r\n\r\n          <ul style=\"padding:0px;list-style-type: none;\">\r\n            {this.userSubmissionList.map((itemData) => {\r\n              return (\r\n                <li itemID={itemData.id()} style=\"margin-top:5px;background: var(--body-bg);\">\r\n                  {UserSubmissionListItem.component({ itemData })}\r\n                </li>\r\n              );\r\n            })}\r\n          </ul>\r\n\r\n          {!this.loading && this.userSubmissionList.length === 0 && (\r\n            <div>\r\n              <div style=\"font-size:1.4em;color: var(--muted-more-color);text-align: center;line-height: 100px;\">\r\n                {app.translator.trans(\"wusong8899-user-submission.lib.list-empty\")}\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {!loading && this.hasMoreResults() && (\r\n            <div style=\"text-align:center;padding:20px\">\r\n              <Button className={'Button Button--primary'} disabled={this.loading} loading={this.loading} onclick={() => this.loadMore()}>\r\n                {app.translator.trans('wusong8899-user-submission.lib.list-load-more')}\r\n              </Button>\r\n            </div>\r\n          )}\r\n\r\n          {loading && <div className=\"UserSubmission-loadMore\">{loading}</div>}\r\n\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n  \r\n  private hasMoreResults(): boolean {\r\n    return this.moreResults;\r\n  }\r\n\r\n  private loadMore(): void {\r\n    this.loading = true;\r\n    this.loadResults(this.userSubmissionList.length);\r\n  }\r\n\r\n  private parseResults(results: UserSubmissionData[]): UserSubmissionData[] {\r\n    this.moreResults = !!(results as any).payload?.links?.next;\r\n    this.userSubmissionList.push(...results);\r\n\r\n    this.loading = false;\r\n    m.redraw();\r\n\r\n    return results;\r\n  }\r\n\r\n  private loadResults(offset: number = 0): Promise<UserSubmissionData[]> {\r\n    return app.store\r\n      .find(\"userSubmissionList\", {\r\n        page: {\r\n          offset\r\n        },\r\n      })\r\n      .catch(() => [])\r\n      .then(this.parseResults.bind(this));\r\n  }\r\n\r\n}\r\n", "import Model from \"flarum/Model\";\r\nimport User from \"flarum/models/User\";\r\nimport { UserSubmissionAttributes } from \"../../types\";\r\n\r\nexport default class UserSubmission extends Model<UserSubmissionAttributes> {\r\n  id!: () => string;\r\n  amount!: () => number;\r\n  platform!: () => string;\r\n  platform_account!: () => string;\r\n  user_account!: () => string;\r\n  submission_user_id!: () => string;\r\n  review_user_id!: () => string | null;\r\n  review_result!: () => 'pending' | 'approved' | 'rejected' | null;\r\n  assigned_at!: () => Date | null;\r\n  reviewed_at!: () => Date | null;\r\n  fromUser!: () => User;\r\n  reviewUser!: () => User | null;\r\n}\r\n\r\nObject.assign(UserSubmission.prototype, {\r\n  id: Model.attribute(\"id\"),\r\n  amount: Model.attribute(\"amount\"),\r\n  platform: Model.attribute(\"platform\"),\r\n  platform_account: Model.attribute(\"platform_account\"),\r\n  user_account: Model.attribute(\"user_account\"),\r\n  submission_user_id: Model.attribute(\"submission_user_id\"),\r\n  review_user_id: Model.attribute(\"review_user_id\"),\r\n  review_result: Model.attribute(\"review_result\"),\r\n  assigned_at: Model.attribute(\"assigned_at\"),\r\n  reviewed_at: Model.attribute(\"reviewed_at\"),\r\n  fromUser: Model.hasOne(\"fromUser\"),\r\n  reviewUser: Model.hasOne(\"reviewUser\"),\r\n});\r\n", "import app from 'flarum/admin/app';\r\nimport SettingsPage from './components/SettingsPage';\r\nimport UserSubmission from \"../forum/model/UserSubmission\";\r\n\r\napp.initializers.add('wusong8899-user-submission', () => {\r\n  app.store.models.userSubmissionList = UserSubmission;\r\n  app.extensionData\r\n    .for('wusong8899-user-submission')\r\n    .registerPage(SettingsPage);\r\n});\r\n"], "names": ["_UserSubmissionReviewModal", "Modal", "vnode", "app", "<PERSON><PERSON>", "e", "value", "UserSubmissionReviewModal", "UserSubmissionListItem", "Component", "itemData", "amount", "platform", "platformAccount", "userAccount", "fromUser", "reviewResult", "reviewResultText", "assignedAt", "reviewedAt", "username", "UserSubmissionSettingsPage", "ExtensionPage", "attrs", "loading", "LoadingIndicator", "results", "offset", "UserSubmission", "Model", "SettingsPage"], "mappings": "wCASA,MAAqBA,EAArB,MAAqBA,UAAkCC,CAAsC,CAA7F,aAAA,CAAA,MAAA,GAAA,SAAA,EAKE,KAAQ,QAAmB,EAAA,CAE3B,OAAOC,EAAY,CACjB,MAAM,OAAOA,CAAK,EAClB,KAAK,SAAW,KAAK,MAAM,SAC3B,KAAK,QAAU,EACjB,CAEA,WAAY,CACV,MAAO,cACT,CAEA,OAAQ,CACN,OAAOC,EAAI,WAAW,MAAM,oDAAoD,CAClF,CAEA,SAAU,CAER,OACE,EAAC,MAAA,CAAI,UAAU,YAAA,EACb,EAAC,OAAI,UAAU,aAAa,MAAM,qBAAA,EAC/BC,EAAO,UACN,CACE,MAAO,kBACP,UAAW,yBACX,SAAU,KAAK,QACf,QAAUC,GAAa,CACrB,KAAK,cAAcA,EAAG,UAAU,CAClC,CAAA,EAEFF,EAAI,WAAW,MAAM,uCAAuC,CAAA,EAC5D,IACDC,EAAO,UACN,CACE,MAAO,kBACP,UAAW,wBACX,SAAU,KAAK,QACf,QAAUC,GAAa,CACrB,KAAK,cAAcA,EAAG,UAAU,CAClC,CAAA,EAEFF,EAAI,WAAW,MAAM,wCAAwC,CAAA,EAC7D,IACDC,EAAO,UACN,CACE,MAAO,kBACP,UAAW,SACX,SAAU,KAAK,QACf,QAAS,IAAM,CACb,KAAK,KAAA,CACP,CAAA,EAEFD,EAAI,WAAW,MAAM,uCAAuC,CAAA,CAEhE,CACF,CAEJ,CAEQ,cAAcE,EAAUC,EAAsC,CACpED,EAAE,eAAA,EAEF,KAAK,QAAU,GACf,KAAK,SAAS,KAAK,CACjB,aAAcC,CAAA,CACf,EACA,KAAK,IAAM,CACV,KAAK,KAAA,EACL,KAAK,QAAU,EACjB,CAAC,EACA,MAAM,IAAM,CACX,KAAK,QAAU,EACjB,CAAC,CACH,CACF,EA9EEN,EAAO,8BAAgC,GACvCA,EAAO,4BAA8B,GAFvC,IAAqBO,EAArBP,ECEA,MAAqBQ,UAA+BC,CAAuC,CAEzF,MAAO,CACL,KAAM,CAAE,SAAAC,GAAa,KAAK,MAEpBC,EAASD,EAAS,OAAA,EAClBE,EAAWF,EAAS,SAAA,EACpBG,EAAkBH,EAAS,iBAAA,EAC3BI,EAAcJ,EAAS,aAAA,EACvBK,EAAWL,EAAS,SAAA,EACpBM,EAAeN,EAAS,cAAA,EACxBO,EAAmBd,EAAI,WAAW,MACtCa,IAAiB,WACb,wDACA,wDAAA,EAEAE,EAAaR,EAAS,YAAA,EACtBS,EAAaT,EAAS,YAAA,EAI5B,OACE,EAAC,MAAA,CAAI,UAAU,6BAAA,IACZ,MAAA,CAAI,MAAM,aAAA,EACT,EAAC,OAAI,MAAM,+CAAA,EACRS,GACC,EAAC,WACEf,EAAO,UAAU,CAChB,MAAO,oBACP,SAAU,GACV,UAAW,QAAA,EAEbD,EAAI,WAAW,MAAM,gDAAgD,CAAA,CAEvE,EAED,CAACgB,GACA,EAAC,WACEf,EAAO,UAAU,CAChB,MAAO,oBACP,UAAW,yBACX,QAAS,IAAM,CACb,KAAK,WAAWM,CAAQ,CAC1B,CAAA,EAEFP,EAAI,WAAW,MAAM,8CAA8C,CAAA,CAErE,EAEF,EAAC,MAAA,KACEC,EAAO,UAAU,CAChB,UAAW,wBACX,KAAM,eACN,MAAOD,EAAI,WAAW,MAAM,oDAAoD,EAChF,QAAS,IAAM,CACb,KAAK,WAAWO,CAAQ,CAC1B,CAAA,CACD,CACH,CACF,CACF,EACA,EAAC,MAAA,KACC,EAAC,IAAA,KAAGP,EAAI,WAAW,MAAM,8CAA8C,EAAE,IAAE,EAC1EiB,EAASL,CAAQ,EAAE,MACpB,EAAC,IAAA,KAAGZ,EAAI,WAAW,MAAM,4CAA4C,EAAE,IAAE,EACxEQ,EAAO,MACR,EAAC,IAAA,KAAGR,EAAI,WAAW,MAAM,gDAAgD,EAAE,IAAE,EAC5Ee,CACH,EACA,EAAC,WACC,EAAC,IAAA,KAAGf,EAAI,WAAW,MAAM,8CAA8C,EAAE,IAAE,EAC1ES,EAAS,MACV,EAAC,SAAGT,EAAI,WAAW,MAAM,qDAAqD,EAAE,IAAE,EACjFU,EAAgB,QAChB,IAAA,KAAGV,EAAI,WAAW,MAAM,iDAAiD,EAAE,IAAE,EAC7EW,CACH,EACCK,GACC,EAAC,WACC,EAAC,IAAA,KAAGhB,EAAI,WAAW,MAAM,kDAAkD,EAAE,IAAE,EAC9Ec,EAAiB,MAClB,EAAC,IAAA,KAAGd,EAAI,WAAW,MAAM,8CAA8C,EAAE,IAAE,EAC1EgB,CACH,CAEJ,CAEJ,CAEQ,WAAWT,EAAoC,CACrDP,EAAI,MAAM,KAAKI,EAA2B,CAAE,SAAAG,EAAU,CACxD,CAEQ,WAAWA,EAAoC,CACjD,QAAQP,EAAI,WAAW,MAAM,iDAAiD,CAAC,GACjFA,EAAI,QAAQ,CACV,OAAQ,SACR,IAAKA,EAAI,MAAM,UAAU,QAAQ,EAAI,uBAAyBO,EAAS,GAAA,CAAG,CAC3E,EACE,KAAK,IAAM,CAEV,OAAO,SAAS,OAAA,CAClB,CAAC,EACA,MAAM,IAAM,CACX,MAAMP,EAAI,WAAW,MAAM,gDAAgD,CAAC,CAC9E,CAAC,CAEP,CACF,CChHA,MAAqBkB,UAAmCC,CAAc,CAAtE,aAAA,CAAA,MAAA,GAAA,SAAA,EACE,KAAQ,QAAmB,GAC3B,KAAQ,YAAuB,GAC/B,KAAQ,mBAA2C,CAAA,CAAC,CAEpD,OAAOC,EAAY,CACjB,MAAM,OAAOA,CAAK,EAClB,KAAK,QAAU,GACf,KAAK,YAAc,GACnB,KAAK,mBAAqB,CAAA,EAC1B,KAAK,YAAA,CACP,CAEA,SAAU,CACR,IAAIC,EAEJ,OAAI,KAAK,UACPA,EAAUC,EAAiB,UAAU,CAAE,KAAM,QAAS,KAIrD,MAAA,CAAI,UAAU,yCAAA,EACb,EAAC,OAAI,UAAU,WAAA,EAEb,EAAC,KAAA,CAAG,MAAM,oCAAA,EACP,KAAK,mBAAmB,IAAKf,GAE1B,EAAC,KAAA,CAAG,OAAQA,EAAS,GAAA,EAAM,MAAM,4CAAA,EAC9BF,EAAuB,UAAU,CAAE,SAAAE,CAAA,CAAU,CAChD,CAEH,CACH,EAEC,CAAC,KAAK,SAAW,KAAK,mBAAmB,SAAW,GACnD,EAAC,MAAA,KACC,EAAC,MAAA,CAAI,MAAM,uFAAA,EACRP,EAAI,WAAW,MAAM,2CAA2C,CACnE,CACF,EAGD,CAACqB,GAAW,KAAK,eAAA,GAChB,EAAC,MAAA,CAAI,MAAM,gCAAA,EACT,EAACpB,EAAA,CAAO,UAAW,yBAA0B,SAAU,KAAK,QAAS,QAAS,KAAK,QAAS,QAAS,IAAM,KAAK,UAAS,EACtHD,EAAI,WAAW,MAAM,+CAA+C,CACvE,CACF,EAGDqB,GAAW,EAAC,MAAA,CAAI,UAAU,yBAAA,EAA2BA,CAAQ,CAEhE,CACF,CAEJ,CAEQ,gBAA0B,CAChC,OAAO,KAAK,WACd,CAEQ,UAAiB,CACvB,KAAK,QAAU,GACf,KAAK,YAAY,KAAK,mBAAmB,MAAM,CACjD,CAEQ,aAAaE,EAAqD,CACxE,YAAK,YAAc,CAAC,CAAEA,EAAgB,SAAS,OAAO,KACtD,KAAK,mBAAmB,KAAK,GAAGA,CAAO,EAEvC,KAAK,QAAU,GACf,EAAE,OAAA,EAEKA,CACT,CAEQ,YAAYC,EAAiB,EAAkC,CACrE,OAAOxB,EAAI,MACR,KAAK,qBAAsB,CAC1B,KAAM,CACJ,OAAAwB,CAAA,CACF,CACD,EACA,MAAM,IAAM,CAAA,CAAE,EACd,KAAK,KAAK,aAAa,KAAK,IAAI,CAAC,CACtC,CAEF,CC1FA,MAAqBC,UAAuBC,CAAgC,CAa5E,CAEA,OAAO,OAAOD,EAAe,UAAW,CACtC,GAAIC,EAAM,UAAU,IAAI,EACxB,OAAQA,EAAM,UAAU,QAAQ,EAChC,SAAUA,EAAM,UAAU,UAAU,EACpC,iBAAkBA,EAAM,UAAU,kBAAkB,EACpD,aAAcA,EAAM,UAAU,cAAc,EAC5C,mBAAoBA,EAAM,UAAU,oBAAoB,EACxD,eAAgBA,EAAM,UAAU,gBAAgB,EAChD,cAAeA,EAAM,UAAU,eAAe,EAC9C,YAAaA,EAAM,UAAU,aAAa,EAC1C,YAAaA,EAAM,UAAU,aAAa,EAC1C,SAAUA,EAAM,OAAO,UAAU,EACjC,WAAYA,EAAM,OAAO,YAAY,CACvC,CAAC,EC5BD1B,EAAI,aAAa,IAAI,6BAA8B,IAAM,CACvDA,EAAI,MAAM,OAAO,mBAAqByB,EACtCzB,EAAI,cACD,IAAI,4BAA4B,EAChC,aAAa2B,CAAY,CAC9B,CAAC"}