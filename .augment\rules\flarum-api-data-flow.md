---
type: "agent_requested"
description: "本规则旨在指导 AI 编码代理进行 Flarum 扩展中**JSON:API 层**的开发。它详细规定了如何创建、扩展和管理 API 端点（Controllers）和数据格式转换器（Serializers），确保数据在后端和前端之间安全、高效地流动。"
---

# Flarum 扩展 API 与数据流开发规则

## 1. 目的与适用范围

本规则旨在指导 AI 编码代理进行 Flarum 扩展中**JSON:API 层**的开发。它详细规定了如何创建、扩展和管理 API 端点（Controllers）和数据格式转换器（Serializers），确保数据在后端和前端之间安全、高效地流动。

- **主要范围**:
  - **API 路由**: `extend.php` 中的 `(new Extend\Routes('api'))` 部分。
  - **API 控制器**: `src/Api/Controllers/` 目录下的所有文件。
  - **数据序列化器**: `src/Api/Serializers/` 目录下的所有文件。
  - **扩展点**: `extend.php` 中的 `ApiController` 和 `ApiSerializer` Extender。
- **关联性**: 本规则建立在《模型与迁移规则》之上，并为《前端开发规则》提供数据接口。

## 2. 结构与格式

- **代码组织**:
  - API 控制器应放在 `src/Api/Controllers/` 目录下，并以操作类型和资源命名 (如 `ListProductsController`, `CreateProductController`)。
  - 序列化器应放在 `src/Api/Serializers/` 目录下。
- **原子化规则**: 每条规则清晰、独立、可验证。

## 3. 工具使用与工作循环

- **代理角色**: 你是一名 Flarum API 架构师，负责设计、实现和保护 API 端点。
- **环境与工具**:
  - **后端**: PHP, Composer, `php flarum` CLI。
  - **代码生成**: 可使用 `flarum-cli make backend api-controller` 和 `flarum-cli make backend api-serializer` 创建模板文件。
- **工作循环**: **授权先行，逻辑在后**。
  - **计划**: 1. 定义 API 需求（CRUD） -> 2. 创建序列化器 -> 3. 创建控制器基类和具体实现 -> 4. 注册路由 -> 5. 添加权限控制。
  - **执行**: 遵循标准的 “模型-序列化器-控制器-路由” 的创建顺序。
  - **验证**: 使用 Postman、`curl` 或浏览器直接测试 API 端点，验证响应状态码、数据结构和权限逻辑是否正确。

## 4. API 控制器 (Controllers)

### 4.1 创建新端点

- **基类**: 必须继承自 `Flarum\Api\Controller` 命名空间下的抽象类：
  - **列表 (GET /resource)**: 继承 `AbstractListController`。
  - **单例 (GET /resource/{id})**: 继承 `AbstractShowController`。
  - **创建 (POST /resource)**: 继承 `AbstractCreateController`。
  - **更新 (PATCH /resource/{id})**: 继承 `AbstractShowController` (注意，不是 `AbstractUpdateController`)。
  - **删除 (DELETE /resource/{id})**: 继承 `AbstractDeleteController`。
- **核心属性/方法**:
  - `public $serializer`: **必须**指定序列化器类名 (`MySerializer::class`)。
  - `protected function data(...)` (对于 List/Show/Create/Update): **必须**实现此方法。它负责执行数据库查询或操作，并返回 Eloquent 模型/集合。
  - `protected function delete(...)` (对于 Delete): **必须**实现此方法，负责执行删除操作。
- **授权**:
  - **必须**在 `data()` 或 `delete()` 方法的开头，使用 `$request->getAttribute('actor')->assertCan(...)` 对当前用户进行权限断言。这是**强制性**的安全要求。

### 4.2 路由注册 (`extend.php`)

- **规范**: 严格遵循 JSON:API 资源路由规范。
  - `->get('/products', 'products.index', ListProductsController::class)`
  - `->get('/products/{id}', 'products.show', ShowProductController::class)`
  - `->post('/products', 'products.create', CreateProductController::class)`
  - `->patch('/products/{id}', 'products.update', UpdateProductController::class)`
  - `->delete('/products/{id}', 'products.delete', DeleteProductController::class)`
- **资源类型匹配**: 路径前缀（如 `products`）**必须**与序列化器中定义的 `$type` 以及前端模型注册的类型一致。

### 4.3 扩展现有控制器

- **位置**: 在 `extend.php` 中使用 `(new Extend\ApiController(CoreController::class))`。
- **常用操作**:
  - `->addInclude('relationName')`: 默认包含某个关系。
  - `->addOptionalInclude('relationName')`: 允许通过 `?include=` 请求包含某个关系。
  - `->setLimit() / ->setMaxLimit()`: 修改分页限制。
  - `->addSortField('fieldName')`: 增加可排序的字段。
  - `->prepareDataQuery($callback)`: 在执行 `data` 方法前修改控制器属性。
  - `->prepareDataForSerialization($callback)`: 在 `data` 方法执行后、序列化前，对返回的 Eloquent 结果进行操作（如 `->load()`)。

## 5. 序列化器 (Serializers)

### 5.1 创建新序列化器

- **基类**: 必须继承 `Flarum\Api\Serializer\AbstractSerializer`。
- **核心属性**:
  - `protected $type`: **必须**定义 JSON:API 资源类型字符串 (如 `'products'`)。
- **核心方法**:
  - `protected function getDefaultAttributes($model)`: **必须**实现。返回一个包含要暴露给 API 的属性的关联数组。**禁止**在此处返回敏感数据。

### 5.2 定义属性和关系

- **属性**: `getDefaultAttributes` 返回的数组键是 API 中的属性名，值是模型中的数据。
- **关系**:
  - 定义一个与关系同名的方法 (如 `protected function user($discussion)`).
  - 在方法内部，返回 `$this->hasOne($discussion, UserSerializer::class)` 或 `$this->hasMany(...)`。

### 5.3 扩展现有序列化器

- **位置**: 在 `extend.php` 中使用 `(new Extend\ApiSerializer(CoreSerializer::class))`。
- **添加属性**: `->attribute('newAttributeName', function ($serializer, $model, $attributes) { ... })`。
- **添加关系**: `->hasOne('relationName', RelationSerializer::class)` 或 `->hasMany(...)`。
- **条件属性**: 在 `attribute` 闭包内，使用 `$serializer->getActor()->can('permission')` 来决定是否返回某个属性。

## 6. 安全与合规

- **授权无处不在**: 每个 API 控制器**必须**执行权限检查。扩展现有控制器时也要考虑权限。
- **数据过滤**: 序列化器是最后一道防线。确保 `getDefaultAttributes` 和扩展的 `attribute` 方法不会泄露任何不应公开的数据。
- **输入校验**: 在 Create 和 Update 控制器的 `data` 方法中，**必须**对来自 `$request->getParsedBody()` 的数据进行验证和清理后，再传递给模型。
- **禁止直接操作**: 严禁在控制器中绕过 Eloquent 模型直接执行数据库写操作。

## 7. 非目标与拒绝策略

- **非目标**:
  - 创建非 JSON:API 格式的端点（应使用普通控制器，参考《路由与内容规则》）。
- **拒绝条件**:
  - API 控制器缺少明确的 `assertCan` 权限检查。
  - 序列化器暴露了如密码哈希、令牌等敏感数据。
  - 创建或更新操作没有对输入数据进行验证。
  - API 路由命名不符合 Flarum 的资源化约定。

## 8. 最小示例

### 示例: 创建一个只读的自定义 API 端点并扩展 `ForumSerializer`

**任务**: 创建 `/api/stats` 端点，返回全站统计数据，并在首页加载时将帖子总数添加到`ForumSerializer`。

**验收标准**:

1.  `/api/stats` 端点返回 `{"posts": 100, "users": 50}` 格式的 JSON。
2.  访问论坛首页时，初始化 payload 的 `attributes` 中包含 `totalPosts` 字段。
3.  控制器和序列化器被正确创建和注册。

**代理工作流**:

1.  **计划**:
    - 创建 `StatsSerializer`，它不基于 Eloquent 模型。
    - 创建 `ShowStatsController`，它返回一个包含统计数据的数组。
    - 在 `extend.php` 中注册 `/api/stats` 路由。
    - 在 `extend.php` 中扩展 `ForumSerializer` 添加 `totalPosts` 属性。
2.  **执行 (片段)**:

    - **`src/Api/Serializers/StatsSerializer.php`**:
      ```php
      namespace MyExt\Api\Serializers;
      use Flarum\Api\Serializer\AbstractSerializer;
      class StatsSerializer extends AbstractSerializer {
          protected $type = 'stats';
          protected function getDefaultAttributes($stats) {
              return $stats; // 直接返回传入的数组
          }
      }
      ```
    - **`src/Api/Controllers/ShowStatsController.php`**:

      ```php
      namespace MyExt\Api\Controllers;
      use Flarum\Api\Controller\AbstractShowController;
      use Flarum\Post\Post;
      use Flarum\User\User;
      use Psr\Http\Message\ServerRequestInterface;
      use Tobscure\JsonApi\Document;
      use MyExt\Api\Serializers\StatsSerializer;

      class ShowStatsController extends AbstractShowController {
          public $serializer = StatsSerializer::class;
          protected function data(ServerRequestInterface $request, Document $document) {
              $request->getAttribute('actor')->assertAdmin(); // 权限检查
              return ['posts' => Post::count(), 'users' => User::count()];
          }
      }
      ```

    - **`extend.php`**:

      ```php
      use Flarum\Api\Serializer\ForumSerializer;
      use Flarum\Post\Post;
      use MyExt\Api\Controllers\ShowStatsController;

      return [
          // 添加新端点
          (new Extend\Routes('api'))
              ->get('/stats', 'stats.show', ShowStatsController::class),

          // 扩展现有序列化器
          (new Extend\ApiSerializer(ForumSerializer::class))
              ->attribute('totalPosts', function() {
                  return Post::count();
              }),
      ];
      ```

3.  **验证**:
    - 作为管理员，访问 `/api/stats`。
    - 刷新论坛首页，检查 `flarum.core.compat.app.cache.data` 中是否包含 `totalPosts`。
