{"version": 3, "file": "forum.js", "sources": ["../src/forum/components/UserSubmissionApplicationSubmitSuccessModal.tsx", "../src/forum/components/UserSubmissionApplicationModal.tsx", "../src/forum/components/UserSubmissionWidget.tsx", "../src/forum/model/UserSubmission.ts", "../src/forum/components/UserSubmissionApplicationListItem.tsx", "../src/forum/components/UserSubmissionApplicationListPage.tsx", "../src/forum/components/UserSubmissionApplicationPage.tsx", "../src/forum/addUserPage.ts", "../src/forum/components/UserSubmissionNotification.tsx", "../src/forum/components/UserSubmissionIndexPage.tsx", "../src/forum/index.ts"], "sourcesContent": ["import app from 'flarum/forum/app';\r\nimport Modal from 'flarum/components/Modal';\r\nimport Button from 'flarum/components/Button';\r\n\r\nexport default class UserSubmissionApplicationSubmitSuccessModal extends Modal {\r\n  static isDismissibleViaBackdropClick = false;\r\n  static isDismissibleViaCloseButton = true;\r\n\r\n  oninit(vnode: any) {\r\n    super.oninit(vnode);\r\n  }\r\n\r\n  className(): string {\r\n    return 'Modal--small';\r\n  }\r\n\r\n  title(): string {\r\n    return app.translator.trans('wusong8899-user-submission.forum.submit-success');\r\n  }\r\n\r\n  content() {\r\n    return (\r\n      <div className=\"Modal-body\">\r\n        <div className=\"Form\">\r\n          <div className=\"Form-group\" style=\"text-align: center;\">\r\n            {Button.component(\r\n              {\r\n                className: 'Button Button--primary',\r\n                onclick: () => {\r\n                  this.hide();\r\n                },\r\n              },\r\n              app.translator.trans('wusong8899-user-submission.lib.ok')\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n}\r\n", "import app from 'flarum/forum/app';\r\nimport Modal from 'flarum/components/Modal';\r\nimport Stream from 'flarum/utils/Stream';\r\nimport Button from 'flarum/components/Button';\r\nimport UserSubmissionApplicationSubmitSuccessModal from './UserSubmissionApplicationSubmitSuccessModal';\r\nimport { StreamType } from '../../types';\r\n\r\nexport default class UserSubmissionApplicationModal extends Modal {\r\n  static isDismissibleViaBackdropClick = false;\r\n  static isDismissibleViaCloseButton = true;\r\n\r\n  private loading: boolean = false;\r\n  private amount: StreamType<string>;\r\n  private platform: StreamType<string>;\r\n  private platformAccount: StreamType<string>;\r\n  private userAccount: StreamType<string>;\r\n\r\n  oninit(vnode: any) {\r\n    super.oninit(vnode);\r\n    this.loading = false;\r\n\r\n    this.amount = Stream(\"\");\r\n    this.platform = Stream(\"\");\r\n    this.platformAccount = Stream(\"\");\r\n    this.userAccount = Stream(\"\");\r\n  }\r\n\r\n  className(): string {\r\n    return 'Modal--small';\r\n  }\r\n\r\n  title(): string {\r\n    return app.translator.trans('wusong8899-user-submission.forum.item-header');\r\n  }\r\n\r\n  content() {\r\n    return (\r\n      <div className=\"Modal-body\">\r\n        <div className=\"Form\">\r\n          <div className=\"Form-group\" style=\"text-align: center;\">\r\n              <div>\r\n                <div className=\"userSubmissionDataLabel\">{app.translator.trans('wusong8899-user-submission.lib.list-amount')}</div>\r\n                <input type=\"number\" min=\"0\" disabled={this.loading} required className=\"FormControl\" bidi={this.amount} />\r\n              </div>\r\n\r\n              <div className=\"userSubmissionDataLabel\">{app.translator.trans('wusong8899-user-submission.lib.list-platform')}</div>\r\n              <input maxLength=\"500\" disabled={this.loading} required className=\"FormControl\" bidi={this.platform} />\r\n\r\n              <div className=\"userSubmissionDataLabel\">{app.translator.trans('wusong8899-user-submission.lib.list-platformAccount')}</div>\r\n              <input maxLength=\"500\" disabled={this.loading} required className=\"FormControl\" bidi={this.platformAccount} />\r\n\r\n              <div className=\"userSubmissionDataLabel\">{app.translator.trans('wusong8899-user-submission.lib.list-userAccountFull')}</div>\r\n              <input maxLength=\"500\" disabled={this.loading} required className=\"FormControl\" bidi={this.userAccount} />\r\n          </div>\r\n\r\n          <div className=\"Form-group\" style=\"text-align: center;\">\r\n            {Button.component(\r\n              {\r\n                style:'min-width:66px;',\r\n                className: 'Button Button--primary',\r\n                type: 'submit',\r\n                loading: this.loading,\r\n              },\r\n              app.translator.trans('wusong8899-user-submission.lib.ok')\r\n            )}&nbsp;\r\n            {Button.component(\r\n              {\r\n                style:'min-width:66px;',\r\n                className: 'Button',\r\n                loading: this.loading,\r\n                onclick: () => {\r\n                  this.hide();\r\n                }\r\n              },\r\n              app.translator.trans('wusong8899-user-submission.lib.cancel')\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n\r\n  onsubmit(e: Event): void {\r\n    e.preventDefault();\r\n\r\n    this.loading = true;\r\n\r\n    app.store\r\n      .createRecord(\"userSubmissionList\")\r\n      .save({\r\n        amount: this.amount(),\r\n        platform: this.platform(),\r\n        platformAccount: this.platformAccount(),\r\n        userAccount: this.userAccount()\r\n      })\r\n      .then((result) => {\r\n        app.store.pushPayload(result);\r\n        this.loading = false;\r\n        app.modal.show(UserSubmissionApplicationSubmitSuccessModal);\r\n      })\r\n      .catch(() => {\r\n        this.loading = false;\r\n      });\r\n  }\r\n}\r\n", "import app from 'flarum/forum/app';\nimport Component, { ComponentAttrs } from 'flarum/Component';\nimport UserSubmissionApplicationModal from './UserSubmissionApplicationModal';\nimport LogInModal from 'flarum/components/LogInModal';\n\ninterface UserSubmissionWidgetAttrs extends ComponentAttrs {}\n\n/**\n * Widget component for user submission application\n * Displays on the tags page to allow users to submit applications\n */\nexport default class UserSubmissionWidget extends Component<UserSubmissionWidgetAttrs> {\n  private static readonly POLL_INTERVAL = 10; // milliseconds\n  private static readonly WIDGET_CLASS = 'UserSubmissionApplication';\n  private static readonly CONTAINER_SELECTOR = '.splideTagContainer';\n\n  oncreate(vnode: any) {\n    super.oncreate(vnode);\n    this.setupWidget();\n  }\n\n  view() {\n    return (\n      <div className=\"user-submission-widget\">\n        <div className=\"user-submission-header\">\n          <img \n            className=\"user-submission-icon\"\n            src=\"https://i.mji.rip/2025/08/15/102ee6e187aa177ddfe02364dc82208d.png\" \n            alt=\"User Submission\"\n          />\n          <span className=\"user-submission-title\">\n            {app.translator.trans(\"wusong8899-user-submission.forum.item-header\")}\n          </span>\n        </div>\n        <div className=\"user-submission-input-container\">\n          <div \n            className=\"user-submission-input-overlay\"\n            onclick={this.handleInputClick.bind(this)}\n          />\n          <div className=\"Search-input\">\n            <input \n              disabled \n              className=\"FormControl\" \n              type=\"search\" \n              placeholder={app.translator.trans('wusong8899-user-submission.forum.item-input-placeholder')}\n            />\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  private setupWidget(): void {\n    const pollForContainer = setInterval(() => {\n      const container = $(UserSubmissionWidget.CONTAINER_SELECTOR);\n      \n      if (container.length > 0) {\n        clearInterval(pollForContainer);\n        this.insertWidget(container);\n      }\n    }, UserSubmissionWidget.POLL_INTERVAL);\n  }\n\n  private insertWidget(container: JQuery): void {\n    if (!container.hasClass(UserSubmissionWidget.WIDGET_CLASS)) {\n      const widgetHtml = this.renderWidgetHtml();\n      $(widgetHtml).insertAfter(container);\n      container.addClass(UserSubmissionWidget.WIDGET_CLASS);\n      this.attachEventHandlers();\n    }\n  }\n\n  private renderWidgetHtml(): string {\n    return `\n      <div style=\"display: flex;align-items: center;font-weight: bold;font-size: 14px;\">\n        <img style=\"width:22px;\" src=\"https://i.mji.rip/2025/08/15/102ee6e187aa177ddfe02364dc82208d.png\" />&nbsp;&nbsp;${app.translator.trans(\"wusong8899-user-submission.forum.item-header\")}\n      </div>\n      <div style=\"padding-top: 10px;position:relative\">\n        <div class=\"UserSubmissionApplicationInput\" style=\"position: absolute;height: 37px;width: 100%;z-index: 1;\"></div>\n        <div style=\"width:100%\" class=\"Search-input\">\n          <input disabled style=\"width: 100%;font-size:12px;\" class=\"FormControl\" type=\"search\" placeholder=\"${app.translator.trans('wusong8899-user-submission.forum.item-input-placeholder')}\" />\n        </div>\n      </div>\n    `;\n  }\n\n  private attachEventHandlers(): void {\n    $(\".UserSubmissionApplicationInput\").on(\"click\", this.handleInputClick.bind(this));\n  }\n\n  private handleInputClick(): void {\n    if (app.session.user) {\n      app.modal.show(UserSubmissionApplicationModal);\n    } else {\n      app.modal.show(LogInModal);\n    }\n  }\n}\n", "import Model from \"flarum/Model\";\r\nimport User from \"flarum/models/User\";\r\nimport { UserSubmissionAttributes } from \"../../types\";\r\n\r\nexport default class UserSubmission extends Model<UserSubmissionAttributes> {\r\n  id!: () => string;\r\n  amount!: () => number;\r\n  platform!: () => string;\r\n  platform_account!: () => string;\r\n  user_account!: () => string;\r\n  submission_user_id!: () => string;\r\n  review_user_id!: () => string | null;\r\n  review_result!: () => 'pending' | 'approved' | 'rejected' | null;\r\n  assigned_at!: () => Date | null;\r\n  reviewed_at!: () => Date | null;\r\n  fromUser!: () => User;\r\n  reviewUser!: () => User | null;\r\n}\r\n\r\nObject.assign(UserSubmission.prototype, {\r\n  id: Model.attribute(\"id\"),\r\n  amount: Model.attribute(\"amount\"),\r\n  platform: Model.attribute(\"platform\"),\r\n  platform_account: Model.attribute(\"platform_account\"),\r\n  user_account: Model.attribute(\"user_account\"),\r\n  submission_user_id: Model.attribute(\"submission_user_id\"),\r\n  review_user_id: Model.attribute(\"review_user_id\"),\r\n  review_result: Model.attribute(\"review_result\"),\r\n  assigned_at: Model.attribute(\"assigned_at\"),\r\n  reviewed_at: Model.attribute(\"reviewed_at\"),\r\n  fromUser: Model.hasOne(\"fromUser\"),\r\n  reviewUser: Model.hasOne(\"reviewUser\"),\r\n});\r\n", "import app from 'flarum/forum/app';\r\nimport Component, { ComponentAttrs } from \"flarum/Component\";\r\nimport Button from 'flarum/components/Button';\r\nimport { UserSubmissionData } from \"../../types\";\r\n\r\ninterface UserSubmissionApplicationListItemAttrs extends ComponentAttrs {\r\n  itemData: UserSubmissionData;\r\n}\r\n\r\nexport default class UserSubmissionApplicationListItem extends Component<UserSubmissionApplicationListItemAttrs> {\r\n\r\n  view() {\r\n    const { itemData } = this.attrs;\r\n\r\n    const amount = itemData.amount();\r\n    const id = itemData.id();\r\n    const platform = itemData.platform();\r\n    const platformAccount = itemData.platform_account();\r\n    const userAccount = itemData.user_account();\r\n    const reviewResult = itemData.review_result();\r\n    const reviewResultText = app.translator.trans(\r\n      reviewResult === 'approved'\r\n        ? 'wusong8899-user-submission.lib.list-submission-accept'\r\n        : 'wusong8899-user-submission.lib.list-submission-decline'\r\n    );\r\n    const assignedAt = itemData.assigned_at();\r\n    const reviewedAt = itemData.reviewed_at();\r\n    let containerClassName = \"userSubmissionApplicationContainer \";\r\n\r\n    if (reviewedAt === null) {\r\n      containerClassName += \"userSubmissionApplicationReviewing\";\r\n    } else {\r\n      if (reviewResult === 'approved') {\r\n        containerClassName += \"userSubmissionApplicationAccepted\";\r\n      } else {\r\n        containerClassName += \"userSubmissionApplicationDeclined\";\r\n      }\r\n    }\r\n\r\n    return (\r\n      <div className={containerClassName}>\r\n        <div>\r\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-id')}: </b>\r\n          {id}&nbsp;|&nbsp;\r\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-assignedAt')}: </b>\r\n          {assignedAt}\r\n        </div>\r\n        <div>\r\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-amount')}: </b>\r\n          {amount}&nbsp;|&nbsp;\r\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-platform')}: </b>\r\n          {platform}&nbsp;|&nbsp;\r\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-platformAccount')}: </b>\r\n          {platformAccount}&nbsp;|&nbsp;\r\n          <b>{app.translator.trans('wusong8899-user-submission.lib.list-userAccount')}: </b>\r\n          {userAccount}\r\n        </div>\r\n        {reviewedAt && (\r\n          <div>\r\n            <b>{app.translator.trans('wusong8899-user-submission.lib.list-reviewResult')}: </b>\r\n            {reviewResult === 'rejected' && (\r\n              <span style=\"color:red\">{reviewResultText}&nbsp;|&nbsp;</span>\r\n            )}\r\n            {reviewResult === 'approved' && (\r\n              <span style=\"color:green\">{reviewResultText}&nbsp;|&nbsp;</span>\r\n            )}\r\n            <b>{app.translator.trans('wusong8899-user-submission.lib.list-reviewAt')}: </b>\r\n            {reviewedAt}\r\n          </div>\r\n        )}\r\n        {!reviewedAt && (\r\n          <div>\r\n            <b>{app.translator.trans('wusong8899-user-submission.lib.list-reviewResult')}: </b>\r\n            <span style=\"color:grey\">{app.translator.trans('wusong8899-user-submission.lib.list-submission-reviewing')}</span>\r\n          </div>\r\n        )}\r\n        <div style=\"margin-top: 10px; text-align: right;\">\r\n          {Button.component({\r\n            className: 'Button Button--danger Button--small',\r\n            icon: 'fas fa-trash',\r\n            onclick: () => this.deleteItem(itemData)\r\n          }, app.translator.trans('wusong8899-user-submission.forum.delete-submission'))}\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  private deleteItem(itemData: UserSubmissionData): void {\r\n    if (confirm(app.translator.trans('wusong8899-user-submission.forum.confirm-delete'))) {\r\n      app.request({\r\n        method: 'DELETE',\r\n        url: app.forum.attribute('apiUrl') + '/userSubmissionList/' + itemData.id(),\r\n      })\r\n        .then(() => {\r\n          // Refresh the page or remove the item from the list\r\n          window.location.reload();\r\n        })\r\n        .catch(() => {\r\n          alert(app.translator.trans('wusong8899-user-submission.forum.delete-failed'));\r\n        });\r\n    }\r\n  }\r\n}\r\n", "import Component, { ComponentAttrs } from \"flarum/Component\";\r\nimport app from \"flarum/forum/app\";\r\nimport LoadingIndicator from \"flarum/components/LoadingIndicator\";\r\nimport Button from \"flarum/components/Button\";\r\nimport { UserSubmissionData } from \"../../types\";\r\n\r\nimport UserSubmissionApplicationListItem from \"./UserSubmissionApplicationListItem\";\r\n\r\ninterface UserSubmissionApplicationListPageAttrs extends ComponentAttrs {\r\n  params: {\r\n    user: any;\r\n  };\r\n}\r\n\r\nexport default class UserSubmissionApplicationListPage extends Component<UserSubmissionApplicationListPageAttrs> {\r\n  private loading: boolean = true;\r\n  private moreResults: boolean = false;\r\n  private userSubmissionList: UserSubmissionData[] = [];\r\n\r\n  oninit(vnode: any) {\r\n    super.oninit(vnode);\r\n    this.loading = true;\r\n    this.moreResults = false;\r\n    this.userSubmissionList = [];\r\n    this.loadResults();\r\n  }\r\n\r\n  view() {\r\n    let loading: any;\r\n\r\n    if (this.loading) {\r\n      loading = LoadingIndicator.component({ size: \"large\" });\r\n    }\r\n\r\n    return (\r\n      <div>\r\n        <ul style=\"padding:0px;list-style-type: none;\">\r\n          {this.userSubmissionList.map((itemData) => {\r\n            return (\r\n              <li key={itemData.id()} style=\"margin-top:5px;background: var(--body-bg);\">\r\n                {UserSubmissionApplicationListItem.component({ itemData })}\r\n              </li>\r\n            );\r\n          })}\r\n        </ul>\r\n\r\n        {!this.loading && this.userSubmissionList.length === 0 && (\r\n          <div>\r\n            <div style=\"font-size:1.4em;color: var(--muted-more-color);text-align: center;line-height: 100px;\">\r\n              {app.translator.trans(\"wusong8899-user-submission.lib.list-empty\")}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {!loading && this.hasMoreResults() && (\r\n          <div style=\"text-align:center;padding:20px\">\r\n            <Button className={'Button Button--primary'} disabled={this.loading} loading={this.loading} onclick={() => this.loadMore()}>\r\n              {app.translator.trans('wusong8899-user-submission.lib.list-load-more')}\r\n            </Button>\r\n          </div>\r\n        )}\r\n\r\n        {loading && <div className=\"UserSubmission-loadMore\">{loading}</div>}\r\n\r\n      </div>\r\n    );\r\n  }\r\n  \r\n  private hasMoreResults(): boolean {\r\n    return this.moreResults;\r\n  }\r\n\r\n  private loadMore(): void {\r\n    this.loading = true;\r\n    this.loadResults(this.userSubmissionList.length);\r\n  }\r\n\r\n  private parseResults(results: UserSubmissionData[]): UserSubmissionData[] {\r\n    this.moreResults = !!(results as any).payload?.links?.next;\r\n    this.userSubmissionList.push(...results);\r\n\r\n    this.loading = false;\r\n    m.redraw();\r\n\r\n    return results;\r\n  }\r\n\r\n  private loadResults(offset: number = 0): Promise<UserSubmissionData[]> {\r\n    return app.store\r\n      .find(\"userSubmissionApplicationList\", {\r\n        page: {\r\n          offset\r\n        },\r\n      })\r\n      .catch(() => [])\r\n      .then(this.parseResults.bind(this));\r\n  }\r\n\r\n}\r\n", "import app from 'flarum/forum/app';\r\nimport UserPage from \"flarum/components/UserPage\";\r\nimport UserSubmissionApplicationListPage from \"./UserSubmissionApplicationListPage\";\r\n\r\nexport default class UserSubmissionApplicationPage extends UserPage {\r\n  oninit(vnode: any) {\r\n    super.oninit(vnode);\r\n    this.loadUser(m.route.param(\"username\"));\r\n  }\r\n\r\n  content() {\r\n    if (app.session.user) {\r\n      const currentUserID = app.session.user.id();\r\n      const targetUserID = this.user.id();\r\n\r\n      if (currentUserID === targetUserID) {\r\n        return (\r\n          <div>\r\n            {UserSubmissionApplicationListPage.component({\r\n              params: {\r\n                user: this.user,\r\n              },\r\n            })}\r\n          </div>\r\n        );\r\n      }\r\n    }\r\n\r\n    return null;\r\n  }\r\n}\r\n", "import app from 'flarum/forum/app';\r\nimport { extend } from \"flarum/extend\";\r\nimport UserPage from \"flarum/components/UserPage\";\r\nimport LinkButton from \"flarum/components/LinkButton\";\r\n\r\nimport UserSubmissionApplicationPage from './components/UserSubmissionApplicationPage';\r\n\r\nexport default function addUserPage(): void {\r\n  app.routes[\"user.userSubmissionApplication\"] = {\r\n    path: \"/u/:username/userSubmissionApplication\",\r\n    component: UserSubmissionApplicationPage,\r\n  };\r\n\r\n  extend(UserPage.prototype, \"navItems\", function (items) {\r\n      if (app.session.user) {\r\n        const currentUserID = app.session.user.id();\r\n        const targetUserID = this.user.id();\r\n\r\n        if (currentUserID === targetUserID) {\r\n          items.add(\r\n            \"userSubmissionApplication\",\r\n            LinkButton.component({\r\n                href: app.route(\"user.userSubmissionApplication\", {\r\n                  username: this.user.username(),\r\n                }),\r\n                icon: \"fas fa-file-signature\",\r\n              },\r\n              [\r\n                app.translator.trans(\r\n                  \"wusong8899-user-submission.forum.application-list-title\"\r\n                )\r\n              ]\r\n            ),\r\n            10\r\n          );\r\n        }\r\n      }\r\n  });\r\n}\r\n", "import app from 'flarum/forum/app';\r\nimport Notification from \"flarum/components/Notification\";\r\n\r\nexport default class UserSubmissionNotification extends Notification {\r\n  icon(): null {\r\n    return null;\r\n  }\r\n\r\n  href(): string {\r\n    return app.route(\"user.userSubmissionApplication\", {\r\n      username: app.session.user.username(),\r\n    });\r\n  }\r\n\r\n  content(): string {\r\n    this.attrs.notification.subject();\r\n    return app.translator.trans('wusong8899-user-submission.forum.notification-submission-result-title');\r\n  }\r\n\r\n  excerpt(): string | undefined {\r\n    const notification = this.attrs.notification.subject();\r\n    const reviewResult = notification.review_result();\r\n    const submissionId = notification.id();\r\n\r\n    if (reviewResult === 'approved') {\r\n      return app.translator.trans('wusong8899-user-submission.forum.notification-submission-result-success', { id: submissionId });\r\n    } else if (reviewResult === 'rejected') {\r\n      return app.translator.trans('wusong8899-user-submission.forum.notification-submission-result-failed', { id: submissionId });\r\n    }\r\n\r\n    return undefined;\r\n  }\r\n}\r\n", "import app from 'flarum/forum/app';\r\nimport Page from 'flarum/components/Page';\r\nimport IndexPage from 'flarum/components/IndexPage';\r\nimport listItems from 'flarum/common/helpers/listItems';\r\n\r\nimport UserSubmissionApplicationModal from './UserSubmissionApplicationModal';\r\nimport LogInModal from \"flarum/components/LogInModal\";\r\n\r\nexport default class UserSubmissionIndexPage extends Page {\r\n  oninit(vnode: any) {\r\n    super.oninit(vnode);\r\n  }\r\n\r\n  view() {\r\n    return (\r\n      <div className=\"MoneyLeaderboardPage\">\r\n        {IndexPage.prototype.hero()}\r\n\r\n        <div className=\"container\">\r\n          <div className=\"sideNavContainer\">\r\n            <nav className=\"IndexPage-nav sideNav\">\r\n              <ul>{listItems(IndexPage.prototype.sidebarItems().toArray())}</ul>\r\n            </nav>\r\n            <div className=\"user-submission-header\">\r\n              <img\r\n                className=\"user-submission-icon\"\r\n                src=\"https://mutluresim.com/images/2023/03/26/ViOux.png\"\r\n                alt=\"User Submission\"\r\n              />\r\n              &nbsp;{app.translator.trans(\"wusong8899-user-submission.forum.item-header\")}\r\n            </div>\r\n            <div className=\"user-submission-input-container\">\r\n              <div\r\n                className=\"user-submission-input-overlay\"\r\n                onclick={() => this.openModal()}\r\n              />\r\n              <div className=\"Search-input\">\r\n                <input\r\n                  disabled\r\n                  className=\"FormControl\"\r\n                  type=\"search\"\r\n                  placeholder={app.translator.trans('wusong8899-user-submission.forum.item-input-placeholder')}\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  private openModal(): void {\r\n    if (app.session.user) {\r\n      app.modal.show(UserSubmissionApplicationModal);\r\n    } else {\r\n      app.modal.show(LogInModal);\r\n    }\r\n  }\r\n}\r\n", "import app from 'flarum/forum/app';\r\nimport { extend } from 'flarum/extend';\r\nimport NotificationGrid from \"flarum/components/NotificationGrid\";\r\nimport HeaderPrimary from 'flarum/forum/components/HeaderPrimary';\r\nimport UserSubmissionWidget from './components/UserSubmissionWidget';\r\n\r\nimport UserSubmission from \"./model/UserSubmission\";\r\nimport addUserPage from './addUserPage';\r\nimport UserSubmissionNotification from \"./components/UserSubmissionNotification\";\r\nimport UserSubmissionIndexPage from './components/UserSubmissionIndexPage';\r\n\r\napp.initializers.add('wusong8899-user-submission', () => {\r\n  app.routes['userSubmission'] = {\r\n    path: '/userSubmission',\r\n    component: UserSubmissionIndexPage,\r\n  };\r\n\r\n  app.store.models.userSubmissionList = UserSubmission;\r\n  app.notificationComponents.userSubmissionList = UserSubmissionNotification;\r\n\r\n  extend(HeaderPrimary.prototype, 'view', function () {\r\n      const routeName = app.current.get('routeName');\r\n\r\n      if (routeName === 'tags') {\r\n        // Initialize the user submission widget for the tags page\r\n        const widget = new UserSubmissionWidget();\r\n        widget.oncreate({});\r\n      }\r\n  });\r\n\r\n  extend(NotificationGrid.prototype, \"notificationTypes\", function (items) {\r\n    items.add(\"userSubmissionList\", {\r\n      name: \"userSubmissionList\",\r\n      icon: \"fas fa-file-signature\",\r\n      label: app.translator.trans(\r\n        \"wusong8899-user-submission.forum.notification-submission-result\"\r\n      ),\r\n    });\r\n  });\r\n\r\n  addUserPage();\r\n});\r\n\r\n"], "names": ["_UserSubmissionApplicationSubmitSuccessModal", "Modal", "vnode", "app", "<PERSON><PERSON>", "UserSubmissionApplicationSubmitSuccessModal", "_UserSubmissionApplicationModal", "Stream", "e", "result", "UserSubmissionApplicationModal", "_UserSubmissionWidget", "Component", "pollFor<PERSON><PERSON>r", "container", "widgetHtml", "LogInModal", "UserSubmissionWidget", "UserSubmission", "Model", "UserSubmissionApplicationListItem", "itemData", "amount", "id", "platform", "platformAccount", "userAccount", "reviewResult", "reviewResultText", "assignedAt", "reviewedAt", "containerClassName", "UserSubmissionApplicationListPage", "loading", "LoadingIndicator", "results", "offset", "UserSubmissionApplicationPage", "UserPage", "currentUserID", "targetUserID", "addUserPage", "extend", "items", "LinkButton", "UserSubmissionNotification", "Notification", "notification", "submissionId", "UserSubmissionIndexPage", "Page", "IndexPage", "listItems", "HeaderPrimary", "NotificationGrid"], "mappings": "0DAIA,MAAqBA,EAArB,MAAqBA,UAAoDC,CAAM,CAI7E,OAAOC,EAAY,CACjB,MAAM,OAAOA,CAAK,CACpB,CAEA,WAAoB,CAClB,MAAO,cACT,CAEA,OAAgB,CACd,OAAOC,EAAI,WAAW,MAAM,iDAAiD,CAC/E,CAEA,SAAU,CACR,OACE,EAAC,MAAA,CAAI,UAAU,YAAA,IACZ,MAAA,CAAI,UAAU,MAAA,EACb,EAAC,MAAA,CAAI,UAAU,aAAa,MAAM,uBAC/BC,EAAO,UACN,CACE,UAAW,yBACX,QAAS,IAAM,CACb,KAAK,KAAA,CACP,CAAA,EAEFD,EAAI,WAAW,MAAM,mCAAmC,CAAA,CAE5D,CACF,CACF,CAEJ,CACF,EAlCEH,EAAO,8BAAgC,GACvCA,EAAO,4BAA8B,GAFvC,IAAqBK,EAArBL,ECGA,MAAqBM,EAArB,MAAqBA,UAAuCL,CAAM,CAAlE,aAAA,CAAA,MAAA,GAAA,SAAA,EAIE,KAAQ,QAAmB,EAAA,CAM3B,OAAOC,EAAY,CACjB,MAAM,OAAOA,CAAK,EAClB,KAAK,QAAU,GAEf,KAAK,OAASK,EAAO,EAAE,EACvB,KAAK,SAAWA,EAAO,EAAE,EACzB,KAAK,gBAAkBA,EAAO,EAAE,EAChC,KAAK,YAAcA,EAAO,EAAE,CAC9B,CAEA,WAAoB,CAClB,MAAO,cACT,CAEA,OAAgB,CACd,OAAOJ,EAAI,WAAW,MAAM,8CAA8C,CAC5E,CAEA,SAAU,CACR,OACE,EAAC,MAAA,CAAI,UAAU,YAAA,EACb,EAAC,MAAA,CAAI,UAAU,MAAA,EACb,EAAC,MAAA,CAAI,UAAU,aAAa,MAAM,qBAAA,EAC9B,EAAC,MAAA,KACC,EAAC,MAAA,CAAI,UAAU,yBAAA,EAA2BA,EAAI,WAAW,MAAM,4CAA4C,CAAE,EAC7G,EAAC,QAAA,CAAM,KAAK,SAAS,IAAI,IAAI,SAAU,KAAK,QAAS,SAAQ,GAAC,UAAU,cAAc,KAAM,KAAK,MAAA,CAAQ,CAC3G,EAEA,EAAC,MAAA,CAAI,UAAU,yBAAA,EAA2BA,EAAI,WAAW,MAAM,8CAA8C,CAAE,EAC/G,EAAC,QAAA,CAAM,UAAU,MAAM,SAAU,KAAK,QAAS,SAAQ,GAAC,UAAU,cAAc,KAAM,KAAK,QAAA,CAAU,EAErG,EAAC,MAAA,CAAI,UAAU,yBAAA,EAA2BA,EAAI,WAAW,MAAM,qDAAqD,CAAE,EACtH,EAAC,QAAA,CAAM,UAAU,MAAM,SAAU,KAAK,QAAS,SAAQ,GAAC,UAAU,cAAc,KAAM,KAAK,eAAA,CAAiB,EAE5G,EAAC,MAAA,CAAI,UAAU,yBAAA,EAA2BA,EAAI,WAAW,MAAM,qDAAqD,CAAE,EACtH,EAAC,QAAA,CAAM,UAAU,MAAM,SAAU,KAAK,QAAS,SAAQ,GAAC,UAAU,cAAc,KAAM,KAAK,WAAA,CAAa,CAC5G,EAEA,EAAC,MAAA,CAAI,UAAU,aAAa,MAAM,uBAC/BC,EAAO,UACN,CACE,MAAM,kBACN,UAAW,yBACX,KAAM,SACN,QAAS,KAAK,OAAA,EAEhBD,EAAI,WAAW,MAAM,mCAAmC,CAAA,EACxD,IACDC,EAAO,UACN,CACE,MAAM,kBACN,UAAW,SACX,QAAS,KAAK,QACd,QAAS,IAAM,CACb,KAAK,KAAA,CACP,CAAA,EAEFD,EAAI,WAAW,MAAM,uCAAuC,CAAA,CAEhE,CACF,CACF,CAEJ,CAGA,SAASK,EAAgB,CACvBA,EAAE,eAAA,EAEF,KAAK,QAAU,GAEfL,EAAI,MACD,aAAa,oBAAoB,EACjC,KAAK,CACJ,OAAQ,KAAK,OAAA,EACb,SAAU,KAAK,SAAA,EACf,gBAAiB,KAAK,gBAAA,EACtB,YAAa,KAAK,YAAA,CAAY,CAC/B,EACA,KAAMM,GAAW,CAChBN,EAAI,MAAM,YAAYM,CAAM,EAC5B,KAAK,QAAU,GACfN,EAAI,MAAM,KAAKE,CAA2C,CAC5D,CAAC,EACA,MAAM,IAAM,CACX,KAAK,QAAU,EACjB,CAAC,CACL,CACF,EAjGEC,EAAO,8BAAgC,GACvCA,EAAO,4BAA8B,GAFvC,IAAqBI,EAArBJ,ECIA,MAAqBK,EAArB,MAAqBA,UAA6BC,CAAqC,CAKrF,SAASV,EAAY,CACnB,MAAM,SAASA,CAAK,EACpB,KAAK,YAAA,CACP,CAEA,MAAO,CACL,SACG,MAAA,CAAI,UAAU,0BACb,EAAC,MAAA,CAAI,UAAU,wBAAA,EACb,EAAC,MAAA,CACC,UAAU,uBACV,IAAI,oEACJ,IAAI,iBAAA,CAAA,EAEN,EAAC,OAAA,CAAK,UAAU,yBACbC,EAAI,WAAW,MAAM,8CAA8C,CACtE,CACF,EACA,EAAC,MAAA,CAAI,UAAU,iCAAA,EACb,EAAC,MAAA,CACC,UAAU,gCACV,QAAS,KAAK,iBAAiB,KAAK,IAAI,CAAA,CAAA,EAE1C,EAAC,MAAA,CAAI,UAAU,cAAA,EACb,EAAC,QAAA,CACC,SAAQ,GACR,UAAU,cACV,KAAK,SACL,YAAaA,EAAI,WAAW,MAAM,yDAAyD,CAAA,CAAA,CAE/F,CACF,CACF,CAEJ,CAEQ,aAAoB,CAC1B,MAAMU,EAAmB,YAAY,IAAM,CACzC,MAAMC,EAAY,EAAEH,EAAqB,kBAAkB,EAEvDG,EAAU,OAAS,IACrB,cAAcD,CAAgB,EAC9B,KAAK,aAAaC,CAAS,EAE/B,EAAGH,EAAqB,aAAa,CACvC,CAEQ,aAAaG,EAAyB,CAC5C,GAAI,CAACA,EAAU,SAASH,EAAqB,YAAY,EAAG,CAC1D,MAAMI,EAAa,KAAK,iBAAA,EACxB,EAAEA,CAAU,EAAE,YAAYD,CAAS,EACnCA,EAAU,SAASH,EAAqB,YAAY,EACpD,KAAK,oBAAA,CACP,CACF,CAEQ,kBAA2B,CACjC,MAAO;AAAA;AAAA,yHAE8GR,EAAI,WAAW,MAAM,8CAA8C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,+GAK9EA,EAAI,WAAW,MAAM,yDAAyD,CAAC;AAAA;AAAA;AAAA,KAI5L,CAEQ,qBAA4B,CAClC,EAAE,iCAAiC,EAAE,GAAG,QAAS,KAAK,iBAAiB,KAAK,IAAI,CAAC,CACnF,CAEQ,kBAAyB,CAC3BA,EAAI,QAAQ,KACdA,EAAI,MAAM,KAAKO,CAA8B,EAE7CP,EAAI,MAAM,KAAKa,CAAU,CAE7B,CACF,EArFEL,EAAwB,cAAgB,GACxCA,EAAwB,aAAe,4BACvCA,EAAwB,mBAAqB,sBAH/C,IAAqBM,EAArBN,ECPA,MAAqBO,UAAuBC,CAAgC,CAa5E,CAEA,OAAO,OAAOD,EAAe,UAAW,CACtC,GAAIC,EAAM,UAAU,IAAI,EACxB,OAAQA,EAAM,UAAU,QAAQ,EAChC,SAAUA,EAAM,UAAU,UAAU,EACpC,iBAAkBA,EAAM,UAAU,kBAAkB,EACpD,aAAcA,EAAM,UAAU,cAAc,EAC5C,mBAAoBA,EAAM,UAAU,oBAAoB,EACxD,eAAgBA,EAAM,UAAU,gBAAgB,EAChD,cAAeA,EAAM,UAAU,eAAe,EAC9C,YAAaA,EAAM,UAAU,aAAa,EAC1C,YAAaA,EAAM,UAAU,aAAa,EAC1C,SAAUA,EAAM,OAAO,UAAU,EACjC,WAAYA,EAAM,OAAO,YAAY,CACvC,CAAC,ECvBD,MAAqBC,UAA0CR,CAAkD,CAE/G,MAAO,CACL,KAAM,CAAE,SAAAS,GAAa,KAAK,MAEpBC,EAASD,EAAS,OAAA,EAClBE,EAAKF,EAAS,GAAA,EACdG,EAAWH,EAAS,SAAA,EACpBI,EAAkBJ,EAAS,iBAAA,EAC3BK,EAAcL,EAAS,aAAA,EACvBM,EAAeN,EAAS,cAAA,EACxBO,EAAmBzB,EAAI,WAAW,MACtCwB,IAAiB,WACb,wDACA,wDAAA,EAEAE,EAAaR,EAAS,YAAA,EACtBS,EAAaT,EAAS,YAAA,EAC5B,IAAIU,EAAqB,sCAEzB,OAAID,IAAe,KACjBC,GAAsB,qCAElBJ,IAAiB,WACnBI,GAAsB,oCAEtBA,GAAsB,oCAKxB,EAAC,MAAA,CAAI,UAAWA,CAAA,IACb,MAAA,KACC,EAAC,IAAA,KAAG5B,EAAI,WAAW,MAAM,wCAAwC,EAAE,IAAE,EACpEoB,EAAG,MACJ,EAAC,IAAA,KAAGpB,EAAI,WAAW,MAAM,gDAAgD,EAAE,IAAE,EAC5E0B,CACH,EACA,EAAC,MAAA,KACC,EAAC,IAAA,KAAG1B,EAAI,WAAW,MAAM,4CAA4C,EAAE,IAAE,EACxEmB,EAAO,QACP,IAAA,KAAGnB,EAAI,WAAW,MAAM,8CAA8C,EAAE,IAAE,EAC1EqB,EAAS,MACV,EAAC,IAAA,KAAGrB,EAAI,WAAW,MAAM,qDAAqD,EAAE,IAAE,EACjFsB,EAAgB,MACjB,EAAC,SAAGtB,EAAI,WAAW,MAAM,iDAAiD,EAAE,IAAE,EAC7EuB,CACH,EACCI,GACC,EAAC,MAAA,KACC,EAAC,SAAG3B,EAAI,WAAW,MAAM,kDAAkD,EAAE,IAAE,EAC9EwB,IAAiB,YAChB,EAAC,OAAA,CAAK,MAAM,WAAA,EAAaC,EAAiB,KAAa,EAExDD,IAAiB,YAChB,EAAC,OAAA,CAAK,MAAM,aAAA,EAAeC,EAAiB,KAAa,EAE3D,EAAC,IAAA,KAAGzB,EAAI,WAAW,MAAM,8CAA8C,EAAE,IAAE,EAC1E2B,CACH,EAED,CAACA,GACA,EAAC,MAAA,KACC,EAAC,IAAA,KAAG3B,EAAI,WAAW,MAAM,kDAAkD,EAAE,IAAE,EAC/E,EAAC,OAAA,CAAK,MAAM,YAAA,EAAcA,EAAI,WAAW,MAAM,0DAA0D,CAAE,CAC7G,EAEF,EAAC,MAAA,CAAI,MAAM,sCAAA,EACRC,EAAO,UAAU,CAChB,UAAW,sCACX,KAAM,eACN,QAAS,IAAM,KAAK,WAAWiB,CAAQ,CAAA,EACtClB,EAAI,WAAW,MAAM,oDAAoD,CAAC,CAC/E,CACF,CAEJ,CAEQ,WAAWkB,EAAoC,CACjD,QAAQlB,EAAI,WAAW,MAAM,iDAAiD,CAAC,GACjFA,EAAI,QAAQ,CACV,OAAQ,SACR,IAAKA,EAAI,MAAM,UAAU,QAAQ,EAAI,uBAAyBkB,EAAS,GAAA,CAAG,CAC3E,EACE,KAAK,IAAM,CAEV,OAAO,SAAS,OAAA,CAClB,CAAC,EACA,MAAM,IAAM,CACX,MAAMlB,EAAI,WAAW,MAAM,gDAAgD,CAAC,CAC9E,CAAC,CAEP,CACF,CCxFA,MAAqB6B,UAA0CpB,CAAkD,CAAjH,aAAA,CAAA,MAAA,GAAA,SAAA,EACE,KAAQ,QAAmB,GAC3B,KAAQ,YAAuB,GAC/B,KAAQ,mBAA2C,CAAA,CAAC,CAEpD,OAAOV,EAAY,CACjB,MAAM,OAAOA,CAAK,EAClB,KAAK,QAAU,GACf,KAAK,YAAc,GACnB,KAAK,mBAAqB,CAAA,EAC1B,KAAK,YAAA,CACP,CAEA,MAAO,CACL,IAAI+B,EAEJ,OAAI,KAAK,UACPA,EAAUC,EAAiB,UAAU,CAAE,KAAM,QAAS,GAItD,EAAC,MAAA,KACC,EAAC,KAAA,CAAG,MAAM,sCACP,KAAK,mBAAmB,IAAKb,GAE1B,EAAC,KAAA,CAAG,IAAKA,EAAS,GAAA,EAAM,MAAM,4CAAA,EAC3BD,EAAkC,UAAU,CAAE,SAAAC,CAAA,CAAU,CAC3D,CAEH,CACH,EAEC,CAAC,KAAK,SAAW,KAAK,mBAAmB,SAAW,GACnD,EAAC,MAAA,KACC,EAAC,MAAA,CAAI,MAAM,uFAAA,EACRlB,EAAI,WAAW,MAAM,2CAA2C,CACnE,CACF,EAGD,CAAC8B,GAAW,KAAK,eAAA,GAChB,EAAC,MAAA,CAAI,MAAM,gCAAA,EACT,EAAC7B,EAAA,CAAO,UAAW,yBAA0B,SAAU,KAAK,QAAS,QAAS,KAAK,QAAS,QAAS,IAAM,KAAK,SAAA,CAAS,EACtHD,EAAI,WAAW,MAAM,+CAA+C,CACvE,CACF,EAGD8B,GAAW,EAAC,MAAA,CAAI,UAAU,yBAAA,EAA2BA,CAAQ,CAEhE,CAEJ,CAEQ,gBAA0B,CAChC,OAAO,KAAK,WACd,CAEQ,UAAiB,CACvB,KAAK,QAAU,GACf,KAAK,YAAY,KAAK,mBAAmB,MAAM,CACjD,CAEQ,aAAaE,EAAqD,CACxE,YAAK,YAAc,CAAC,CAAEA,EAAgB,SAAS,OAAO,KACtD,KAAK,mBAAmB,KAAK,GAAGA,CAAO,EAEvC,KAAK,QAAU,GACf,EAAE,OAAA,EAEKA,CACT,CAEQ,YAAYC,EAAiB,EAAkC,CACrE,OAAOjC,EAAI,MACR,KAAK,gCAAiC,CACrC,KAAM,CACJ,OAAAiC,CAAA,CACF,CACD,EACA,MAAM,IAAM,CAAA,CAAE,EACd,KAAK,KAAK,aAAa,KAAK,IAAI,CAAC,CACtC,CAEF,CC9FA,MAAqBC,UAAsCC,CAAS,CAClE,OAAOpC,EAAY,CACjB,MAAM,OAAOA,CAAK,EAClB,KAAK,SAAS,EAAE,MAAM,MAAM,UAAU,CAAC,CACzC,CAEA,SAAU,CACR,GAAIC,EAAI,QAAQ,KAAM,CACpB,MAAMoC,EAAgBpC,EAAI,QAAQ,KAAK,GAAA,EACjCqC,EAAe,KAAK,KAAK,GAAA,EAE/B,GAAID,IAAkBC,EACpB,OACE,EAAC,MAAA,KACER,EAAkC,UAAU,CAC3C,OAAQ,CACN,KAAM,KAAK,IAAA,CACb,CACD,CACH,CAGN,CAEA,OAAO,IACT,CACF,CCvBA,SAAwBS,GAAoB,CAC1CtC,EAAI,OAAO,gCAAgC,EAAI,CAC7C,KAAM,yCACN,UAAWkC,CAAA,EAGbK,EAAAA,OAAOJ,EAAS,UAAW,WAAY,SAAUK,EAAO,CACpD,GAAIxC,EAAI,QAAQ,KAAM,CACpB,MAAMoC,EAAgBpC,EAAI,QAAQ,KAAK,GAAA,EACjCqC,EAAe,KAAK,KAAK,GAAA,EAE3BD,IAAkBC,GACpBG,EAAM,IACJ,4BACAC,EAAW,UAAU,CACjB,KAAMzC,EAAI,MAAM,iCAAkC,CAChD,SAAU,KAAK,KAAK,SAAA,CAAS,CAC9B,EACD,KAAM,uBAAA,EAER,CACEA,EAAI,WAAW,MACb,yDAAA,CACF,CACF,EAEF,EAAA,CAGN,CACJ,CAAC,CACH,CCnCA,MAAqB0C,UAAmCC,CAAa,CACnE,MAAa,CACX,OAAO,IACT,CAEA,MAAe,CACb,OAAO3C,EAAI,MAAM,iCAAkC,CACjD,SAAUA,EAAI,QAAQ,KAAK,SAAA,CAAS,CACrC,CACH,CAEA,SAAkB,CAChB,YAAK,MAAM,aAAa,QAAA,EACjBA,EAAI,WAAW,MAAM,uEAAuE,CACrG,CAEA,SAA8B,CAC5B,MAAM4C,EAAe,KAAK,MAAM,aAAa,QAAA,EACvCpB,EAAeoB,EAAa,cAAA,EAC5BC,EAAeD,EAAa,GAAA,EAElC,GAAIpB,IAAiB,WACnB,OAAOxB,EAAI,WAAW,MAAM,0EAA2E,CAAE,GAAI6C,EAAc,EAC7H,GAAWrB,IAAiB,WAC1B,OAAOxB,EAAI,WAAW,MAAM,yEAA0E,CAAE,GAAI6C,EAAc,CAI9H,CACF,CCxBA,MAAqBC,UAAgCC,CAAK,CACxD,OAAOhD,EAAY,CACjB,MAAM,OAAOA,CAAK,CACpB,CAEA,MAAO,CACL,OACE,EAAC,MAAA,CAAI,UAAU,sBAAA,EACZiD,EAAU,UAAU,KAAA,EAErB,EAAC,OAAI,UAAU,WAAA,EACb,EAAC,MAAA,CAAI,UAAU,oBACb,EAAC,MAAA,CAAI,UAAU,yBACb,EAAC,KAAA,KAAIC,EAAUD,EAAU,UAAU,aAAA,EAAe,QAAA,CAAS,CAAE,CAC/D,EACA,EAAC,MAAA,CAAI,UAAU,wBAAA,EACb,EAAC,MAAA,CACC,UAAU,uBACV,IAAI,qDACJ,IAAI,iBAAA,CAAA,EACJ,IACKhD,EAAI,WAAW,MAAM,8CAA8C,CAC5E,EACA,EAAC,MAAA,CAAI,UAAU,iCAAA,EACb,EAAC,MAAA,CACC,UAAU,gCACV,QAAS,IAAM,KAAK,UAAA,CAAU,CAAA,EAEhC,EAAC,MAAA,CAAI,UAAU,cAAA,EACb,EAAC,QAAA,CACC,SAAQ,GACR,UAAU,cACV,KAAK,SACL,YAAaA,EAAI,WAAW,MAAM,yDAAyD,CAAA,CAAA,CAE/F,CACF,CACF,CACF,CACF,CAEJ,CAEQ,WAAkB,CACpBA,EAAI,QAAQ,KACdA,EAAI,MAAM,KAAKO,CAA8B,EAE7CP,EAAI,MAAM,KAAKa,CAAU,CAE7B,CACF,CC/CAb,EAAI,aAAa,IAAI,6BAA8B,IAAM,CACvDA,EAAI,OAAO,eAAoB,CAC7B,KAAM,kBACN,UAAW8C,CAAA,EAGb9C,EAAI,MAAM,OAAO,mBAAqBe,EACtCf,EAAI,uBAAuB,mBAAqB0C,EAEhDH,EAAAA,OAAOW,EAAc,UAAW,OAAQ,UAAY,CAC9BlD,EAAI,QAAQ,IAAI,WAAW,IAE3B,QAED,IAAIc,EAAA,EACZ,SAAS,EAAE,CAExB,CAAC,EAEDyB,EAAAA,OAAOY,EAAiB,UAAW,oBAAqB,SAAUX,EAAO,CACvEA,EAAM,IAAI,qBAAsB,CAC9B,KAAM,qBACN,KAAM,wBACN,MAAOxC,EAAI,WAAW,MACpB,iEAAA,CACF,CACD,CACH,CAAC,EAEDsC,EAAA,CACF,CAAC"}