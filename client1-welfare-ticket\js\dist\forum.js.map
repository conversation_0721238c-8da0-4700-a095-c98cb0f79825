{"version": 3, "file": "forum.js", "mappings": "MACA,IAAIA,EAAsB,CCA1BA,EAAyBC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,WAC7B,IAAOF,EAAiB,QACxB,IAAM,EAEP,OADAD,EAAoBI,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GCLRF,EAAwB,CAACM,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXP,EAAoBS,EAAEF,EAAYC,KAASR,EAAoBS,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3ER,EAAwB,CAACc,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFf,EAAyBM,IACH,oBAAXa,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeL,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeL,EAAS,aAAc,CAAEe,OAAO,M,+BCLvD,MAAM,EAA+BC,OAAOC,KAAKC,OAAO,a,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAe,OCA1D,EAA+BF,OAAOC,KAAKC,OAAO,wB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,yB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,uB,aCAzC,SAASC,EAAgBhB,EAAGiB,GAMzC,OALAD,EAAkBf,OAAOiB,gBAAkB,SAAyBlB,EAAGiB,GAErE,OADAjB,EAAEmB,UAAYF,EACPjB,GAGFgB,EAAgBhB,EAAGiB,GCLb,SAASG,EAAeC,EAAUC,GAC/CD,EAASd,UAAYN,OAAOsB,OAAOD,EAAWf,WAC9Cc,EAASd,UAAUiB,YAAcH,EACjCH,EAAeG,EAAUC,GCJ3B,MAAM,EAA+BT,OAAOC,KAAKC,OAAkB,U,aCAnE,MAAM,EAA+BF,OAAOC,KAAKC,OAAY,I,aCA7D,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,+B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,qB,aCAnBF,OAAOC,KAAKC,OAAO,mB,ICEnCU,EAAAA,SAAAA,G,oFACnBC,KAAA,WACE,IAAOC,EAAwBC,KAAKC,MAA7BF,qBAEDG,EADyBH,EAAqBI,aAAcC,WACpBC,UAAU,GAClDC,EAAYC,IAAIC,MAAMC,UAAU,8BAAgC,UAEhEC,EAAoBX,EAAqBY,cACzCC,EAAsBF,EAAkBG,SACxCC,EAAgD,OAAtBF,EAA2B,GAAGG,KAAKC,MAAMJ,GACnEK,EAAwBF,KAAKC,MAAMN,EAAkBQ,YAErDC,EAAyC,CAC7C,EAAEZ,IAAIa,WAAWC,MAAM,6DAA6D,CAACC,MAAM,EAAEC,WAAWN,EAAsBO,iBAC9H,EAAEjB,IAAIa,WAAWC,MAAM,6DAA6D,CAACC,MAAM,EAAEC,WAAWN,EAAsBQ,iBAC9H,EAAElB,IAAIa,WAAWC,MAAM,6DAA6D,CAACC,MAAM,EAAEC,WAAWN,EAAsBS,kBAG1HC,EAA0B5B,EAAqB6B,KAC/CC,EAA2B9B,EAAqB+B,MAChDC,EAAgChC,EAAqBiC,YACrDC,EAAoC3B,EAAU4B,QAAQ,UAAWH,GACjEI,EAA+B7B,EAAU4B,QAAQ,UAAWL,GAC5DO,EAA8BC,SAAStC,EAAqBuC,UAG9DC,EAA8B,GAC9BC,EAAqB,GAEzB,IAAc,IAJoC,WAAjCzC,EAAqB0C,WAIlB,CAClB,IAAMC,EAA+B3B,KAAKC,MAAMjB,EAAqB0C,WAC/DE,EAAmBD,EAA6BE,OAClDC,EAAW,EAiBf,OAfiC,IAA9BT,GACDG,EAA8BhC,IAAIa,WAAWC,MAAM,uDACjDmB,EAAqB,cAEY,IAAhCT,GACDQ,EAA8BhC,IAAIa,WAAWC,MAAM,qDACnDmB,EAAqB,cAErBD,EAA8BhC,IAAIa,WAAWC,MAAM,sDAAsD,CAACwB,SAASF,EAAiBG,MAAMb,IAC1IO,EAAqB,eAOvB,SAAKO,UAAU,iCACb,SAAKC,MAAM,iEACT,WAAIzC,IAAIa,WAAWC,MAAM,oDAAoD,CAACO,GAAG1B,MAEnF,aACE,SAAK8C,MAAM,kCACc,OAAtBpC,GAA8BE,EAAwBmC,KAAI,SAACC,GAC1D,IAAIC,EAAQT,EAA6BU,QAAQf,SAASa,IACtDG,EAAY,qDAOhB,OALGtB,EAA8B,IAAc,IAAToB,GAAcN,EAASF,IAC3DU,GAAW,6DACXR,KAIA,SAAKG,MAAOK,GACV,SAAKC,MAAM,wBAAwBN,MApBlB,4DAoBkDE,SAM7E,SAAKF,MAAM,qBACT,WAAIzC,IAAIa,WAAWC,MAAM,8CAAzB,MACCM,EAFH,MAGE,WAAIpB,IAAIa,WAAWC,MAAM,+CAAzB,MACCc,EAJH,MAKE,WAAI5B,IAAIa,WAAWC,MAAM,mDAAzB,MACA,SAAK2B,MAAM,8FAA8FN,EAA6Ba,KAAK,OAE7I,SAAKP,MAAM,qBACT,WAAIzC,IAAIa,WAAWC,MAAM,kDAAzB,MACCF,EAAuCwB,GAF1C,MAGE,WAAIpC,IAAIa,WAAWC,MAAM,6CAAzB,MACA,UAAM2B,MAAOR,GACVD,KAMT,GAAiC,IAA9BH,EACDG,EAA8BhC,IAAIa,WAAWC,MAAM,uDACjDmB,EAAqB,iBACpB,CACH,IAAMgB,EAAczB,EAA8BF,EAC5C4B,EAAkBnD,EAAU4B,QAAQ,UAAWwB,KAAKC,IAAIH,IAE3DA,GAAa,GACdhB,EAAqB,YACrBD,EAA8BhC,IAAIa,WAAWC,MAAM,mEAAmE,CAACyB,MAAMW,MAE7HjB,EAAqB,cACrBD,EAA8BhC,IAAIa,WAAWC,MAAM,mEAAmE,CAACyB,MAAMW,KAIjI,OACE,SAAKV,UAAU,iCACb,SAAKC,MAAM,iEACT,WAAIzC,IAAIa,WAAWC,MAAM,oDAAoD,CAACO,GAAG1B,MAEnF,aACE,SAAKoD,MAAM,6BAA6BN,MAAM,kDAC5C,WAAIzC,IAAIa,WAAWC,MAAM,wDAG7B,SAAK2B,MAAM,qBACT,WAAIzC,IAAIa,WAAWC,MAAM,sDAAzB,MACCc,GAGH,SAAKa,MAAM,qBACT,WAAIzC,IAAIa,WAAWC,MAAM,2DAAzB,MACA,UAAM2B,MAAOR,GACVD,M,EAQbqB,eAAA,SAAeC,EAAQC,GACrB,IAAIC,EAASL,KAAKM,IAAI,GAAIF,GAC1B,OAAOJ,KAAKO,MAAMJ,EAASE,GAAUA,G,EAxIpBlE,CAA6CqE,KCM7CC,EAAAA,SAAAA,G,oFACnBC,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACbrE,KAAKsE,SAAU,EACftE,KAAKuE,aAAc,EACnBvE,KAAKD,qBAAuB,GAC5BC,KAAKwE,4BAA8B,KACnCxE,KAAKyE,KAAOzE,KAAKC,MAAMyE,OAAOD,KAC9BzE,KAAK2E,e,EAGP7E,KAAA,WAAO,IACDwE,EAEAM,EAAsBC,EAHrB,OAECvE,EAAYC,IAAAA,MAAAA,UAAoB,8BAAgC,UAYtE,OATGP,KAAKsE,SACNA,EAAUQ,IAAAA,UAA2B,CAAEC,KAAM,UAC7CH,EAAwBtE,EAAU4B,QAAQ,UAAW,KACrD2C,EAAuBvE,EAAU4B,QAAQ,UAAW,OAEpD0C,EAAwBtE,EAAU4B,QAAQ,UAAWlC,KAAKwE,4BAA4BQ,YACtFH,EAAuBvE,EAAU4B,QAAQ,UAAWlC,KAAKwE,4BAA4BS,aAIrF,aACE,SAAKjC,MAAM,oCACT,SAAKA,MAAM,+EACRzC,IAAAA,WAAAA,MAAqB,2DAExB,EAAC,IAAD,CAAQwC,UAAW,yBAA0BmC,QAAS,kBAAMC,EAAEC,MAAMC,IAAI9E,IAAAA,MAAU,oBAC/EA,IAAAA,WAAAA,MAAqB,6CAG1B,SAAKyC,MAAM,iDACRzC,IAAAA,WAAAA,MAAqB,8CAA8C,CAClE+E,kBAAkBV,EAClBW,iBAAiBV,KAGrB,QAAI7B,MAAM,kEACPhD,KAAKD,qBAAqBkD,KAAI,SAAClD,GAC9B,OACE,QAAIiD,MAAM,qBAAqB,UAASjD,EAAqB6B,MAC1D/B,EAAqC2F,UAAU,CAAEzF,qBAAAA,UAMxDC,KAAKsE,SAA8C,IAAnCtE,KAAKD,qBAAqB6C,QAC1C,aACE,SAAKI,MAAM,uGAAuGzC,IAAAA,WAAAA,MAAqB,uDAIzI+D,GAAWtE,KAAKyF,kBAChB,SAAKzC,MAAM,kCACT,EAAC,IAAD,CAAQD,UAAW,yBAA0B2C,SAAU1F,KAAKsE,QAASA,QAAStE,KAAKsE,QAASY,QAAS,kBAAM,EAAKS,aAC7GpF,IAAAA,WAAAA,MAAqB,0DAK3B+D,GAAW,SAAKvB,UAAU,0BAA0BuB,K,EAK3DqB,SAAA,WACE3F,KAAKsE,SAAU,EACftE,KAAK2E,YAAY3E,KAAKD,qBAAqB6C,S,EAG7CgD,aAAA,SAAaC,GAAS,WAIpB,OAHA7F,KAAKuE,cAAgBsB,EAAQC,QAAQC,SAAWF,EAAQC,QAAQC,MAAMC,KACtE,GAAGC,KAAKC,MAAMlG,KAAKD,qBAAsB8F,GAElCtF,IAAAA,MAAAA,KACC,uCADD,OAEE,eACN4F,MAAK,SAACC,GAKL,OAJA,EAAK5B,4BAA8B4B,EAAc,GACjD,EAAK9B,SAAU,EACfa,EAAEkB,SAEKR,M,EAKbJ,eAAA,WACE,OAAOzF,KAAKuE,a,EAGdI,YAAA,SAAY2B,GACV,YADsB,IAAZA,IAAAA,EAAS,GACZ/F,IAAAA,MAAAA,KACC,wBAAyB,CAC7BgG,OAAQ,CACN9B,KAAMzE,KAAKyE,KAAK7C,MAElB4E,KAAM,CACJF,OAAAA,KANC,OASE,eACNH,KAAKnG,KAAK4F,aAAaa,KAAKzG,Q,EA3GdmE,CAAyCD,KCLzCwC,EAAAA,SAAAA,G,oFACnBtC,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACbrE,KAAK2G,SAASxB,EAAEC,MAAMwB,MAAM,c,EAG9BC,QAAA,WACE,OACE,SAAK9D,UAAU,oCACZoB,EAAiCqB,UAAU,CAC1Cd,OAAQ,CACND,KAAMzE,KAAKyE,U,EAXFiC,CAAoCI,KCHzD,MAAM,EAA+B7H,OAAOC,KAAKC,OAAc,M,aCE1C4H,EAAAA,SAAAA,G,kEAAAA,CAAsBC,KAC3C3I,OAAO4I,OAAOF,EAAcpI,UAAW,CACrCiD,GAAIoF,IAAAA,UAAgB,MACpBE,MAAOF,IAAAA,UAAgB,SACvBG,KAAMH,IAAAA,UAAgB,QACtBI,SAAUJ,IAAAA,UAAgB,YAC1BK,MAAOL,IAAAA,UAAgB,SACvBM,MAAON,IAAAA,UAAgB,SACvBO,KAAMP,IAAAA,UAAgB,QACtBQ,KAAMR,IAAAA,UAAgB,QACtBS,gBAAiBT,IAAAA,UAAgB,mBACjCU,UAAWV,IAAAA,UAAgB,aAC3BW,UAAWX,IAAAA,UAAgB,aAC3BnG,OAAQmG,IAAAA,UAAgB,UACxB9F,SAAU8F,IAAAA,UAAgB,YAC1BY,WAAYZ,IAAAA,UAAgB,eAC5Ba,UAAWb,IAAAA,UAAgB,aAC3Bc,WAAYd,IAAAA,OAAa,gB,ICjBNe,EAAAA,SAAAA,G,kEAAAA,CAA8Bf,KACnD3I,OAAO4I,OAAOc,EAAsBpJ,UAAW,CAC7CiD,GAAIoF,IAAAA,UAAgB,MACpBE,MAAOF,IAAAA,UAAgB,SACvB7G,WAAY6G,IAAAA,UAAgB,cAC5BgB,QAAShB,IAAAA,UAAgB,WACzBlF,IAAKkF,IAAAA,UAAgB,OACrBzF,WAAYyF,IAAAA,UAAgB,cAC5BvE,QAASuE,IAAAA,UAAgB,WACzBhF,UAAWgF,IAAAA,UAAgB,aAC3B1E,OAAQ0E,IAAAA,UAAgB,UACxBiB,YAAajB,IAAAA,UAAgB,eAE7BrG,YAAaqG,IAAAA,OAAa,iB,ICbPkB,EAAAA,SAAAA,G,kEAAAA,CAAmClB,KACxD3I,OAAO4I,OAAOiB,EAA2BvJ,UAAW,CAClDwJ,qBAAsBnB,IAAAA,UAAgB,wBACtCoB,gBAAiBpB,IAAAA,UAAgB,qB,ICHdqB,EAAAA,SAAAA,G,kEAAAA,CAAqCrB,KAC1D3I,OAAO4I,OAAOoB,EAA6B1J,UAAW,CACpDqG,SAAUgC,IAAAA,UAAgB,YAC1B/B,SAAU+B,IAAAA,UAAgB,cCL5B,MAAM,EAA+B/H,OAAOC,KAAKC,OAAO,mB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,4B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,oB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,2B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,4B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,gB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,2B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,4B,aCInCmJ,EAAAA,SAAAA,G,oFAGnBlE,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,I,EAGftB,UAAA,WACE,MAAO,gB,EAGTmE,MAAA,WACE,OAAO3G,IAAAA,WAAAA,MAAqB,wD,EAG9BsG,QAAA,WACE,MAAO,CACL,SAAK9D,UAAU,cACb,SAAKC,MAAM,qBACNuF,IAAAA,UAAiB,CACdvF,MAAM,aACND,UAAW,yBACXmC,QAAS,WACPsD,SAASC,WAGblI,IAAAA,WAAAA,MAAqB,uD,EA1Bd+H,CAAkCI,KAAlCJ,EACZK,eAAgB,E,ICGJC,EAAAA,SAAAA,G,oFAGnBxE,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACbrE,KAAKU,kBAAoBV,KAAKC,MAAMS,kBACpCV,KAAKM,UAAYC,IAAAA,MAAAA,UAAoB,8BAAgC,UACrEP,KAAK6I,aAAetI,IAAAA,QAAAA,KAAAA,UAA2B,SAE/C,IAAMuI,EAAyB9I,KAAKU,kBAAkBkB,KAAMxB,WAC5DJ,KAAKE,gBAAkB4I,EAAsBzI,UAAU,GACvDL,KAAK+I,iBAAmB,EAExB,IAAM9H,EAAwBF,KAAKC,MAAMhB,KAAKU,kBAAkBQ,YAEhElB,KAAKmB,uCAAyC,CAC5C,EAAEZ,IAAAA,WAAAA,MAAqB,6DACvB,EAAEA,IAAAA,WAAAA,MAAqB,6DAA6D,CAACe,MAAM,EAAEC,WAAWN,EAAsBO,iBAC9H,EAAEjB,IAAAA,WAAAA,MAAqB,6DAA6D,CAACe,MAAM,EAAEC,WAAWN,EAAsBQ,iBAC9H,EAAElB,IAAAA,WAAAA,MAAqB,6DAA6D,CAACe,MAAM,EAAEC,WAAWN,EAAsBS,kBAGhI1B,KAAKgJ,kBAAoB,CACvBC,MAAMC,GAAAA,CAAO,GACbC,MAAMD,GAAAA,CAAO,GACbE,MAAMF,GAAAA,CAAO,K,EAKjBnG,UAAA,WACE,MAAO,gB,EAGTmE,MAAA,WACE,OAAO3G,IAAAA,WAAAA,MAAqB,oDAAoD,CAACqB,GAAG5B,KAAKE,mB,EAG3F2G,QAAA,WAAU,WACFwC,EAAgBrJ,KAAKM,UAAU4B,QAAQ,UAAWlC,KAAK6I,cAGzDI,GAFkBjJ,KAAKM,UAAU4B,QAAQ,UAAW,GAE5ClC,KAAKgJ,kBAAL,SACRG,EAAQnJ,KAAKgJ,kBAAL,QACRI,EAAQpJ,KAAKgJ,kBAAL,QAEZ,OACE,SAAKjG,UAAU,cACb,SAAKA,UAAU,QACb,SAAKA,UAAU,cACb,eAAQxC,IAAAA,WAAAA,MAAqB,qDAC7B,EAAC,IAAD,CAAQyC,MAAM,eAAehE,MAAOiK,EAAOK,QAAS,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAIC,gBAAgB,SAASC,SAAU,SAACC,GAAO,EAAKC,gBAAgBD,EAAE,MACjJ,EAAC,IAAD,CAAQzG,MAAM,gCAAgChE,MAAOmK,EAAOG,QAAS,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAIC,gBAAgB,SAASC,SAAU,SAACC,GAAO,EAAKC,gBAAgBD,EAAE,MAClK,EAAC,IAAD,CAAQzG,MAAM,gCAAgChE,MAAOoK,EAAOE,QAAS,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAIC,gBAAgB,SAASC,SAAU,SAACC,GAAO,EAAKC,gBAAgBD,EAAE,MAClK,SAAKzG,MAAM,oBAAoBzC,IAAAA,WAAAA,MAAqB,gDACpD,SAAKyC,MAAM,oBAAmB,WAAO2G,UAAQ,EAAC/H,GAAG,kBAAkBmB,UAAU,cAAcwE,KAAK,SAASqC,KAAK,OAAOC,IAAI,IAAIC,IAAK9J,KAAK6I,aAAcW,SAAU,kBAAM,EAAKO,kBAAkBC,QAAS,kBAAM,EAAKD,qBAChN,SAAK/G,MAAM,oBAAoBzC,IAAAA,WAAAA,MAAqB,sDAAuD8I,GAC3G,SAAKrG,MAAM,oBAAoBzC,IAAAA,WAAAA,MAAqB,mEAAmE,UAAMqB,GAAG,kCAAkCrB,IAAAA,WAAAA,MAAqB,gEAEzL,SAAKwC,UAAU,aAAaC,MAAM,uBAC/BuF,IAAAA,UACC,CACExF,UAAW,yBACXwE,KAAM,SACNjD,QAAStE,KAAKsE,SAEhB/D,IAAAA,WAAAA,MAAqB,mDAPzB,IASGgI,IAAAA,UACC,CACExF,UAAW,mCACXuB,QAAStE,KAAKsE,QACdY,QAAS,WACP,EAAK+E,SAGT1J,IAAAA,WAAAA,MAAqB,2D,EAQjCqD,eAAA,SAAeC,EAAQC,GACrB,IAAIC,EAASL,KAAKM,IAAI,GAAIF,GAC1B,OAAOJ,KAAKO,MAAMJ,EAASE,GAAUA,G,EAGvC2F,gBAAA,SAAgB1K,EAAM4C,GACpB5B,KAAKgJ,kBAAkB,OAAOpH,GAAI5C,GAGlC,IADA,IAAIkL,EAAoB,EAChBC,EAAE,EAAEA,GAAGnK,KAAK+I,iBAAiBoB,IACe,IAA/C9H,SAASrC,KAAKgJ,kBAAkB,OAAOmB,OACxCD,IAIJE,EAAE,mCAAmCC,KAAMrK,KAAKmB,uCAAuC+I,GAAoB3G,KAAK,M,EAGlHwG,eAAA,WAEE,IAAIO,EAAkBtK,KAAK4D,eAAewG,EAAE,oBAAoBG,MAAM,GAChEC,EAAiBxK,KAAK6I,aAEzByB,EAAgBE,GACjBJ,EAAE,oBAAoBG,IAAIC,I,EAI9BC,SAAA,SAAShB,GAAG,WACVA,EAAEiB,iBAMF,IAJA,IAAMC,EAAkB3K,KAAK4D,eAAewG,EAAE,oBAAoBG,MAAM,GAClErK,EAAkBF,KAAKE,gBACvB0K,EAAuB,GAErBT,EAAE,EAAEA,GAAGnK,KAAK+I,iBAAiBoB,IAAI,CACvC,IAAMU,EAAyBxI,SAASrC,KAAKgJ,kBAAkB,OAAOmB,MAE1C,IAAzBU,GAGDD,EAAqB3E,KAAK4E,EAAuB,GAIrD,GAAiC,IAA9BD,EAAqBhI,OAKxB,GAAqB,IAAlB+H,EAAH,CAKA,IAAMG,EAAsB,CAC1B3K,WAAWD,EACX4B,IAAI6I,EACJpD,KAAK,EACL9E,QAAQmI,GAGV5K,KAAKsE,SAAU,EAEf/D,IAAAA,MAAAA,aACgB,yBACbwK,KAAKD,GACL3E,MACC,SAAC6E,GACCzK,IAAAA,MAAAA,YAAsByK,GACtBzK,IAAAA,MAAAA,KAAe+H,GACf/H,IAAAA,QAAAA,KAAAA,KAAAA,WAAAA,OAAwCoK,EACxC,EAAKrG,SAAU,KARrB,OAWS,SAACmF,GACN,EAAKnF,SAAU,UAzBjB/D,IAAAA,OAAAA,KAAgB0K,IAAO,CAAC1D,KAAM,SAAUhH,IAAAA,WAAAA,MAAqB,0DAL7DA,IAAAA,OAAAA,KAAgB0K,IAAO,CAAC1D,KAAM,SAAUhH,IAAAA,WAAAA,MAAqB,kE,EAlI9CqI,CAAyCF,KAAzCE,EACZD,eAAgB,E,ICLJuC,EAAAA,SAAAA,G,oFAGnB9G,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,I,EAGftB,UAAA,WACE,MAAO,gB,EAGTmE,MAAA,WACE,OAAO3G,IAAAA,WAAAA,MAAqB,+D,EAG9BsG,QAAA,WACE,MAAO,CACL,SAAK9D,UAAU,cACb,SAAKC,MAAM,qBACNuF,IAAAA,UAAiB,CACdvF,MAAM,aACND,UAAW,yBACXmC,QAAS,WACPsD,SAASC,WAGblI,IAAAA,WAAAA,MAAqB,uD,EA1Bd2K,CAAwCxC,KAAxCwC,EACZvC,eAAgB,E,ICGJwC,EAAAA,SAAAA,G,oFAGnB/G,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACbrE,KAAKU,kBAAoBV,KAAKC,MAAMS,kBACpC,IAAMO,EAAwBF,KAAKC,MAAMhB,KAAKU,kBAAkBQ,YAChElB,KAAKoL,uBAAyBpL,KAAK4D,eAAe3C,EAAsBoK,UAAU,GAClFrL,KAAK6I,aAAetI,IAAAA,QAAAA,KAAAA,UAA2B,U,EAIjDwC,UAAA,WACE,MAAO,gB,EAGTmE,MAAA,WACE,OAAO3G,IAAAA,WAAAA,MAAqB,uD,EAG9BsG,QAAA,WAAU,WACFvG,EAAYC,IAAAA,MAAAA,UAAoB,8BAAgC,UAChE8I,EAAgB/I,EAAU4B,QAAQ,UAAWlC,KAAK6I,cAClDyC,EAA6BhL,EAAU4B,QAAQ,UAAWlC,KAAKoL,wBAErE,OAAGpL,KAAK6I,cAAc7I,KAAKoL,uBAClB,CACL,SAAKrI,UAAU,cACb,YAAKxC,IAAAA,WAAAA,MAAqB,6DAA6D,CAACiH,KAAK8D,EAA2BxI,MAAMuG,KAC9H,SAAKrG,MAAM,wCACNuF,IAAAA,UAAiB,CACdvF,MAAM,aACND,UAAW,yBACXwE,KAAM,UAERhH,IAAAA,WAAAA,MAAqB,mDAN3B,IAQKgI,IAAAA,UACC,CACExF,UAAW,mCACXuB,QAAStE,KAAKsE,QACdY,QAAS,WACP,EAAK+E,SAGT1J,IAAAA,WAAAA,MAAqB,0DAMxB,CACL,SAAKwC,UAAU,cACb,YAAKxC,IAAAA,WAAAA,MAAqB,sEAAsE,CAACiH,KAAK8D,EAA2BxI,MAAMuG,KACvI,SAAKrG,MAAM,wCACNuF,IAAAA,UAAiB,CACdvF,MAAM,aACND,UAAW,yBACXmC,QAAS,WACP,EAAK+E,SAGT1J,IAAAA,WAAAA,MAAqB,uD,EAQnCqD,eAAA,SAAeC,EAAQC,GACrB,IAAIC,EAASL,KAAKM,IAAI,GAAIF,GAC1B,OAAOJ,KAAKO,MAAMJ,EAASE,GAAUA,G,EAGvC0G,SAAA,SAAShB,GAAG,WACVA,EAAEiB,iBAEF1K,KAAKU,kBAAkBqK,KAAK,CACxBQ,gBAAe,IAEhBpF,MACC,WACE5F,IAAAA,QAAAA,KAAAA,KAAAA,WAAAA,OAAwC,EAAK6K,uBAC7C7K,IAAAA,MAAAA,KAAe2K,MAEjB,SAACM,GACC,EAAKlH,SAAU,M,EAvFJ6G,CAAyCzC,KAAzCyC,EACZxC,eAAgB,ECTzB,MAAM,EAA+B1J,OAAOC,KAAKC,OAAO,yB,aCAxD,MAAM,GAA+BF,OAAOC,KAAKC,OAAO,oB,eCQnCsM,GAAAA,SAAAA,G,oFACnBrH,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACbrE,KAAKsE,SAAU,G,EAGjBxE,KAAA,WAAO,WACCQ,EAAYC,IAAIC,MAAMC,UAAU,8BAAgC,UAC/DC,EAAqBV,KAAKC,MAA1BS,kBAEDR,EADyBQ,EAAkBkB,KAAMxB,WACTC,UAAU,GAClDqL,EAAqBhL,EAAkB4G,QAEvCqE,GADoBjL,EAAkB6G,OACd7G,EAAkB0G,YAC1CwE,EAAqBlL,EAAkB2G,QACvCwE,EAAkBnL,EAAkBiH,YACpC/G,EAAmD,OAA7BF,EAAkBG,SAAgB,GAAGE,KAAKC,MAAMN,EAAkBG,UACxFiL,EAAwBpL,EAAkBkH,aAC1CmE,EAAkB,yBAA8C,OAArBL,EAA0B,GAAGA,GAAoB,wBAAwBE,GAAsC5L,KAAKgM,mBAAmBF,IAAwB,IAC1MG,EAAyBL,EAAmB,gBAAgB,GAC5DM,EAAyBxL,EAAkBmH,YAE3CsE,EAAwBzL,EAAkBgH,YAC1CzG,EAAwBF,KAAKC,MAAMN,EAAkBQ,YACrDkK,EAAyBnK,EAAsBoK,UAC/Ce,EAA4BnL,EAAsBoL,aAClDf,EAA6BhL,EAAU4B,QAAQ,UAAWkJ,GAC1DkB,EAA4BhM,EAAU4B,QAAQ,UAAWiK,GAEzDI,EAA0B7L,EAAkBoH,aAC9C0E,GAAoD,IAA1BD,EAAgChM,IAAIa,WAAWC,MAAM,uDAAuDoL,IAAAA,CAASF,GAE7IG,EAA8D,OAAlBb,EAAuB,sCAAsC,yBAE/G,OACE,aACA,SAAKvI,MAAM,yBAAyBN,MAAO+I,GACzC,SAAKzI,MAAM,oBAAoBN,MAAM,oCACnC,SAAKM,MAAM,uBACT,SAAKN,MAAM,mDAAmDzC,IAAIa,WAAWC,MAAM,oDAAoD,CAACO,GAAG1B,KAC3I,SAAK8C,MAAM,sBAAsBM,MAAM,8BAA8B/C,IAAIa,WAAWC,MAAM,oDAAoD,CAACO,GAAG1B,MAEpJ,SAAKoD,MAAM,uBACR/C,IAAIa,WAAWC,MAAM,uDADxB,KACkFmL,EADlF,MAEGjM,IAAIa,WAAWC,MAAM,qDAFxB,IAGKiK,EAHL,MAIM/K,IAAIa,WAAWC,MAAM,wDAAyD+K,EAJpF,MAMA,SAAK9I,MAAM,sBAAsBN,MAAM,eACX,IAAzBkJ,GAA2D,IAA7BtL,EAAoBgC,QACjD,SAAKU,MAAM,wBAAwBN,MAAM,iEACtCzC,IAAIa,WAAWC,MAAM,kDAIA,IAAzB6K,GAA2D,IAA7BtL,EAAoBgC,QACjD,SAAKI,MAAM,iBACT,SAAKA,MAAO0J,GACV,SAAKpJ,MAAM,2CAA2C4B,QAAS,kBAAM,EAAKyH,eAAejM,KACtFH,IAAIa,WAAWC,MAAM,gDAIP,OAAlBwK,GACC,SAAK7I,MAAM,wBACT,SAAKM,MAAM,2CAA2C4B,QAAS,kBAAM,EAAKqG,eAAe7K,KACtFH,IAAIa,WAAWC,MAAM,uDAON,IAAzB6K,GAA8BtL,EAAoBgC,OAAO,GAAKhC,EAAoBqC,KAAI,SAACC,GACtF,OACE,SAAKF,MAAM,+CACT,SAAKM,MAAM,wBAAwBN,MAAOiJ,GAAyB/I,QAM3E,SAAKI,MAAM,wBACR/C,IAAIa,WAAWC,MAAM,qDADxB,IAEGiL,KAMkB,OAAxBX,GAA6D,IAA7B/K,EAAoBgC,QACnD,SAAKI,MAAM,sBACT,QAAIA,MAAM,sCAAqC,SAAKM,MAAM,0BAA1D,IAA+F/C,IAAIa,WAAWC,MAAM,gDACpH,WAAOuL,SAAS,WAAW5J,MAAM,oCAC/B,YAAQ6J,IAAKlB,EAAuBpE,KAAK,kB,EASnDoF,eAAA,SAAejM,GAETH,IAAIuM,QAAQrI,KACdlE,IAAIwM,MAAMC,KAAKpE,EAAkC,CAAClI,kBAAAA,IAElDH,IAAIwM,MAAMC,KAAKC,M,EAInB1B,eAAA,SAAe7K,GAETH,IAAIuM,QAAQrI,KACdlE,IAAIwM,MAAMC,KAAK7B,EAAkC,CAACzK,kBAAAA,IAElDH,IAAIwM,MAAMC,KAAKC,M,EAInBjB,mBAAA,SAAmBkB,GAEjB,IADA,IAAIC,EAAO,EACFhD,EAAI,EAAGA,EAAI+C,EAAItK,OAAQuH,IAC9BgD,EAAOD,EAAIE,WAAWjD,KAAOgD,GAAQ,GAAKA,GAG5C,IADA,IAAIE,EAAS,IACJlD,EAAI,EAAGA,EAAI,EAAGA,IAErBkD,IAAW,MADEF,GAAa,EAAJhD,EAAU,KACR/J,SAAS,KAAKkN,QAAQ,GAEhD,OAAOD,G,EAnIU5B,CAA8BvH,KCD9BqJ,GAAAA,SAAAA,G,oFACnBnJ,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACbrE,KAAKwN,UAAY,aACjBxN,KAAKsE,SAAU,EACftE,KAAKyN,kBAAoB,GACzBzN,KAAK2E,e,EAGP7E,KAAA,WAAO,IACDwE,EADC,OAOL,OAJItE,KAAKsE,UACPA,EAAUQ,IAAAA,UAA2B,CAAEC,KAAM,WAI7C,SAAKhC,UAAU,aACZ2K,IAAAA,UAAAA,OAED,SAAK3K,UAAU,aACb,SAAKA,UAAU,oBACb,SAAKA,UAAU,yBACb,YAAK4K,GAAAA,CAAUD,IAAAA,UAAAA,eAAmCE,aAGpD,SAAKtK,MAAM,gCACRtD,KAAKyN,kBAAkBxK,KAAI,SAACvC,GAC3B,OACE,SAAKsC,MAAM,oBACRyI,GAAsBjG,UAAU,CAAE9E,kBAAAA,SAIvCV,KAAKsE,SACL,SAAKtB,MAAM,qBACT,SAAKA,MAAM,2DACRzC,IAAIa,WAAWC,MAAM,8DAEvBkH,IAAAA,UAAiB,CACdxF,UAAW,SACXmC,QAAS,WACP,EAAK2I,wBAGTtN,IAAIa,WAAWC,MAAM,2DAVzB,IAYGkH,IAAAA,UAAiB,CACdxF,UAAW,SACXmC,QAAS,WACP,EAAK4I,uBAGTvN,IAAIa,WAAWC,MAAM,wDAKzBrB,KAAKsE,SAA2C,IAAhCtE,KAAKyN,kBAAkB7K,QACvC,aACE,SAAKI,MAAM,uGAAuGzC,IAAIa,WAAWC,MAAM,8CAI1IiD,GAAW,SAAKvB,UAAU,0BAA0BuB,Q,EASjEsB,aAAA,SAAaC,GAIX,MAHA,GAAGI,KAAKC,MAAMlG,KAAKyN,kBAAmB5H,GACtC7F,KAAKsE,SAAU,EACfa,EAAEkB,SACKR,G,EAGTkI,qBAAA,SAAqBlI,GAInB,MAHA,GAAGI,KAAKC,MAAMlG,KAAKgO,0BAA2BnI,GAC9C7F,KAAKsE,SAAU,EACfa,EAAEkB,SACKR,G,EAGTlB,YAAA,WAKE,OAAOpE,IAAI0N,MACRC,KAAK,oBAAqB,CACzB1H,KAAM,CACJD,OAPS,CACb4H,KAAK,aAGA,OAME,eACNhI,KAAKnG,KAAK4F,aAAaa,KAAKzG,Q,EAGjC6N,oBAAA,WACE1I,EAAEC,MAAMC,IAAI9E,IAAI6E,MAAM,oCAAqC,CACzDqH,SAAUlM,IAAIuM,QAAQrI,KAAKgI,e,EAI/BqB,mBAAA,WACE3I,EAAEC,MAAMC,IAAI9E,IAAI6E,MAAM,0B,EA7GLmI,CAA+Ba,KCA/BC,GAAAA,SAAAA,G,oFACnBjK,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACbrE,KAAKwN,UAAY,aACjBxN,KAAKsE,SAAU,EACftE,KAAKyN,kBAAoB,GACzBzN,KAAK2E,e,EAGP7E,KAAA,WAAO,IACDwE,EADC,OAOL,OAJItE,KAAKsE,UACPA,EAAUQ,IAAAA,UAA2B,CAAEC,KAAM,WAI7C,SAAKhC,UAAU,aACZ2K,IAAAA,UAAAA,OAED,SAAK3K,UAAU,aACb,SAAKA,UAAU,oBACb,SAAKA,UAAU,yBACb,YAAK4K,GAAAA,CAAUD,IAAAA,UAAAA,eAAmCE,aAGpD,SAAKtK,MAAM,gCACRtD,KAAKyN,kBAAkBxK,KAAI,SAACvC,GAC3B,OACE,SAAKsC,MAAM,oBACRyI,GAAsBjG,UAAU,CAAE9E,kBAAAA,SAIvCV,KAAKsE,SACL,SAAKtB,MAAM,qBACRuF,IAAAA,UAAiB,CACdxF,UAAW,SACXmC,QAAS,WACP,EAAK2I,wBAGTtN,IAAIa,WAAWC,MAAM,2DAPzB,IASGkH,IAAAA,UAAiB,CACdxF,UAAW,SACXmC,QAAS,WACP,EAAKoJ,gBAGT/N,IAAIa,WAAWC,MAAM,wDAKzBrB,KAAKsE,SAA2C,IAAhCtE,KAAKyN,kBAAkB7K,QACvC,aACE,SAAKI,MAAM,uGAAuGzC,IAAIa,WAAWC,MAAM,8CAI1IiD,GAAW,SAAKvB,UAAU,0BAA0BuB,Q,EASjEsB,aAAA,SAAaC,GAIX,MAHA,GAAGI,KAAKC,MAAMlG,KAAKyN,kBAAmB5H,GACtC7F,KAAKsE,SAAU,EACfa,EAAEkB,SACKR,G,EAGTkI,qBAAA,SAAqBlI,GAInB,MAHA,GAAGI,KAAKC,MAAMlG,KAAKgO,0BAA2BnI,GAC9C7F,KAAKsE,SAAU,EACfa,EAAEkB,SACKR,G,EAGTlB,YAAA,WAKE,OAAOpE,IAAI0N,MACRC,KAAK,oBAAqB,CACzB1H,KAAM,CACJD,OAPS,CACb4H,KAAK,cAGA,OAME,eACNhI,KAAKnG,KAAK4F,aAAaa,KAAKzG,Q,EAGjC6N,oBAAA,WACE1I,EAAEC,MAAMC,IAAI9E,IAAI6E,MAAM,oCAAqC,CACzDqH,SAAUlM,IAAIuM,QAAQrI,KAAKgI,e,EAI/B6B,YAAA,WACEnJ,EAAEC,MAAMC,IAAI9E,IAAI6E,MAAM,mB,EA1GLiJ,CAAsCD,KCP3D,MAAM,GAA+BnP,OAAOC,KAAKC,OAAO,2B,ICGnCoP,GAAAA,SAAAA,G,oFACnBC,KAAA,WACE,MAAO,qB,EAGTC,KAAA,WACE,OAAOlO,IAAAA,MAAU,oCAAqC,CACpDkM,SAAUlM,IAAAA,QAAAA,KAAAA,c,EAIdsG,QAAA,WACE,IAAM6H,EAAe1O,KAAKC,MAAMyO,aAAaC,UAEvCC,EAAkC,WADXF,EAAajM,UAIpCvC,EADyBwO,EAAavO,aAAcC,WACZC,UAAU,GAClDwO,EAAqBtO,IAAAA,WAAAA,MAAqB,oDAAoD,CAACqB,GAAG1B,IAExG,OAAc,IAAX0O,EACMrO,IAAAA,WAAAA,MAAqB,+DAAgE,CAC1F2G,MAAO2H,IAGFtO,IAAAA,WAAAA,MAAqB,wDAAyD,CACnF2G,MAAO2H,K,EAKbC,QAAA,WACE,IAAMJ,EAAe1O,KAAKC,MAAMyO,aAAaC,UAEvCC,EAAkC,WADXF,EAAajM,UAGpCsM,EAAwBL,EAAa1M,YACrCmK,EAAwBuC,EAAa5M,MACrCH,EAA0B+M,EAAa9M,KACvCtB,EAAYC,IAAAA,MAAAA,UAAoB,8BAAgC,UAChEyO,EAA4B1O,EAAU4B,QAAQ,UAAW6M,GACzDzC,EAA4BhM,EAAU4B,QAAQ,UAAWiK,GAE/D,IAAc,IAAXyC,EAAgB,CACjB,IAAMpL,EAAcuL,EAAsB5C,EACpC1I,EAAkBnD,EAAU4B,QAAQ,UAAWwB,KAAKC,IAAIH,IAE9D,OAAGA,EAAY,EACNjD,IAAAA,WAAAA,MAAqB,qEAAsE,CAChG0O,UAAW3C,EACXrH,SAAU+J,EACVxL,YAAaC,EACbyL,WAAYvN,IAGPpB,IAAAA,WAAAA,MAAqB,qEAAsE,CAChG0O,UAAW3C,EACXrH,SAAU+J,EACVxL,YAAaC,EACbyL,WAAYvN,IAMhB,OAAOpB,IAAAA,WAAAA,MAAqB,qDAAsD,CAChF0O,UAAW3C,EACXrH,SAAU+J,EACVE,WAAYvN,K,EApEC4M,C,OAAkCY,ICHvD,MAAM,GAA+BlQ,OAAOC,KAAKC,OAAO,+B,eCcxDoB,IAAAA,aAAAA,IAAqB,wBAAwB,WAC3CA,IAAAA,MAAAA,OAAAA,kBAAqCwG,EACrCxG,IAAAA,MAAAA,OAAAA,sBAAyCwH,EACzCxH,IAAAA,MAAAA,OAAAA,2BAA8C2H,EAC9C3H,IAAAA,MAAAA,OAAAA,oCAAuD8H,EACvD9H,IAAAA,uBAAAA,sBAAmDgO,GAEnDhO,IAAAA,OAAAA,cAA8B,CAC5B6O,KAAM,iBACN5J,UAAW+H,IAGbhN,IAAAA,OAAAA,qBAAqC,CACnC6O,KAAM,wBACN5J,UAAW6I,KCtBbgB,EAAAA,EAAAA,QAAO3B,IAAAA,UAAqB,YAAY,SAAU4B,GAChD,IAAIC,EAA2BhP,IAAAA,MAAAA,UAAoB,sBAC/CiP,EAAsD,KAA3BD,EAA8BhP,IAAAA,WAAAA,MAAqB,2DAA2DgP,EAU7I,OARAD,EAAMG,IACJ,gBACA,EAAC,IAAD,CAAYjB,KAAK,oBAAoBC,KAAMlO,IAAAA,MAAU,kBAClDiP,GAEH,IAGKF,KCZT/O,IAAImP,OAAO,qCAAuC,CAChDN,KAAM,4CACN5J,UAAWmK,IAGbN,EAAAA,EAAAA,QAAOvI,IAAAA,UAAoB,YAAY,SAAUwI,EAAM7K,GAChDlE,IAAIuM,QAAQrI,MACSlE,IAAIuM,QAAQrI,KAAK7C,MAClB5B,KAAKyE,KAAK7C,MAG7B0N,EAAMG,IACJ,+BACAG,IAAAA,UAAqB,CACjBnB,KAAMlO,IAAI6E,MAAM,oCAAqC,CACnDqH,SAAUzM,KAAKyE,KAAKgI,aAEtB+B,KAAM,qBAER,CACEjO,IAAIa,WAAWC,MACb,gEAIN,QFGVgO,EAAAA,EAAAA,QAAOQ,KAAAA,UAA4B,qBAAqB,SAAUP,GAChEA,EAAMG,IAAI,wBAAyB,CACjCK,KAAM,wBACNtB,KAAM,oBACNuB,MAAOxP,IAAAA,WAAAA,MACL,mE", "sources": ["webpack://@wusong8899/flarum-daily-check-in/webpack/bootstrap", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/compat get default export", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/define property getters", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/hasOwnProperty shorthand", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/make namespace object", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['forum/app']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['extend']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/IndexPage']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/LinkButton']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/UserPage']\"", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['Component']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['app']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/LoadingIndicator']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Button']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Link']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/WelfareTicketPurchaseHistoryListItem.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/WelfareTicketPurchaseHistoryList.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/WelfareTicketPurchaseHistoryPage.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['Model']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/model/WelfareTicket.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/model/WelfareTicketPurchase.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/model/WelfareTicketPurchaseCount.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/model/WelfareTicketPurchaseSummary.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Page']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/helpers/listItems']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Modal']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/components/Alert']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/components/Select']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['utils/Stream']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/components/Modal']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/components/Button']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/WelfareTicketSuccessModal.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/WelfareTicketPurchaseTicketModal.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/WelfareTicketDealerSuccessModal.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/WelfareTicketPurchaseDealerModal.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/LogInModal']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['helpers/username']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/WelfareTicketListItem.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/WelfareTicketIndexPage.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/WelfareTicketHistoryIndexPage.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Notification']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/WelfareTicketNotification.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/NotificationGrid']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/index.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/addSidebarMenu.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/addUserPage.js"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['forum/app'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['extend'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/IndexPage'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/LinkButton'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/UserPage'];", "export default function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}", "import setPrototypeOf from \"./setPrototypeOf.js\";\nexport default function _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  setPrototypeOf(subClass, superClass);\n}", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['Component'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['app'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/LoadingIndicator'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Button'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Link'];", "import Component from \"flarum/Component\";\n\nexport default class WelfareTicketPurchaseHistoryListItem extends Component {\n  view() {\n    const {welfareTicketHistory} = this.attrs;\n    const welfareTicketIdString = (welfareTicketHistory.welfare_id()).toString();\n    const welfareTicketID = welfareTicketIdString.substring(1);\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n\n    const welfareTicketData = welfareTicketHistory.welfareData();\n    const welfareTicketResult = welfareTicketData.result();\n    const welfareTicketResultList = welfareTicketResult===null?[]:JSON.parse(welfareTicketResult);\n    const welfareTicketSettings = JSON.parse(welfareTicketData.settings());\n\n    const welfareTicketSelectedBallWinMultiplier = {\n      1:app.translator.trans('wusong8899-welfare-ticket.forum.welfare-current-selection-count',{count:1,multiplier:welfareTicketSettings.win1Multiplier}),\n      2:app.translator.trans('wusong8899-welfare-ticket.forum.welfare-current-selection-count',{count:2,multiplier:welfareTicketSettings.win2Multiplier}),\n      3:app.translator.trans('wusong8899-welfare-ticket.forum.welfare-current-selection-count',{count:3,multiplier:welfareTicketSettings.win3Multiplier}),\n    };\n\n    const welfareTicketPurchaseID = welfareTicketHistory.id();\n    const welfareTicketPurchaseBet = welfareTicketHistory.bet();\n    const welfareTicketPurchaseWinTotal = welfareTicketHistory.win_total();\n    const welfareTicketPurchaseWinTotalText = moneyName.replace('[money]', welfareTicketPurchaseWinTotal);\n    const welfareTicketPurchaseBetText = moneyName.replace('[money]', welfareTicketPurchaseBet);\n    const welfareTicketPurchaseOpened = parseInt(welfareTicketHistory.opened());\n\n    const isDealer = welfareTicketHistory.numbers()===\"dealer\";\n    let welfareTicketPurchaseResult = \"\";\n    let welfareResultStyle = \"\";\n\n    if(isDealer===false){\n      const welfareTicketPurchaseNumbers = JSON.parse(welfareTicketHistory.numbers());\n      const hitCountRequired = welfareTicketPurchaseNumbers.length;\n      let hitCount = 0;\n\n      if(welfareTicketPurchaseOpened===0){\n        welfareTicketPurchaseResult = app.translator.trans(\"wusong8899-welfare-ticket.forum.welfare-result-not-ready\");\n          welfareResultStyle = \"color:gray\";\n      }else{\n        if(welfareTicketPurchaseWinTotal===0){\n          welfareTicketPurchaseResult = app.translator.trans(\"wusong8899-welfare-ticket.forum.welfare-result-not-hit\");\n          welfareResultStyle = \"color:red\";\n        }else{\n          welfareTicketPurchaseResult = app.translator.trans(\"wusong8899-welfare-ticket.forum.welfare-result-win-price\",{hitCount:hitCountRequired,money:welfareTicketPurchaseWinTotalText});\n          welfareResultStyle = \"color:green\";\n        }\n      }\n\n      const WelfareTicketBallStyle = \"opacity: 0.9;width: 24px;height: 27px;line-height: 27px;\";\n\n      return (\n        <div className=\"welfareTicketHistoryContainer\">\n          <div style=\"padding-top: 5px;font-size: 16px;color: lightgoldenrodyellow;\">\n            <b>{app.translator.trans('wusong8899-welfare-ticket.forum.welfare-item-season-3D',{id:welfareTicketID})}</b>\n          </div>\n          <div>\n            <div style=\"float: right;margin-top:-25px;\">\n              {welfareTicketResult!==null && welfareTicketResultList.map((resultData) => {\n                let isHit = welfareTicketPurchaseNumbers.indexOf(parseInt(resultData));\n                let ballStyle = \"display:inline-block;margin-left: 2px;padding:4px;\";\n\n                if(welfareTicketPurchaseWinTotal>0 && isHit!==-1 && hitCount<hitCountRequired){\n                  ballStyle+=\"border: 1px solid lightgoldenrodyellow;border-radius: 4px;\";\n                  hitCount++;\n                }\n\n                return (\n                  <div style={ballStyle}>\n                    <div class='WelfareTicketBallBlue' style={WelfareTicketBallStyle}>{resultData}</div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n          <div style=\"padding-top: 5px;\">\n            <b>{app.translator.trans('wusong8899-welfare-ticket.forum.welfare-item-id')}: </b>\n            {welfareTicketPurchaseID}&nbsp;|&nbsp;\n            <b>{app.translator.trans('wusong8899-welfare-ticket.forum.welfare-item-bet')}: </b>\n            {welfareTicketPurchaseBetText}&nbsp;|&nbsp;\n            <b>{app.translator.trans('wusong8899-welfare-ticket.forum.welfare-item-numbers')}: </b>\n            <div style=\"display:inline-block;border:1px solid var(--text-color);padding: 0px 4px;margin-left: 4px;\">{welfareTicketPurchaseNumbers.join(\",\")}</div>\n          </div>\n          <div style=\"padding-top: 5px;\">\n            <b>{app.translator.trans('wusong8899-welfare-ticket.forum.welfare-result-type')}: </b>\n            {welfareTicketSelectedBallWinMultiplier[hitCountRequired]}&nbsp;|&nbsp;\n            <b>{app.translator.trans('wusong8899-welfare-ticket.forum.welfare-result')}: </b>\n            <span style={welfareResultStyle}>\n              {welfareTicketPurchaseResult}\n            </span>\n          </div>\n        </div>\n      );\n    }else{\n      if(welfareTicketPurchaseOpened===0){\n        welfareTicketPurchaseResult = app.translator.trans(\"wusong8899-welfare-ticket.forum.welfare-result-not-ready\");\n          welfareResultStyle = \"color:gray\";\n      }else{\n        const profitTotal = welfareTicketPurchaseWinTotal-welfareTicketPurchaseBet;\n        const profitTotalText = moneyName.replace('[money]', Math.abs(profitTotal));\n\n        if(profitTotal<=0){\n          welfareResultStyle = \"color:red\";\n          welfareTicketPurchaseResult = app.translator.trans(\"wusong8899-welfare-ticket.forum.welfare-result-dealer-negative-profit\",{money:profitTotalText});\n        }else{\n          welfareResultStyle = \"color:green\";\n          welfareTicketPurchaseResult = app.translator.trans(\"wusong8899-welfare-ticket.forum.welfare-result-dealer-positive-profit\",{money:profitTotalText});\n        }\n      }\n\n      return (\n        <div className=\"welfareTicketHistoryContainer\">\n          <div style=\"padding-top: 5px;font-size: 16px;color: lightgoldenrodyellow;\">\n            <b>{app.translator.trans('wusong8899-welfare-ticket.forum.welfare-item-season-3D',{id:welfareTicketID})}</b>\n          </div>\n          <div>\n            <div class=\"WelfareRainbowTextAnimated\" style=\"float: right;margin-top:-25px;font-size: 24px;\">\n              <b>{app.translator.trans('wusong8899-welfare-ticket.forum.welfare-item-is-dealer')}</b>\n            </div>\n          </div>\n          <div style=\"padding-top: 5px;\">\n            <b>{app.translator.trans('wusong8899-welfare-ticket.forum.welfare-item-dealer-bet')}: </b>\n            {welfareTicketPurchaseBetText}\n          </div>\n\n          <div style=\"padding-top: 5px;\">\n            <b>{app.translator.trans('wusong8899-welfare-ticket.forum.welfare-result-dealer-profit')}: </b>\n            <span style={welfareResultStyle}>\n              {welfareTicketPurchaseResult}\n            </span>\n          </div>\n        </div>\n      );\n    }\n  }\n\n  precisionRound(number, precision) {\n    let factor = Math.pow(10, precision);\n    return Math.round(number * factor) / factor;\n  }\n}\n", "import Component from \"flarum/Component\";\nimport app from \"flarum/app\";\nimport LoadingIndicator from \"flarum/components/LoadingIndicator\";\nimport Button from \"flarum/components/Button\";\nimport Link from \"flarum/components/Link\";\n\nimport WelfareTicketPurchaseHistoryListItem from \"./WelfareTicketPurchaseHistoryListItem\";\n\nexport default class WelfareTicketPurchaseHistoryList extends Component {\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.loading = true;\n    this.moreResults = false;\n    this.welfareTicketHistory = [];\n    this.welfareTicketHistorySummary = null;\n    this.user = this.attrs.params.user;\n    this.loadResults();\n  }\n\n  view() {\n    let loading;\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n    let purchaseCostTotalText,purchaseWinTotalText;\n\n    if(this.loading){\n      loading = LoadingIndicator.component({ size: \"large\" });\n      purchaseCostTotalText = moneyName.replace('[money]', \"-\");\n      purchaseWinTotalText = moneyName.replace('[money]', \"-\");\n    }else{\n      purchaseCostTotalText = moneyName.replace('[money]', this.welfareTicketHistorySummary.betTotal());\n      purchaseWinTotalText = moneyName.replace('[money]', this.welfareTicketHistorySummary.winTotal());\n    }\n\n    return (\n      <div>\n        <div style=\"display:flex;padding-bottom:10px\">\n          <div style=\"font-size: 24px;font-weight: bold;display:inline-block;padding-right: 10px;\">\n            {app.translator.trans(\"wusong8899-welfare-ticket.forum.welfare-ticket-view-history\")}\n          </div>\n          <Button className={'Button Button--primary'} onclick={() => m.route.set(app.route(\"welfareTicket\"))}>\n            {app.translator.trans(\"wusong8899-welfare-ticket.forum.purchase-now\")}\n          </Button>\n        </div>\n        <div style=\"color:var(--muted-color);padding-bottom:10px;\">\n          {app.translator.trans(\"wusong8899-welfare-ticket.forum.purchase-summary\",{\n            purchaseCostTotal:purchaseCostTotalText,\n            purchaseWinTotal:purchaseWinTotalText\n          })}\n        </div>\n        <ul style=\"margin: 0;padding: 0;list-style-type: none;position: relative;\">\n          {this.welfareTicketHistory.map((welfareTicketHistory) => {\n            return (\n              <li style=\"padding-bottom:5px\" data-id={welfareTicketHistory.id()}>\n                {WelfareTicketPurchaseHistoryListItem.component({ welfareTicketHistory })}\n              </li>\n            );\n          })}\n        </ul>\n          \n        {!this.loading && this.welfareTicketHistory.length===0 && (\n          <div>\n            <div style=\"font-size:1.4em;color: var(--muted-more-color);text-align: center;height: 300px;line-height: 100px;\">{app.translator.trans(\"wusong8899-guaguale.forum.guaguale-history-list-empty\")}</div>\n          </div>\n        )}\n\n        {!loading && this.hasMoreResults() && (\n          <div style=\"text-align:center;padding:20px\">\n            <Button className={'Button Button--primary'} disabled={this.loading} loading={this.loading} onclick={() => this.loadMore()}>\n              {app.translator.trans('wusong8899-guaguale.forum.guaguale-history-list-load-more')}\n            </Button>\n          </div>\n        )}\n\n        {loading && <div className=\"WelfareTicket-loadMore\">{loading}</div>}\n      </div>\n    );\n  }\n\n  loadMore() {\n    this.loading = true;\n    this.loadResults(this.welfareTicketHistory.length);\n  }\n\n  parseResults(results) {\n    this.moreResults = !!results.payload.links && !!results.payload.links.next;\n    [].push.apply(this.welfareTicketHistory, results);\n\n    return app.store\n      .find(\"welfareTicketPurchaseHistorySummary\")\n      .catch(() => {})\n      .then((summaryResult) => {\n        this.welfareTicketHistorySummary = summaryResult[0];\n        this.loading = false;\n        m.redraw();\n\n        return results;\n      });\n\n  }\n\n  hasMoreResults() {\n    return this.moreResults;\n  }\n\n  loadResults(offset = 0) {\n    return app.store\n      .find(\"welfareTicketPurchase\", {\n        filter: {\n          user: this.user.id(),\n        },\n        page: {\n          offset,\n        },\n      })\n      .catch(() => {})\n      .then(this.parseResults.bind(this));\n  }\n}\n", "import UserPage from \"flarum/components/UserPage\";\nimport WelfareTicketPurchaseHistoryList from \"./WelfareTicketPurchaseHistoryList\";\n\nexport default class GuaGuaLePurchaseHistoryPage extends UserPage {\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.loadUser(m.route.param(\"username\"));\n  }\n\n  content() {\n    return (\n      <div className=\"WelfareTicketPurchaseHistoryPage\">\n        {WelfareTicketPurchaseHistoryList.component({\n          params: {\n            user: this.user,\n          },\n        })}\n      </div>\n    );\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['Model'];", "import Model from \"flarum/Model\";\n\nexport default class WelfareTicket extends Model {}\nObject.assign(WelfareTicket.prototype, {\n  id: Model.attribute(\"id\"),\n  title: Model.attribute(\"title\"),\n  desc: Model.attribute(\"desc\"),\n  playback: Model.attribute(\"playback\"),\n  color: Model.attribute(\"color\"),\n  image: Model.attribute(\"image\"),\n  type: Model.attribute(\"type\"),\n  cost: Model.attribute(\"cost\"),\n  purchased_total: Model.attribute(\"purchased_total\"),\n  bet_total: Model.attribute(\"bet_total\"),\n  dealer_id: Model.attribute(\"dealer_id\"),\n  result: Model.attribute(\"result\"),\n  settings: Model.attribute(\"settings\"),\n  assignedAt: Model.attribute(\"assigned_at\"),\n  activated: Model.attribute(\"activated\"),\n  dealerData: Model.hasOne(\"dealerData\"),\n});\n", "import Model from \"flarum/Model\";\n\nexport default class WelfareTicketPurchase extends Model {}\nObject.assign(WelfareTicketPurchase.prototype, {\n  id: Model.attribute(\"id\"),\n  title: Model.attribute(\"title\"),\n  welfare_id: Model.attribute(\"welfare_id\"),\n  user_id: Model.attribute(\"user_id\"),\n  bet: Model.attribute(\"bet\"),\n  multiplier: Model.attribute(\"multiplier\"),\n  numbers: Model.attribute(\"numbers\"),\n  win_total: Model.attribute(\"win_total\"),\n  opened: Model.attribute(\"opened\"),\n  assigned_at: Model.attribute(\"assigned_at\"),\n\n  welfareData: Model.hasOne(\"welfareData\"),\n});\n", "import Model from \"flarum/Model\";\n\nexport default class WelfareTicketPurchaseCount extends Model {}\nObject.assign(WelfareTicketPurchaseCount.prototype, {\n  total_purchase_count: Model.attribute(\"total_purchase_count\"),\n  total_win_count: Model.attribute(\"total_win_count\"),\n});\n", "import Model from \"flarum/Model\";\n\nexport default class WelfareTicketPurchaseSummary extends Model {}\nObject.assign(WelfareTicketPurchaseSummary.prototype, {\n  betTotal: Model.attribute(\"betTotal\"),\n  winTotal: Model.attribute(\"winTotal\")\n});\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Page'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/helpers/listItems'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Modal'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/Alert'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/Select'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['utils/Stream'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/Modal'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/Button'];", "import app from 'flarum/forum/app';\nimport Modal from 'flarum/common/components/Modal';\nimport Button from 'flarum/common/components/Button';\n\nexport default class WelfareTicketSuccessModal extends Modal {\n  static isDismissible = false;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    return app.translator.trans('wusong8899-welfare-ticket.forum.welfare-purchase-success');\n  }\n\n  content() {\n    return [\n      <div className=\"Modal-body\">\n        <div style=\"text-align:center\">\n            {Button.component({\n                style:'width:66px',\n                className: 'Button Button--primary',\n                onclick: () => {\n                  location.reload();\n                }\n              },\n              app.translator.trans('wusong8899-welfare-ticket.forum.welfare-purchase-ok')\n            )}\n          </div>\n      </div>,\n    ];\n  }\n}", "import app from 'flarum/forum/app';\nimport Modal from 'flarum/components/Modal';\nimport Button from 'flarum/components/Button';\nimport Alert from 'flarum/common/components/Alert';\nimport Select from 'flarum/common/components/Select';\nimport Stream from 'flarum/utils/Stream';\nimport WelfareTicketSuccessModal from './WelfareTicketSuccessModal';\n\nexport default class WelfareTicketPurchaseTicketModal extends Modal {\n  static isDismissible = false;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.welfareTicketData = this.attrs.welfareTicketData;\n    this.moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n    this.currentMoney = app.session.user.attribute(\"money\");\n\n    const welfareTicketIdString = (this.welfareTicketData.id()).toString();\n    this.welfareTicketID = welfareTicketIdString.substring(1);\n    this.welfareTotalBall = 3;\n\n    const welfareTicketSettings = JSON.parse(this.welfareTicketData.settings());\n\n    this.welfareTicketSelectedBallWinMultiplier = {\n      0:app.translator.trans('wusong8899-welfare-ticket.forum.welfare-current-selection-none'),\n      1:app.translator.trans('wusong8899-welfare-ticket.forum.welfare-current-selection-count',{count:1,multiplier:welfareTicketSettings.win1Multiplier}),\n      2:app.translator.trans('wusong8899-welfare-ticket.forum.welfare-current-selection-count',{count:2,multiplier:welfareTicketSettings.win2Multiplier}),\n      3:app.translator.trans('wusong8899-welfare-ticket.forum.welfare-current-selection-count',{count:3,multiplier:welfareTicketSettings.win3Multiplier}),\n    };\n\n    this.welfareTicketBall = {\n      ball1:Stream(0),\n      ball2:Stream(0),\n      ball3:Stream(0),\n    };\n\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    return app.translator.trans('wusong8899-welfare-ticket.forum.welfare-item-season-3D',{id:this.welfareTicketID});\n  }\n\n  content() {\n    const moneyNameText = this.moneyName.replace('[money]', this.currentMoney);\n    const moneyCostText = this.moneyName.replace('[money]', 0);\n\n    let ball1 = this.welfareTicketBall[\"ball1\"]();\n    let ball2 = this.welfareTicketBall[\"ball2\"]();\n    let ball3 = this.welfareTicketBall[\"ball3\"]();\n\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form\">\n          <div className=\"Form-group\">\n            <label>{app.translator.trans('wusong8899-welfare-ticket.forum.welfare-select-number')}</label>\n            <Select style=\"width:100px;\" value={ball1} options={[\"无\",0,1,2,3,4,5,6,7,8,9]} buttonClassName=\"Button\" onchange={(e) => {this.updateSelection(e,1);}} />\n            <Select style=\"width:100px;margin-left:20px;\" value={ball2} options={[\"无\",0,1,2,3,4,5,6,7,8,9]} buttonClassName=\"Button\" onchange={(e) => {this.updateSelection(e,2);}} />\n            <Select style=\"width:100px;margin-left:20px;\" value={ball3} options={[\"无\",0,1,2,3,4,5,6,7,8,9]} buttonClassName=\"Button\" onchange={(e) => {this.updateSelection(e,3);}} />\n            <div style=\"padding-top:10px\">{app.translator.trans('wusong8899-welfare-ticket.forum.welfare-item-bet')}</div>\n            <div style=\"padding-top:10px\"><input required id=\"WelfareBetInput\" className=\"FormControl\" type=\"number\" step=\"0.01\" min=\"1\" max={this.currentMoney} onchange={() => this.updateBetInput()} onkeyup={() => this.updateBetInput()} /></div>\n            <div style=\"padding-top:10px\">{app.translator.trans('wusong8899-guaguale.forum.guaguale-current-money-amount')}{moneyNameText}</div>\n            <div style=\"padding-top:10px\">{app.translator.trans('wusong8899-welfare-ticket.forum.welfare-current-selection-multiplier')}<span id=\"welfareTicketWinMultiplierText\">{app.translator.trans('wusong8899-welfare-ticket.forum.welfare-current-selection-none')}</span></div>\n          </div>\n          <div className=\"Form-group\" style=\"text-align: center;\">\n            {Button.component(\n              {\n                className: 'Button Button--primary',\n                type: 'submit',\n                loading: this.loading,\n              },\n              app.translator.trans('wusong8899-welfare-ticket.forum.welfare-purchase-ok')\n            )}&nbsp;\n            {Button.component(\n              {\n                className: 'Button transferMoneyButton--gray',\n                loading: this.loading,\n                onclick: () => {\n                  this.hide();\n                }\n              },\n              app.translator.trans('wusong8899-welfare-ticket.forum.welfare-purchase-cancel')\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  precisionRound(number, precision) {\n    let factor = Math.pow(10, precision);\n    return Math.round(number * factor) / factor;\n  }\n\n  updateSelection(value,id){\n    this.welfareTicketBall[\"ball\"+id](value);\n\n    let selectedBallCount = 0;\n    for(let i=1;i<=this.welfareTotalBall;i++){\n      if(parseInt(this.welfareTicketBall[\"ball\"+i]())!==0){\n        selectedBallCount++;\n      }\n    }\n\n    $(\"#welfareTicketWinMultiplierText\").text((this.welfareTicketSelectedBallWinMultiplier[selectedBallCount]).join(\"\"));\n  }\n\n  updateBetInput(){\n    //\n    let WelfareBetValue = this.precisionRound($(\"#WelfareBetInput\").val(),2);\n    const availableMoney = this.currentMoney;\n\n    if(WelfareBetValue>availableMoney){\n      $(\"#WelfareBetInput\").val(availableMoney);\n    }\n  }\n\n  onsubmit(e) {\n    e.preventDefault();\n\n    const welfareBetValue = this.precisionRound($(\"#WelfareBetInput\").val(),2);\n    const welfareTicketID = this.welfareTicketID;\n    const welfareTicketNumbers = [];\n\n    for(let i=1;i<=this.welfareTotalBall;i++){\n      const welfareTicketBallValue = parseInt(this.welfareTicketBall[\"ball\"+i]());\n\n      if(welfareTicketBallValue===0){\n\n      }else{\n        welfareTicketNumbers.push(welfareTicketBallValue-1);\n      }\n    }\n\n    if(welfareTicketNumbers.length===0){\n      app.alerts.show(Alert, {type: 'error'}, app.translator.trans('wusong8899-welfare-ticket.forum.purchase-error-no-number-selected'));\n      return;\n    }\n\n    if(welfareBetValue===0){\n      app.alerts.show(Alert, {type: 'error'}, app.translator.trans('wusong8899-welfare-ticket.forum.purchase-error-no-bet'));\n      return;\n    }\n\n    const welfarePurchaseData = {\n      welfare_id:welfareTicketID,\n      bet:welfareBetValue,\n      type:1,\n      numbers:welfareTicketNumbers\n    };\n\n    this.loading = true;\n\n    app.store\n      .createRecord(\"welfareTicketPurchase\")\n      .save(welfarePurchaseData)\n      .then(\n        (welfareTicketPurchase) => {\n          app.store.pushPayload(welfareTicketPurchase);\n          app.modal.show(WelfareTicketSuccessModal);\n          app.session.user.data.attributes.money-=welfareBetValue;\n          this.loading = false;\n        }\n      )\n      .catch((e) => {\n        this.loading = false;\n      });\n  }\n}\n", "import app from 'flarum/forum/app';\nimport Modal from 'flarum/common/components/Modal';\nimport Button from 'flarum/common/components/Button';\n\nexport default class WelfareTicketDealerSuccessModal extends Modal {\n  static isDismissible = false;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    return app.translator.trans('wusong8899-welfare-ticket.forum.welfare-purchase-dealer-success');\n  }\n\n  content() {\n    return [\n      <div className=\"Modal-body\">\n        <div style=\"text-align:center\">\n            {Button.component({\n                style:'width:66px',\n                className: 'Button Button--primary',\n                onclick: () => {\n                  location.reload();\n                }\n              },\n              app.translator.trans('wusong8899-welfare-ticket.forum.welfare-purchase-ok')\n            )}\n          </div>\n      </div>,\n    ];\n  }\n}", "import app from 'flarum/forum/app';\nimport Modal from 'flarum/components/Modal';\nimport Button from 'flarum/components/Button';\nimport Alert from 'flarum/common/components/Alert';\nimport Select from 'flarum/common/components/Select';\nimport Stream from 'flarum/utils/Stream';\nimport WelfareTicketDealerSuccessModal from './WelfareTicketDealerSuccessModal';\n\nexport default class WelfareTicketPurchaseDealerModal extends Modal {\n  static isDismissible = false;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.welfareTicketData = this.attrs.welfareTicketData;\n    const welfareTicketSettings = JSON.parse(this.welfareTicketData.settings());\n    this.welfareTicketDealerBet = this.precisionRound(welfareTicketSettings.dealerBet,2);\n    this.currentMoney = app.session.user.attribute(\"money\");\n\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    return app.translator.trans('wusong8899-welfare-ticket.forum.welfare-purchase-dealer');\n  }\n\n  content() {\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n    const moneyNameText = moneyName.replace('[money]', this.currentMoney);\n    const welfareTicketDealerBetText = moneyName.replace('[money]', this.welfareTicketDealerBet);\n\n    if(this.currentMoney>=this.welfareTicketDealerBet){\n      return [\n        <div className=\"Modal-body\">\n          <h3>{app.translator.trans('wusong8899-welfare-ticket.forum.welfare-purchase-dealer-confirm',{cost:welfareTicketDealerBetText,money:moneyNameText})}</h3>\n          <div style=\"text-align:center;padding-top: 20px;\">\n              {Button.component({\n                  style:'width:66px',\n                  className: 'Button Button--primary',\n                  type: 'submit',\n                },\n                app.translator.trans('wusong8899-welfare-ticket.forum.welfare-purchase-ok')\n              )}&nbsp;\n              {Button.component(\n                {\n                  className: 'Button transferMoneyButton--gray',\n                  loading: this.loading,\n                  onclick: () => {\n                    this.hide();\n                  }\n                },\n                app.translator.trans('wusong8899-welfare-ticket.forum.welfare-purchase-cancel')\n              )}\n            </div>\n        </div>,\n      ];\n    }else{\n      return [\n        <div className=\"Modal-body\">\n          <h3>{app.translator.trans('wusong8899-welfare-ticket.forum.welfare-purchase-dealer-insuffcient-fund',{cost:welfareTicketDealerBetText,money:moneyNameText})}</h3>\n          <div style=\"text-align:center;padding-top: 20px;\">\n              {Button.component({\n                  style:'width:66px',\n                  className: 'Button Button--primary',\n                  onclick: () => {\n                    this.hide();\n                  }\n                },\n                app.translator.trans('wusong8899-welfare-ticket.forum.welfare-purchase-ok')\n              )}\n            </div>\n        </div>,\n      ];\n    }\n  }\n\n  precisionRound(number, precision) {\n    let factor = Math.pow(10, precision);\n    return Math.round(number * factor) / factor;\n  }\n\n  onsubmit(e) {\n    e.preventDefault();\n\n    this.welfareTicketData.save({\n        purchaseDealer:true\n      })\n      .then(\n        () => {\n          app.session.user.data.attributes.money-=this.welfareTicketDealerBet;\n          app.modal.show(WelfareTicketDealerSuccessModal);\n        },\n        (response) => {\n          this.loading = false;\n        }\n      );\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/LogInModal'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['helpers/username'];", "import Component from \"flarum/Component\";\nimport Link from \"flarum/components/Link\";\nimport Button from 'flarum/components/Button';\nimport WelfareTicketPurchaseTicketModal from './WelfareTicketPurchaseTicketModal';\nimport WelfareTicketPurchaseDealerModal from './WelfareTicketPurchaseDealerModal';\nimport LogInModal from \"flarum/components/LogInModal\";\nimport username from \"flarum/helpers/username\";\n\nexport default class WelfareTicketListItem extends Component {\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.loading = true;\n  }\n\n  view() {\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n    const {welfareTicketData} = this.attrs;\n    const welfareTicketIdString = (welfareTicketData.id()).toString();\n    const welfareTicketID = welfareTicketIdString.substring(1);\n    const welfareTicketImage = welfareTicketData.image();\n    const welfareTicketType = welfareTicketData.type();\n    const welfareTicketPlayback = welfareTicketData.playback();\n    const welfareTicketColor = welfareTicketData.color();\n    const welfareDealerID = welfareTicketData.dealer_id();\n    const welfareTicketResult = welfareTicketData.result()===null?[]:JSON.parse(welfareTicketData.result());\n    const welfareTicketAssignAt = welfareTicketData.assignedAt();\n    const backgroundStyle = \"background-image:url(\"+(welfareTicketImage===null?'':welfareTicketImage)+\");background-color: \"+(welfareTicketColor?welfareTicketColor:this.getBackgroundColor(welfareTicketAssignAt))+\";\";\n    const WelfareTicketBallStyle = welfareTicketColor?\"opacity: 0.9;\":\"\";\n    const welfareTicketActivated = welfareTicketData.activated();\n\n    const welfareTicketBetTotal = welfareTicketData.bet_total();\n    const welfareTicketSettings = JSON.parse(welfareTicketData.settings());\n    const welfareTicketDealerBet = welfareTicketSettings.dealerBet;\n    const welfareTicketDealerBetCut = welfareTicketSettings.dealerBetCut;\n    const welfareTicketDealerBetText = moneyName.replace('[money]', welfareTicketDealerBet);\n    const welfareTicketBetTotalText = moneyName.replace('[money]', welfareTicketBetTotal);\n\n    const welfareTicketDealerData = welfareTicketData.dealerData();\n    let welfareTicketDealerText = welfareTicketDealerData===false?app.translator.trans('wusong8899-welfare-ticket.forum.welfare-item-dealer-none'):username(welfareTicketDealerData);\n\n    const welfareTicketPurchaseButtonContainerStyle = welfareDealerID===null?\"display: inline-block;width: 165px;\":\"display: inline-block;\";\n\n    return (\n      <div>\n      <div class=\"WelfareTicketContainer\" style={backgroundStyle}>\n        <div class=\"WelfareTicketMask\" style=\"background: var(--shadow-color);\">\n          <div class=\"WelfareTicketSeason\">\n            <div style=\"position: absolute;color: lightgoldenrodyellow;\">{app.translator.trans('wusong8899-welfare-ticket.forum.welfare-item-season-3D',{id:welfareTicketID})}</div>\n            <div style=\"filter: blur(20px);\" class=\"WelfareRainbowTextAnimated\">{app.translator.trans('wusong8899-welfare-ticket.forum.welfare-item-season-3D',{id:welfareTicketID})}</div>\n          </div>\n          <div class=\"WelfareTicketDealer\">\n            {app.translator.trans('wusong8899-welfare-ticket.forum.welfare-item-dealer-name')}: {welfareTicketDealerText}&nbsp;|&nbsp;\n            {app.translator.trans('wusong8899-welfare-ticket.forum.welfare-item-dealerBet')}: \n              {welfareTicketDealerBetText}&nbsp;\n              ({app.translator.trans('wusong8899-welfare-ticket.forum.welfare-item-dealerBetCut')}{welfareTicketDealerBetCut}%)\n          </div>\n          <div class=\"WelfareTicketResult\" style=\"height:60px\">\n            {welfareTicketActivated===0 && welfareTicketResult.length===0 && (\n              <div class=\"WelfareFadingAnimated\" style=\"height:50px;display:inline-block;color: lightgoldenrodyellow;\">\n                {app.translator.trans(\"wusong8899-welfare-ticket.forum.welfare-bet-closed\")}\n              </div>\n            )}\n\n            {welfareTicketActivated===1 && welfareTicketResult.length===0 && (\n              <div style=\"height: 50px;\">\n                <div style={welfareTicketPurchaseButtonContainerStyle}>\n                  <div class=\"WelfareTicketAssignTicket buttonRegister\" onclick={() => this.purchaseTicket(welfareTicketData)}>\n                    {app.translator.trans(\"wusong8899-welfare-ticket.forum.welfare-bet-now\")}\n                  </div>\n                </div>\n\n                {welfareDealerID===null && (\n                  <div style=\"display:inline-block\">\n                    <div class=\"WelfareTicketAssignDealer buttonRegister\" onclick={() => this.purchaseDealer(welfareTicketData)}>\n                      {app.translator.trans(\"wusong8899-welfare-ticket.forum.welfare-become-dealer\")}\n                    </div>\n                  </div>\n                )}\n              </div>\n            )}\n\n            {welfareTicketActivated===0 && welfareTicketResult.length>0 && welfareTicketResult.map((resultData) => {\n              return (\n                <div style=\"margin-left: 2px;padding: 4px;height: 70px;\">\n                  <div class='WelfareTicketBallBlue' style={WelfareTicketBallStyle}>{resultData}</div>\n                </div>\n              );\n            })}\n          </div>\n\n          <div class=\"WelfareTicketDetails\">\n            {app.translator.trans('wusong8899-welfare-ticket.forum.welfare-item-bet-total')}: \n            {welfareTicketBetTotalText}\n          </div>\n\n        </div>\n      </div>\n\n      {welfareTicketPlayback!==null && welfareTicketResult.length===0 && (\n        <div style=\"padding-top: 10px;\">\n          <h3 style=\"display: flex;align-items: center;\"><div class=\"WelfareTicketReminder\"></div>&nbsp;{app.translator.trans('wusong8899-welfare-ticket.forum.welfare-playback')}</h3>\n          <video controls=\"controls\" style=\"width: 100%;border-radius: 12px;\">\n            <source src={welfareTicketPlayback} type=\"video/mp4\" />\n          </video>\n        </div>\n      )}\n\n      </div>\n    );\n  }\n\n  purchaseTicket(welfareTicketData) {\n    //\n    if (app.session.user) {\n      app.modal.show(WelfareTicketPurchaseTicketModal, {welfareTicketData});\n    } else {\n      app.modal.show(LogInModal);\n    }\n  }\n\n  purchaseDealer(welfareTicketData) {\n    //\n    if (app.session.user) {\n      app.modal.show(WelfareTicketPurchaseDealerModal, {welfareTicketData});\n    } else {\n      app.modal.show(LogInModal);\n    }\n  }\n\n  getBackgroundColor(str) {\n    let hash = 0;\n    for (let i = 0; i < str.length; i++) {\n      hash = str.charCodeAt(i) + ((hash << 5) - hash);\n    }\n    let colour = '#';\n    for (let i = 0; i < 3; i++) {\n      let value = (hash >> (i * 8)) & 0xFF;\n      colour += ('00' + value.toString(16)).substr(-2);\n    }\n    return colour;\n  }\n}\n", "import Page from 'flarum/components/Page';\nimport IndexPage from 'flarum/components/IndexPage';\nimport listItems from 'flarum/common/helpers/listItems';\nimport LoadingIndicator from \"flarum/components/LoadingIndicator\";\nimport WelfareTicketListItem from \"./WelfareTicketListItem\";\nimport Button from 'flarum/components/Button';\n\nexport default class WelfareTicketIndexPage extends Page {\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.bodyClass = 'App--index';\n    this.loading = true;\n    this.welfareTicketList = [];\n    this.loadResults();\n  }\n\n  view() {\n    let loading;\n\n    if (this.loading) {\n      loading = LoadingIndicator.component({ size: \"large\" });\n    }\n\n    return (\n      <div className=\"IndexPage\">\n        {IndexPage.prototype.hero()}\n\n        <div className=\"container\">\n          <div className=\"sideNavContainer\">\n            <nav className=\"IndexPage-nav sideNav\">\n              <ul>{listItems(IndexPage.prototype.sidebarItems().toArray())}</ul>\n            </nav>\n\n            <div class=\"WelfareTicketGlobalContainer\">\n              {this.welfareTicketList.map((welfareTicketData) => {\n                return (\n                  <div style=\"padding-top:5px;\">\n                    {WelfareTicketListItem.component({ welfareTicketData})}\n                  </div>\n                );\n              })}\n              {!this.loading && (\n                <div style=\"padding-top:10px;\">\n                  <div style=\"display: flex;align-items: center;padding-bottom: 10px;\">\n                    {app.translator.trans('wusong8899-welfare-ticket.forum.welfare-ticket-result-reminder')}\n                  </div>\n                  {Button.component({\n                      className: 'Button',\n                      onclick: () => {\n                        this.showPurchaseHistory();\n                      }\n                    },\n                    app.translator.trans('wusong8899-welfare-ticket.forum.welfare-ticket-view-history')\n                  )}&nbsp;\n                  {Button.component({\n                      className: 'Button',\n                      onclick: () => {\n                        this.showWelfareHistory();\n                      }\n                    },\n                    app.translator.trans('wusong8899-welfare-ticket.forum.welfare-ticket-history')\n                  )}\n                </div>\n              )}\n\n              {!this.loading && this.welfareTicketList.length===0 && (\n                <div>\n                  <div style=\"font-size:1.4em;color: var(--muted-more-color);text-align: center;height: 300px;line-height: 100px;\">{app.translator.trans(\"wusong8899-guaguale.forum.guaguale-list-empty\")}</div>\n                </div>\n              )}\n\n              {loading && <div className=\"WelfareTicket-loadMore\">{loading}</div>}\n            </div>\n\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  parseResults(results) {\n    [].push.apply(this.welfareTicketList, results);\n    this.loading = false;\n    m.redraw();\n    return results;\n  }\n\n  parsePurchaseResults(results) {\n    [].push.apply(this.guagualePurchaseCountList, results);\n    this.loading = false;\n    m.redraw();\n    return results;\n  }\n\n  loadResults() {\n    const filter = {\n      item:\"active\"\n    };\n\n    return app.store\n      .find(\"welfareTicketList\", {\n        page: {\n          filter\n        },\n      })\n      .catch(() => {})\n      .then(this.parseResults.bind(this));\n  }\n\n  showPurchaseHistory() {\n    m.route.set(app.route(\"user.welfareTicketPurchaseHistory\", {\n      username: app.session.user.username(),\n    }));\n  }\n\n  showWelfareHistory() {\n    m.route.set(app.route(\"welfareTicketHistory\"));\n  }\n}\n", "import Page from 'flarum/components/Page';\nimport IndexPage from 'flarum/components/IndexPage';\nimport listItems from 'flarum/common/helpers/listItems';\nimport LoadingIndicator from \"flarum/components/LoadingIndicator\";\nimport WelfareTicketListItem from \"./WelfareTicketListItem\";\nimport Button from 'flarum/components/Button';\n\nexport default class WelfareTicketHistoryIndexPage extends Page {\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.bodyClass = 'App--index';\n    this.loading = true;\n    this.welfareTicketList = [];\n    this.loadResults();\n  }\n\n  view() {\n    let loading;\n\n    if (this.loading) {\n      loading = LoadingIndicator.component({ size: \"large\" });\n    }\n\n    return (\n      <div className=\"IndexPage\">\n        {IndexPage.prototype.hero()}\n\n        <div className=\"container\">\n          <div className=\"sideNavContainer\">\n            <nav className=\"IndexPage-nav sideNav\">\n              <ul>{listItems(IndexPage.prototype.sidebarItems().toArray())}</ul>\n            </nav>\n\n            <div class=\"WelfareTicketGlobalContainer\">\n              {this.welfareTicketList.map((welfareTicketData) => {\n                return (\n                  <div style=\"padding-top:5px;\">\n                    {WelfareTicketListItem.component({ welfareTicketData})}\n                  </div>\n                );\n              })}\n              {!this.loading && (\n                <div style=\"padding-top:10px;\">\n                  {Button.component({\n                      className: 'Button',\n                      onclick: () => {\n                        this.showPurchaseHistory();\n                      }\n                    },\n                    app.translator.trans('wusong8899-welfare-ticket.forum.welfare-ticket-view-history')\n                  )}&nbsp;\n                  {Button.component({\n                      className: 'Button',\n                      onclick: () => {\n                        this.showWelfare();\n                      }\n                    },\n                    app.translator.trans('wusong8899-welfare-ticket.forum.welfare-ticket-current')\n                  )}\n                </div>\n              )}\n\n              {!this.loading && this.welfareTicketList.length===0 && (\n                <div>\n                  <div style=\"font-size:1.4em;color: var(--muted-more-color);text-align: center;height: 300px;line-height: 100px;\">{app.translator.trans(\"wusong8899-guaguale.forum.guaguale-list-empty\")}</div>\n                </div>\n              )}\n\n              {loading && <div className=\"WelfareTicket-loadMore\">{loading}</div>}\n            </div>\n\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  parseResults(results) {\n    [].push.apply(this.welfareTicketList, results);\n    this.loading = false;\n    m.redraw();\n    return results;\n  }\n\n  parsePurchaseResults(results) {\n    [].push.apply(this.guagualePurchaseCountList, results);\n    this.loading = false;\n    m.redraw();\n    return results;\n  }\n\n  loadResults() {\n    const filter = {\n      item:\"history\"\n    };\n\n    return app.store\n      .find(\"welfareTicketList\", {\n        page: {\n          filter\n        },\n      })\n      .catch(() => {})\n      .then(this.parseResults.bind(this));\n  }\n\n  showPurchaseHistory() {\n    m.route.set(app.route(\"user.welfareTicketPurchaseHistory\", {\n      username: app.session.user.username(),\n    }));\n  }\n\n  showWelfare() {\n    m.route.set(app.route(\"welfareTicket\"));\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Notification'];", "import app from 'flarum/forum/app';\nimport Notification from \"flarum/components/Notification\";\n\nexport default class WelfareTicketNotification extends Notification {\n  icon() {\n    return \"fas fa-ticket-alt\";\n  }\n\n  href() {\n    return app.route(\"user.welfareTicketPurchaseHistory\", {\n      username: app.session.user.username(),\n    });\n  }\n\n  content() {\n    const notification = this.attrs.notification.subject();\n    const welfareTicketNumbers = notification.numbers();\n    const isDealer = welfareTicketNumbers===\"dealer\";\n\n    const welfareTicketIdString = (notification.welfare_id()).toString();\n    const welfareTicketID = welfareTicketIdString.substring(1);\n    const welfareTicketTitle = app.translator.trans('wusong8899-welfare-ticket.forum.welfare-item-season-3D',{id:welfareTicketID})\n\n    if(isDealer===true){\n      return app.translator.trans('wusong8899-welfare-ticket.forum.notifications.open-content-dealer', {\n        title: welfareTicketTitle\n      });\n    }else{\n      return app.translator.trans('wusong8899-welfare-ticket.forum.notifications.open-content', {\n        title: welfareTicketTitle\n      });\n    }\n  }\n\n  excerpt() {\n    const notification = this.attrs.notification.subject();\n    const welfareTicketNumbers = notification.numbers();\n    const isDealer = welfareTicketNumbers===\"dealer\";\n\n    const welfareTicketWinTotal = notification.win_total();\n    const welfareTicketBetTotal = notification.bet();\n    const welfareTicketPurchaseID = notification.id();\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n    const welfareTicketWinTotalText = moneyName.replace('[money]', welfareTicketWinTotal);\n    const welfareTicketBetTotalText = moneyName.replace('[money]', welfareTicketBetTotal);\n\n    if(isDealer===true){\n      const profitTotal = welfareTicketWinTotal-welfareTicketBetTotal;\n      const profitTotalText = moneyName.replace('[money]', Math.abs(profitTotal));\n\n      if(profitTotal>0){\n        return app.translator.trans('wusong8899-welfare-ticket.forum.notifications.win-total-positive-dealer', {\n          costTotal: welfareTicketBetTotalText,\n          winTotal: welfareTicketWinTotalText,\n          profitTotal: profitTotalText,\n          purchaseID: welfareTicketPurchaseID\n        });\n      }else{\n        return app.translator.trans('wusong8899-welfare-ticket.forum.notifications.win-total-negative-dealer', {\n          costTotal: welfareTicketBetTotalText,\n          winTotal: welfareTicketWinTotalText,\n          profitTotal: profitTotalText,\n          purchaseID: welfareTicketPurchaseID\n        });\n      }\n\n    }else{\n\n      return app.translator.trans('wusong8899-welfare-ticket.forum.notifications.win-total', {\n        costTotal: welfareTicketBetTotalText,\n        winTotal: welfareTicketWinTotalText,\n        purchaseID: welfareTicketPurchaseID\n      });\n    }\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/NotificationGrid'];", "import app from 'flarum/forum/app';\r\nimport { extend } from 'flarum/extend';\r\nimport addSidebarMenu from './addSidebarMenu';\r\nimport addUserPage from \"./addUserPage\";\r\nimport WelfareTicket from \"./model/WelfareTicket\";\r\nimport WelfareTicketPurchase from \"./model/WelfareTicketPurchase\";\r\nimport WelfareTicketPurchaseCount from \"./model/WelfareTicketPurchaseCount\";\r\nimport WelfareTicketPurchaseSummary from \"./model/WelfareTicketPurchaseSummary\";\r\nimport WelfareTicketIndexPage from './components/WelfareTicketIndexPage';\r\nimport WelfareTicketHistoryIndexPage from './components/WelfareTicketHistoryIndexPage';\r\nimport WelfareTicketNotification from \"./components/WelfareTicketNotification\";\r\n\r\nimport NotificationGrid from \"flarum/components/NotificationGrid\";\r\n\r\napp.initializers.add('wusong8899-welfare-ticket', () => {\r\n  app.store.models.welfareTicketList = WelfareTicket;\r\n  app.store.models.welfareTicketPurchase = WelfareTicketPurchase;\r\n  app.store.models.welfareTicketPurchaseCount = WelfareTicketPurchaseCount;\r\n  app.store.models.welfareTicketPurchaseHistorySummary = WelfareTicketPurchaseSummary;\r\n  app.notificationComponents.welfareTicketPurchase = WelfareTicketNotification;\r\n\r\n  app.routes['welfareTicket'] = {\r\n    path: '/welfareTicket',\r\n    component: WelfareTicketIndexPage,\r\n  };\r\n\r\n  app.routes['welfareTicketHistory'] = {\r\n    path: '/welfareTicketHistory',\r\n    component: WelfareTicketHistoryIndexPage,\r\n  };\r\n  \r\n  addSidebarMenu();\r\n  addUserPage();\r\n\r\n  extend(NotificationGrid.prototype, \"notificationTypes\", function (items) {\r\n    items.add(\"welfareTicketPurchase\", {\r\n      name: \"welfareTicketPurchase\",\r\n      icon: \"fas fa-ticket-alt\",\r\n      label: app.translator.trans(\r\n        \"wusong8899-welfare-ticket.forum.welfare-receive-open-result\"\r\n      ),\r\n    });\r\n  });\r\n});", "import { extend } from 'flarum/extend';\nimport app from 'flarum/forum/app';\nimport IndexPage from 'flarum/components/IndexPage';\nimport LinkButton from 'flarum/components/LinkButton';\n\nexport default function addSidebarMenu() {\n  extend(IndexPage.prototype, 'navItems', function (items) {\n    let welfareTicketNameSetting = app.forum.attribute(\"welfareDisplayName\");\n    let welfareTicketDisplayName = welfareTicketNameSetting===\"\"?app.translator.trans('wusong8899-welfare-ticket.forum.welfare-display-name-default'):welfareTicketNameSetting;\n\n    items.add(\n      'WelfareTicket',\n      <LinkButton icon=\"fas fa-ticket-alt\" href={app.route('welfareTicket')}>\n        {welfareTicketDisplayName}\n      </LinkButton>,\n      15\n    );\n\n    return items;\n  });\n}\n", "import { extend } from \"flarum/extend\";\nimport UserPage from \"flarum/components/UserPage\";\nimport LinkButton from \"flarum/components/LinkButton\";\nimport WelfareTicketPurchaseHistoryPage from './components/WelfareTicketPurchaseHistoryPage';\n\nexport default function () {\n  app.routes[\"user.welfareTicketPurchaseHistory\"] = {\n    path: \"/u/:username/welfareTicketPurchaseHistory\",\n    component: WelfareTicketPurchaseHistoryPage,\n  };\n\n  extend(UserPage.prototype, \"navItems\", function (items,user) {\n      if(app.session.user){\n        const currentUserID = app.session.user.id();\n        const targetUserID = this.user.id();\n\n        if(currentUserID==targetUserID){\n          items.add(\n            \"welfareTicketPurchaseHistory\",\n            LinkButton.component({\n                href: app.route(\"user.welfareTicketPurchaseHistory\", {\n                  username: this.user.username(),\n                }),\n                icon: \"fas fa-ticket-alt\",\n              },\n              [\n                app.translator.trans(\n                  \"wusong8899-welfare-ticket.forum.welfare-ticket-purchase-history\"\n                )\n              ]\n            ),\n            10\n          );\n        }\n      }\n  });\n}\n"], "names": ["__webpack_require__", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "flarum", "core", "compat", "_setPrototypeOf", "p", "setPrototypeOf", "__proto__", "_inherits<PERSON><PERSON>e", "subClass", "superClass", "create", "constructor", "WelfareTicketPurchaseHistoryListItem", "view", "welfareTicketHistory", "this", "attrs", "welfareTicketID", "welfare_id", "toString", "substring", "moneyName", "app", "forum", "attribute", "welfareTicketData", "welfareData", "welfareTicketResult", "result", "welfareTicketResultList", "JSON", "parse", "welfareTicketSettings", "settings", "welfareTicketSelectedBallWinMultiplier", "translator", "trans", "count", "multiplier", "win1Multiplier", "win2Multiplier", "win3Multiplier", "welfareTicketPurchaseID", "id", "welfareTicketPurchaseBet", "bet", "welfareTicketPurchaseWinTotal", "win_total", "welfareTicketPurchaseWinTotalText", "replace", "welfareTicketPurchaseBetText", "welfareTicketPurchaseOpened", "parseInt", "opened", "welfareTicketPurchaseResult", "welfareResultStyle", "numbers", "welfareTicketPurchaseNumbers", "hitCountRequired", "length", "hitCount", "money", "className", "style", "map", "resultData", "isHit", "indexOf", "ballStyle", "class", "join", "profitTotal", "profitTotalText", "Math", "abs", "precisionRound", "number", "precision", "factor", "pow", "round", "Component", "WelfareTicketPurchaseHistoryList", "oninit", "vnode", "loading", "moreResults", "welfareTicketHistorySummary", "user", "params", "loadResults", "purchaseCostTotalText", "purchaseWinTotalText", "LoadingIndicator", "size", "betTotal", "winTotal", "onclick", "m", "route", "set", "purchaseCostTotal", "purchaseWinTotal", "component", "hasMoreResults", "disabled", "loadMore", "parseResults", "results", "payload", "links", "next", "push", "apply", "then", "summaryResult", "redraw", "offset", "filter", "page", "bind", "GuaGuaLePurchaseHistoryPage", "loadUser", "param", "content", "UserPage", "WelfareTicket", "Model", "assign", "title", "desc", "playback", "color", "image", "type", "cost", "purchased_total", "bet_total", "dealer_id", "assignedAt", "activated", "dealerData", "WelfareTicketPurchase", "user_id", "assigned_at", "WelfareTicketPurchaseCount", "total_purchase_count", "total_win_count", "WelfareTicketPurchaseSummary", "WelfareTicketSuccessModal", "<PERSON><PERSON>", "location", "reload", "Modal", "isDismissible", "WelfareTicketPurchaseTicketModal", "currentMoney", "welfareTicketIdString", "welfareTotalBall", "welfareTicketBall", "ball1", "Stream", "ball2", "ball3", "moneyNameText", "options", "buttonClassName", "onchange", "e", "updateSelection", "required", "step", "min", "max", "updateBetInput", "onkeyup", "hide", "selected<PERSON>all<PERSON>ount", "i", "$", "text", "WelfareBetValue", "val", "availableMoney", "onsubmit", "preventDefault", "welfareBetValue", "welfareTicketNumbers", "welfareTicketBallValue", "welfarePurchaseData", "save", "welfareTicketPurchase", "<PERSON><PERSON>", "WelfareTicketDealerSuccessModal", "WelfareTicketPurchaseDealerModal", "welfareTicketDealerBet", "dealerBet", "welfareTicketDealerBetText", "purchaseDealer", "response", "WelfareTicketListItem", "welfareTicketImage", "welfareTicketPlayback", "welfareTicketColor", "welfareDealerID", "welfareTicketAssignAt", "backgroundStyle", "getBackgroundColor", "WelfareTicketBallStyle", "welfareTicketActivated", "welfareTicketBetTotal", "welfareTicketDealerBetCut", "dealerBetCut", "welfareTicketBetTotalText", "welfareTicketDealerData", "welfareTicketDealerText", "username", "welfareTicketPurchaseButtonContainerStyle", "purchaseTicket", "controls", "src", "session", "modal", "show", "LogInModal", "str", "hash", "charCodeAt", "colour", "substr", "WelfareTicketIndexPage", "bodyClass", "welfareTicketList", "IndexPage", "listItems", "toArray", "showPurchaseHistory", "showWelfareHistory", "parsePurchaseResults", "guagualePurchaseCountList", "store", "find", "item", "Page", "WelfareTicketHistoryIndexPage", "showWelfare", "WelfareTicketNotification", "icon", "href", "notification", "subject", "is<PERSON><PERSON>er", "welfareTicketTitle", "excerpt", "welfareTicketWinTotal", "welfareTicketWinTotalText", "costTotal", "purchaseID", "Notification", "path", "extend", "items", "welfareTicketNameSetting", "welfareTicketDisplayName", "add", "routes", "WelfareTicketPurchaseHistoryPage", "LinkButton", "NotificationGrid", "name", "label"], "sourceRoot": ""}