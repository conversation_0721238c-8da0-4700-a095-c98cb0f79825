{"version": 3, "file": "admin.js", "sources": ["../src/admin/components/BiddingRankDeleteModal.jsx", "../src/admin/components/BiddingRankListItem.jsx", "../src/admin/components/SettingsPage.jsx", "../src/forum/model/BiddingRank.js", "../src/admin/index.js"], "sourcesContent": ["import Modal from 'flarum/components/Modal';\nimport Button from 'flarum/components/Button';\n\nexport default class BiddingRankDeleteModal extends Modal {\n  static isDismissibleViaBackdropClick = false;\n  static isDismissibleViaCloseButton = true;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.biddingRankListItem = this.attrs.biddingRankListItem;\n    this.loading = false;\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    return app.translator.trans('wusong8899-bidding-rank.admin.delete-confirmation');\n  }\n\n  content() {\n    //\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form-group\" style=\"text-align: center;\">\n          {Button.component(\n            {\n              className: 'Button Button--primary',\n              type: 'submit',\n              loading: this.loading,\n            },\n            app.translator.trans('wusong8899-bidding-rank.admin.confirm')\n          )}&nbsp;\n          {Button.component(\n            {\n              className: 'Button',\n              loading: this.loading,\n              onclick: () => {\n                this.hide();\n              }\n            },\n            app.translator.trans('wusong8899-bidding-rank.admin.cancel')\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  onsubmit(e) {\n    e.preventDefault();\n\n    this.loading = true;\n    this.biddingRankListItem.save({\n      isDelete:1,\n    })\n    .then(\n      () => {\n        location.reload()\n        this.loading = false;\n      }\n    );\n  }\n}\n", "import Component from \"flarum/Component\";\nimport Button from 'flarum/components/Button';\nimport BiddingRankDeleteModal from './BiddingRankDeleteModal';\nimport username from \"flarum/helpers/username\";\n\nexport default class BiddingRankListItem extends Component {\n\n  view() {\n    const {biddingRankListItem,_rankID} = this.attrs;\n    const bidValue = biddingRankListItem.attribute(\"bid\");\n    const bidUser = biddingRankListItem.fromUser();\n    const bidContent = biddingRankListItem.content();\n    const bidTitle = biddingRankListItem.title();\n\n    return (\n      <div className=\"biddingRankSettingContainer\">\n        <div style=\"float:right\">\n          {Button.component({\n            style: \"font-weight:bold;\",\n            className: 'Button Button--danger',\n            onclick: () => {\n              this.deleteItem(biddingRankListItem)\n            }\n          },\n          app.translator.trans('wusong8899-bidding-rank.admin.delete')\n          )}\n        </div>\n        <div>\n          <b>{app.translator.trans('wusong8899-bidding-rank.admin.bidding-rank-list-username')}: </b>\n          {username(bidUser)}&nbsp;|&nbsp;\n          <b>{app.translator.trans('wusong8899-bidding-rank.admin.bidding-rank-list-title')}: </b>\n          {bidTitle}&nbsp;|&nbsp;\n          <b>{app.translator.trans('wusong8899-bidding-rank.admin.bidding-rank-list-bid')}: </b>\n          {bidValue}\n        </div>\n        <div>\n          <b>{app.translator.trans('wusong8899-bidding-rank.admin.bidding-rank-list-content')}: </b>\n          {bidContent}\n        </div>\n      </div>\n    );\n  }\n\n  deleteItem(biddingRankListItem){\n    app.modal.show(BiddingRankDeleteModal, {biddingRankListItem});\n  }\n}\n", "import ExtensionPage from 'flarum/components/ExtensionPage';\nimport LoadingIndicator from 'flarum/components/LoadingIndicator';\nimport Button from 'flarum/components/Button';\nimport BiddingRankListItem from './BiddingRankListItem';\nimport app from 'flarum/admin/app';\n\n\nexport default class BiddingRankSettings extends ExtensionPage {\n  oninit(attrs) {\n    super.oninit(attrs);\n    this.loading = true;\n    this.moreResults = false;\n    this.biddingRankList = [];\n    this.loadResults();\n  }\n\n  content() {\n    let loading;\n\n    if(this.loading){\n      loading = LoadingIndicator.component({ size: \"large\" });\n    }\n\n    return (\n      <div className=\"ExtensionPage-settings FlarumBadgesPage\">\n        <div className=\"container\">\n\n          <ul style=\"padding:0px;list-style-type: none;\">\n            {this.biddingRankList.map((biddingRankListItem) => {\n              return (\n                <li itemID={biddingRankListItem.id()} style=\"margin-top:5px;background: var(--body-bg);\">\n                  {BiddingRankListItem.component({ biddingRankListItem })}\n                </li>\n              );\n            })}\n          </ul>\n\n          {!this.loading && this.biddingRankList.length===0 && (\n            <div>\n              <div style=\"font-size:1.4em;color: var(--muted-more-color);text-align: center;line-height: 100px;\">{app.translator.trans(\"wusong8899-bidding-rank.forum.list-empty\")}</div>\n            </div>\n          )}\n\n          {!loading && this.hasMoreResults() && (\n            <div style=\"text-align:center;padding:20px\">\n              <Button className={'Button Button--primary'} disabled={this.loading} loading={this.loading} onclick={() => this.loadMore()}>\n                {app.translator.trans('wusong8899-bidding-rank.admin.list-load-more')}\n              </Button>\n            </div>\n          )}\n\n          {loading && <div className=\"BiddingRankContent-loadMore\">{loading}</div>}\n\n        </div>\n      </div>\n    );\n  }\n  \n  hasMoreResults() {\n    return this.moreResults;\n  }\n\n  loadMore() {\n    this.loading = true;\n    this.loadResults(this.biddingRankList.length);\n  }\n\n  parseResults(results) {\n    this.moreResults = !!results.payload.links && !!results.payload.links.next;\n    [].push.apply(this.biddingRankList, results);\n\n    return app.store\n      .find(\"biddingRankHistory\")\n      .catch(() => {})\n      .then((result) => {\n        this.hasBid = result.length>0;\n        \n        if(this.hasBid){\n          this.biddingRankHistoryData = result[0];\n        }\n\n        this.loading = false;\n        m.redraw();\n\n        return results;\n      });\n    \n    return results;\n  }\n\n  loadResults(offset = 0) {\n    return app.store\n      .find(\"biddingRankList\", {\n        page: {\n          offset\n        },\n      })\n      .catch(() => {})\n      .then(this.parseResults.bind(this));\n  }\n\n}\n", "import Model from \"flarum/Model\";\n\nexport default class BiddingRank extends Model {}\nObject.assign(BiddingRank.prototype, {\n  id: Model.attribute(\"id\"),\n  title: Model.attribute(\"title\"),\n  content: Model.attribute(\"content\"),\n  user_id: Model.attribute(\"user_id\"),\n  url: Model.attribute(\"url\"),\n  bid: Model.attribute(\"bid\"),\n  fromUser: Model.hasOne(\"fromUser\"),\n});\n", "import SettingsPage from './components/SettingsPage';\nimport BiddingRank from \"../forum/model/BiddingRank\";\n\napp.initializers.add('wusong8899-client1-bidding-rank-content', () => {\n  app.store.models.biddingRankList = BiddingRank;\n  app.extensionData.for('wusong8899-bidding-rank-content').registerPage(SettingsPage);\n});\n"], "names": ["BiddingRankDeleteModal", "Modal", "vnode", "<PERSON><PERSON>", "e", "BiddingRankListItem", "Component", "biddingRankListItem", "_rankID", "bidValue", "bidUser", "bidContent", "bidTitle", "username", "BiddingRankSettings", "ExtensionPage", "attrs", "loading", "LoadingIndicator", "app", "results", "result", "offset", "BiddingRank", "Model", "SettingsPage"], "mappings": "wCAGA,MAAqBA,UAA+BC,CAAM,CACxD,OAAO,8BAAgC,GACvC,OAAO,4BAA8B,GAErC,OAAOC,EAAO,CACZ,MAAM,OAAOA,CAAK,EAClB,KAAK,oBAAsB,KAAK,MAAM,oBACtC,KAAK,QAAU,EACjB,CAEA,WAAY,CACV,MAAO,cACT,CAEA,OAAQ,CACN,OAAO,IAAI,WAAW,MAAM,mDAAmD,CACjF,CAEA,SAAU,CAER,OACE,EAAC,MAAA,CAAI,UAAU,YAAA,EACb,EAAC,OAAI,UAAU,aAAa,MAAM,qBAAA,EAC/BC,EAAO,UACN,CACE,UAAW,yBACX,KAAM,SACN,QAAS,KAAK,OAAA,EAEhB,IAAI,WAAW,MAAM,uCAAuC,CAAA,EAC5D,IACDA,EAAO,UACN,CACE,UAAW,SACX,QAAS,KAAK,QACd,QAAS,IAAM,CACb,KAAK,KAAA,CACP,CAAA,EAEF,IAAI,WAAW,MAAM,sCAAsC,CAAA,CAE/D,CACF,CAEJ,CAEA,SAASC,EAAG,CACVA,EAAE,eAAA,EAEF,KAAK,QAAU,GACf,KAAK,oBAAoB,KAAK,CAC5B,SAAS,CAAA,CACV,EACA,KACC,IAAM,CACJ,SAAS,OAAA,EACT,KAAK,QAAU,EACjB,CAAA,CAEJ,CACF,CC1DA,MAAqBC,UAA4BC,CAAU,CAEzD,MAAO,CACL,KAAM,CAAC,oBAAAC,EAAoB,QAAAC,CAAA,EAAW,KAAK,MACrCC,EAAWF,EAAoB,UAAU,KAAK,EAC9CG,EAAUH,EAAoB,SAAA,EAC9BI,EAAaJ,EAAoB,QAAA,EACjCK,EAAWL,EAAoB,MAAA,EAErC,OACE,EAAC,OAAI,UAAU,6BAAA,IACZ,MAAA,CAAI,MAAM,eACRJ,EAAO,UAAU,CAChB,MAAO,oBACP,UAAW,wBACX,QAAS,IAAM,CACb,KAAK,WAAWI,CAAmB,CACrC,CAAA,EAEF,IAAI,WAAW,MAAM,sCAAsC,CAAA,CAE7D,EACA,EAAC,MAAA,OACE,IAAA,KAAG,IAAI,WAAW,MAAM,0DAA0D,EAAE,IAAE,EACtFM,EAASH,CAAO,EAAE,MACnB,EAAC,SAAG,IAAI,WAAW,MAAM,uDAAuD,EAAE,IAAE,EACnFE,EAAS,MACV,EAAC,IAAA,KAAG,IAAI,WAAW,MAAM,qDAAqD,EAAE,IAAE,EACjFH,CACH,IACC,MAAA,KACC,EAAC,IAAA,KAAG,IAAI,WAAW,MAAM,yDAAyD,EAAE,IAAE,EACrFE,CACH,CACF,CAEJ,CAEA,WAAWJ,EAAoB,CAC7B,IAAI,MAAM,KAAKP,EAAwB,CAAC,oBAAAO,EAAoB,CAC9D,CACF,CCvCA,MAAqBO,UAA4BC,CAAc,CAC7D,OAAOC,EAAO,CACZ,MAAM,OAAOA,CAAK,EAClB,KAAK,QAAU,GACf,KAAK,YAAc,GACnB,KAAK,gBAAkB,CAAA,EACvB,KAAK,YAAA,CACP,CAEA,SAAU,CACR,IAAIC,EAEJ,OAAG,KAAK,UACNA,EAAUC,EAAiB,UAAU,CAAE,KAAM,QAAS,KAIrD,MAAA,CAAI,UAAU,yCAAA,EACb,EAAC,OAAI,UAAU,WAAA,EAEb,EAAC,KAAA,CAAG,MAAM,oCAAA,EACP,KAAK,gBAAgB,IAAKX,GAEvB,EAAC,KAAA,CAAG,OAAQA,EAAoB,GAAA,EAAM,MAAM,4CAAA,EACzCF,EAAoB,UAAU,CAAE,oBAAAE,CAAA,CAAqB,CACxD,CAEH,CACH,EAEC,CAAC,KAAK,SAAW,KAAK,gBAAgB,SAAS,GAC9C,EAAC,MAAA,KACC,EAAC,MAAA,CAAI,MAAM,uFAAA,EAAyFY,EAAI,WAAW,MAAM,0CAA0C,CAAE,CACvK,EAGD,CAACF,GAAW,KAAK,eAAA,GAChB,EAAC,MAAA,CAAI,MAAM,gCAAA,EACT,EAACd,EAAA,CAAO,UAAW,yBAA0B,SAAU,KAAK,QAAS,QAAS,KAAK,QAAS,QAAS,IAAM,KAAK,UAAS,EACtHgB,EAAI,WAAW,MAAM,8CAA8C,CACtE,CACF,EAGDF,GAAW,EAAC,MAAA,CAAI,UAAU,6BAAA,EAA+BA,CAAQ,CAEpE,CACF,CAEJ,CAEA,gBAAiB,CACf,OAAO,KAAK,WACd,CAEA,UAAW,CACT,KAAK,QAAU,GACf,KAAK,YAAY,KAAK,gBAAgB,MAAM,CAC9C,CAEA,aAAaG,EAAS,CACpB,YAAK,YAAc,CAAC,CAACA,EAAQ,QAAQ,OAAS,CAAC,CAACA,EAAQ,QAAQ,MAAM,KACtE,CAAA,EAAG,KAAK,MAAM,KAAK,gBAAiBA,CAAO,EAEpCD,EAAI,MACR,KAAK,oBAAoB,EACzB,MAAM,IAAM,CAAC,CAAC,EACd,KAAME,IACL,KAAK,OAASA,EAAO,OAAO,EAEzB,KAAK,SACN,KAAK,uBAAyBA,EAAO,CAAC,GAGxC,KAAK,QAAU,GACf,EAAE,OAAA,EAEKD,EACR,CAGL,CAEA,YAAYE,EAAS,EAAG,CACtB,OAAOH,EAAI,MACR,KAAK,kBAAmB,CACvB,KAAM,CACJ,OAAAG,CAAA,CACF,CACD,EACA,MAAM,IAAM,CAAC,CAAC,EACd,KAAK,KAAK,aAAa,KAAK,IAAI,CAAC,CACtC,CAEF,CCnGe,MAAMC,UAAoBC,CAAM,CAAA,CAC/C,OAAO,OAAOD,EAAY,UAAW,CACnC,GAAIC,EAAM,UAAU,IAAI,EACxB,MAAOA,EAAM,UAAU,OAAO,EAC9B,QAASA,EAAM,UAAU,SAAS,EAClC,QAASA,EAAM,UAAU,SAAS,EAClC,IAAKA,EAAM,UAAU,KAAK,EAC1B,IAAKA,EAAM,UAAU,KAAK,EAC1B,SAAUA,EAAM,OAAO,UAAU,CACnC,CAAC,ECRD,IAAI,aAAa,IAAI,0CAA2C,IAAM,CACpE,IAAI,MAAM,OAAO,gBAAkBD,EACnC,IAAI,cAAc,IAAI,iCAAiC,EAAE,aAAaE,CAAY,CACpF,CAAC"}