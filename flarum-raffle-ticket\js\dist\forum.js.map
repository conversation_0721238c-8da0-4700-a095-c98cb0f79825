{"version": 3, "file": "forum.js", "mappings": "MACA,IAAIA,EAAsB,CCA1BA,EAAyBC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,WAC7B,IAAOF,EAAiB,QACxB,IAAM,EAEP,OADAD,EAAoBI,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GCLRF,EAAwB,CAACM,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXP,EAAoBS,EAAEF,EAAYC,KAASR,EAAoBS,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3ER,EAAwB,CAACc,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFf,EAAyBM,IACH,oBAAXa,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeL,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeL,EAAS,aAAc,CAAEe,OAAO,M,+BCLvD,MAAM,EAA+BC,OAAOC,KAAKC,OAAO,a,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAe,OCA1D,EAA+BF,OAAOC,KAAKC,OAAO,wB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,yB,aCAzC,SAASC,EAAgBhB,EAAGiB,GAMzC,OALAD,EAAkBf,OAAOiB,gBAAkB,SAAyBlB,EAAGiB,GAErE,OADAjB,EAAEmB,UAAYF,EACPjB,GAGFgB,EAAgBhB,EAAGiB,GCLb,SAASG,EAAeC,EAAUC,GAC/CD,EAASd,UAAYN,OAAOsB,OAAOD,EAAWf,WAC9Cc,EAASd,UAAUiB,YAAcH,EACjCH,EAAeG,EAAUC,GCJ3B,MAAM,EAA+BT,OAAOC,KAAKC,OAAO,mB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,4B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,+B,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAkB,U,aCA9BF,OAAOC,KAAKC,OAAO,mBAAxD,MCAM,EAA+BF,OAAOC,KAAKC,OAAO,qB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,oB,aCInCU,EAAAA,SAAAA,G,oFAGnBC,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACbC,KAAKC,gBAAkBD,KAAKE,MAAMD,gBAClCD,KAAKG,YAAc,GACnBH,KAAKI,OAAS,EACdJ,KAAKK,iBAAmB,G,EAG1BC,8BAAA,SAA8BP,GAC5BC,KAAKO,c,EAGPC,UAAA,WACE,MAAO,gB,EAGTC,MAAA,WAEE,OADqBT,KAAKC,gBAAgBS,eACtBD,S,EAGtBE,QAAA,WAAU,IAEJC,EAFI,OACJP,EAAmB,EAEnBQ,EAAqB,GACnBC,EAAiBC,KAAKC,MAAMhB,KAAKC,gBAAgBgB,mBACjDC,EAAYC,IAAAA,MAAAA,UAAoB,8BAAgC,UAEtE,IAAI,IAAIC,KAASN,EAAe,CAC9B,IAAIO,EAAYC,SAASR,EAAeM,IACpCG,EAAWC,WAAWJ,GACpBK,EAAeP,EAAUQ,QAAQ,UAAWH,GAC5CI,EAAWR,IAAAA,WAAAA,MAAqB,2CAEtCN,EAAmBe,KAAK,CAACC,KAAKJ,EAAa,KAAKX,EAAeM,GAAOO,EAASJ,SAASA,IAE1E,IAAXA,IACDlB,GAAmBkB,EAASF,GAOhC,OAHArB,KAAKK,iBAAmBL,KAAK8B,eAAezB,EAAiB,GAC7DO,EAAuBO,IAAAA,WAAAA,MAAqB,+CAA+C,CAACY,MAAMb,EAAUQ,QAAQ,UAAW1B,KAAKK,oBAGlI,SAAKG,UAAU,cACb,SAAKA,UAAU,QACb,SAAKA,UAAU,cACb,SAAKwB,GAAG,0BAA0BC,SAAUjC,KAAKM,8BAA8B4B,KAAKlC,MAAOmC,MAAM,yFAC/F,YAAQA,MAAM,mEAAmEH,GAAG,iBACpF,SAAKA,GAAG,8BAA8BG,MAAM,6FAC1C,SAAKA,MAAM,mDAAmDvB,GAC9D,SAAKuB,MAAM,8IACRtB,EAAmBuB,KAAI,SAACtB,GACvB,IAAMuB,EAAqBvB,EAAee,KACpCS,EAAyBxB,EAAeS,SACxCgB,EAAc,EAAKC,mBAAmBF,EAAuB,SAGnE,OACE,SAAKH,MAHqB,sDAAsDI,EAAY,kDAIzFF,UAQf,SAAK7B,UAAU,aAAa2B,MAAM,uBAC/BM,IAAAA,UACC,CACEjC,UAAW,yBACXkC,QAAS1C,KAAK0C,QACdC,QAAS,WAEP,EAAKC,SAGTzB,IAAAA,WAAAA,MAAqB,4C,EAQjCZ,WAAA,WAEE,IAAMsC,EAAcC,EAAE,4BAA4BC,SAASC,QACrDC,EAAe,IAErBH,EAAE,4BAA4BE,MAAMH,GACpCC,EAAE,4BAA4BI,OAAOD,GACrCH,EAAE,gCAAgCE,MAAMH,EAAY,GACpDC,EAAE,gCAAgCI,OAAOD,KACzCE,SAASC,eAAe,gBAAgBJ,MAAQH,EAChDM,SAASC,eAAe,gBAAgBF,OAASD,EAEjD,IAAMvC,EAAeV,KAAKC,gBAAgBS,eACpC2C,EAAgB3C,EAAa4C,QAC7BC,EAAgB7C,EAAaD,QAC7B+C,EAAa9C,EAAasB,KAC1ByB,EAAiBzD,KAAKI,OAASJ,KAAKC,gBAAgBG,SAI1D,GAFAJ,KAAK0D,aAAeP,SAASC,eAAe,gBAExB,IAAjBK,EAAmB,CACpBzD,KAAK2D,WAAY,EACjB3D,KAAK4D,UACL5D,KAAK6D,IAAM7D,KAAK0D,aAAaI,WAAW,KAAK,CAACC,oBAAmB,IAEjE,IAAIC,EAAQhE,KAEZ,GAAGqD,EAAc,CACf,IAAIC,EAAQ,IAAIW,MAChBX,EAAMY,IAAMb,EACZC,EAAMa,OAAS,WACbH,EAAMH,IAAIO,UAAUd,EAAO,EAAG,EAAGU,EAAMN,aAAaV,MAAOgB,EAAMN,aAAaR,QAC9Ec,EAAMH,IAAIQ,YACVL,EAAMH,IAAIS,KAAK,EAAG,EAAGzB,EAAaI,GAClCe,EAAMH,IAAIU,UAAY,sBACtBP,EAAMH,IAAIW,OACVR,EAAMS,SAAS5B,EAAYI,QAE1B,CACH,IAAMyB,EAAgB1E,KAAKwC,mBAAmBe,EAAc,IAAIC,GAChExD,KAAK6D,IAAIQ,YACTrE,KAAK6D,IAAIS,KAAK,EAAG,EAAGzB,EAAaI,GACjCjD,KAAK6D,IAAIU,UAAYG,EACrB1E,KAAK6D,IAAIW,OACTxE,KAAKyE,SAAS5B,EAAYI,GAG5BjD,KAAK2E,MAAQ,IAAIV,MACjBjE,KAAK2E,MAAMT,IAAM,y9GAEjBlE,KAAK0D,aAAaM,MAAQhE,KAC1BA,KAAK0D,aAAakB,iBAAiB,YAAa5E,KAAK6E,iBAAiB,GACtE7E,KAAK0D,aAAakB,iBAAiB,aAAc5E,KAAK6E,iBAAiB,GACvE7E,KAAK0D,aAAakB,iBAAiB,YAAa5E,KAAK8E,iBAAiB,GACtE9E,KAAK0D,aAAakB,iBAAiB,YAAa5E,KAAK8E,iBAAiB,GACtE9E,KAAK0D,aAAakB,iBAAiB,UAAW5E,KAAK+E,eAAe,GAClE/E,KAAK0D,aAAakB,iBAAiB,WAAY5E,KAAK+E,eAAe,QAEnE/E,KAAK0D,aAAavB,MAAM6C,QAAU,OAClC7B,SAASC,eAAe,+BAA+BjB,MAAM8C,WAAa,W,EAI9ER,SAAA,SAAS5B,EAAYI,GACnB,IAAMiC,EAAgB/D,IAAAA,WAAAA,MAAqB,4CACrCgE,EAAoBnF,KAAKC,gBAAgBmF,iBAAiB,IAAIjE,IAAAA,WAAAA,MAAqB,2CAEzFnB,KAAK6D,IAAIwB,KAAO,aAChBrF,KAAK6D,IAAIyB,UAAY,SACrBtF,KAAK6D,IAAI0B,aAAe,SACxBvF,KAAK6D,IAAIU,UAAY,QACrBvE,KAAK6D,IAAI2B,SAASN,EAAe5D,SAASuB,EAAY,GAAIvB,SAAS2B,EAAa,GAAG,IAEnFjD,KAAK6D,IAAIwB,KAAO,aAChBrF,KAAK6D,IAAI2B,SAASL,EAAmB7D,SAASuB,EAAY,GAAIvB,SAAS2B,EAAa,GAAG,IACvFE,SAASC,eAAe,+BAA+BjB,MAAM8C,WAAa,W,EAG5EzC,mBAAA,SAAmBiD,GAEjB,IADA,IAAIC,EAAO,EACFC,EAAI,EAAGA,EAAIF,EAAIG,OAAQD,IAC9BD,EAAOD,EAAII,WAAWF,KAAOD,GAAQ,GAAKA,GAG5C,IADA,IAAII,EAAS,IACJH,EAAI,EAAGA,EAAI,EAAGA,IAErBG,IAAW,MADEJ,GAAa,EAAJC,EAAU,KACRI,SAAS,KAAKC,QAAQ,GAEhD,OAAOF,G,EAGTG,gBAAA,SAAgBC,EAAQC,GACtB,OAAOC,KAAKC,KAAKD,KAAKE,IAAIH,EAAOI,EAAIL,EAAOK,EAAG,GAAKH,KAAKE,IAAIH,EAAOK,EAAIN,EAAOM,EAAG,K,EAGpFC,aAAA,SAAaP,EAAQC,GACnB,OAAOC,KAAKM,MAAOP,EAAOI,EAAIL,EAAOK,EAAGJ,EAAOK,EAAIN,EAAOM,I,EAG5DG,kBAAA,SAAkBC,KACXA,GAAUA,EAAS,KAAKA,EAAS,GAUtC,IARA,IAAI/D,EAAe7C,KAAK0D,aAAaV,MACjCC,EAAejD,KAAK0D,aAAaR,OAEjC2D,EADW7G,KAAK6D,IAAIiD,aAAa,EAAG,EAAGjE,EAAaI,GAClC8D,KAClBC,EAAWH,EAAMjB,OACjBqB,EAAYD,EAAIJ,EAChBM,EAAW,EAEPvB,EAAIuB,EAAQ,EAAGvB,EAAIqB,EAAGrB,GAAKiB,EACN,IAAvBtF,SAASuF,EAAMlB,KACjBuB,IAIJ,OAAOd,KAAKe,MAAOD,EAAQD,EAAS,M,EAGtCG,SAAA,SAASC,EAAG3D,GACV,IAAI4D,EAAU,EAAGC,EAAU,EAE3B,QAAkCC,IAA9B9D,EAAa+D,aACf,GACEH,GAAW5D,EAAagE,WACxBH,GAAW7D,EAAaiE,gBAChBjE,EAAeA,EAAa+D,cAMxC,MAAO,CAAClB,GAHFc,EAAEO,OAASP,EAAEQ,QAAQ,GAAGC,SAAWR,EAAUS,OAAOC,YAG3CxB,GAFTa,EAAEY,OAASZ,EAAEQ,QAAQ,GAAGK,SAAWX,EAAUQ,OAAOI,c,EAK5DC,iBAAA,SAAiBC,IACfA,EAAiBA,GAAkB,IAEf,KAClBvF,EAAE,iBAAiBwF,OAAO,IAAI,GAC9BxF,EAAE,iBAAiByF,IAAI,iBAAiB,U,EAI5C1D,gBAAA,SAAgBwC,GACd,IAAIrD,EAAQqD,EAAEmB,cAAcxE,MAC5BA,EAAML,WAAY,EAClBK,EAAMJ,UAAYI,EAAMoD,SAASC,EAAGrD,EAAMN,cAExB,IAAfM,EAAM5D,SACP4D,EAAM5D,OAAS,EACf4D,EAAMyE,e,EAIVA,WAAA,WACE,IAAMC,EAAqB1I,KAAKC,gBAAgB+B,KAChDhC,KAAKC,gBAAgB0I,KAAK,CAACD,mBAAAA,IAE3B,IAAMrI,EAAmBL,KAAKK,iBAC9Bc,IAAAA,QAAAA,KAAAA,KAAAA,WAAAA,OAAwCd,EAErCc,IAAAA,MAAAA,KAAAA,iCACDA,IAAAA,MAAAA,KAAAA,+BAAAA,IAAAA,KAAAA,WAAAA,UAA4Ed,I,EAIhFyB,eAAA,SAAe8G,EAAQC,GACrB,IAAIC,EAAS1C,KAAKE,IAAI,GAAIuC,GAC1B,OAAOzC,KAAKe,MAAMyB,EAASE,GAAUA,G,EAGvChE,gBAAA,SAAgBuC,GACd,IAAIrD,EAAQqD,EAAEmB,cAAcxE,MAE5B,GAAKA,EAAML,UAAX,CAEA0D,EAAE0B,iBAOF,IALA,IAGIxC,EAAGC,EAHHwC,EAAehF,EAAMoD,SAASC,EAAGrD,EAAMN,cACvCuF,EAAOjF,EAAMiC,gBAAgBjC,EAAMJ,UAAWoF,GAC9CE,EAAQlF,EAAMyC,aAAazC,EAAMJ,UAAWoF,GAGvCrD,EAAI,EAAGA,EAAIsD,EAAMtD,IACxBY,EAAIvC,EAAMJ,UAAU2C,EAAKH,KAAK+C,IAAID,GAASvD,EAAK,GAChDa,EAAIxC,EAAMJ,UAAU4C,EAAKJ,KAAKgD,IAAIF,GAASvD,EAAK,GAEhD3B,EAAMH,IAAIwF,yBAA2B,kBACrCrF,EAAMH,IAAIO,UAAUJ,EAAMW,MAAO4B,EAAGC,GAGtCxC,EAAMJ,UAAYoF,EAClBhF,EAAMoE,iBAAiBpE,EAAM2C,uB,EAG/B5B,cAAA,SAAcsC,GACAA,EAAEmB,cAAcxE,MACtBL,WAAY,G,EAhSD9D,CAA6ByJ,KAA7BzJ,EACZ0J,eAAgB,E,ICAJC,EAAAA,SAAAA,G,oFAGnB1J,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACbC,KAAKU,aAAeV,KAAKE,MAAMQ,aAC/BV,KAAKkB,UAAYC,IAAAA,MAAAA,UAAoB,8BAAgC,UACrEnB,KAAKyJ,UAAY,G,EAGnBjJ,UAAA,WACE,MAAO,gB,EAGTC,MAAA,WACE,OAAOT,KAAKU,aAAaD,S,EAG3BE,QAAA,WAAU,WACF+I,EAAgB1J,KAAKkB,UAAUQ,QAAQ,UAAWP,IAAAA,QAAAA,KAAAA,UAA2B,UAC7EwI,EAAgB3J,KAAKkB,UAAUQ,QAAQ,UAAW,GAClDkI,EAAkB5J,KAAKU,aAAamJ,SAAS7J,KAAKU,aAAaoJ,YAErE,OACE,SAAKtJ,UAAU,cACb,SAAKA,UAAU,QACb,SAAKA,UAAU,cACb,eAAQW,IAAAA,WAAAA,MAAqB,iDAAiD,CAAC+F,MAAM0C,KACrF,WAAOG,YAAa5I,IAAAA,WAAAA,MAAqB,4DAA6Da,GAAG,wBAAwBgI,UAAQ,EAACxJ,UAAU,cAAcyJ,KAAK,SAASC,KAAK,IAAIC,IAAI,IAAIC,IAAKR,EAAiBS,SAAU,kBAAM,EAAKC,sBAAsBC,QAAS,kBAAM,EAAKD,wBACtR,SAAKnI,MAAM,oBAAoBhB,IAAAA,WAAAA,MAAqB,sDAAuDuI,GAC3G,aAAMvI,IAAAA,WAAAA,MAAqB,gDAAgD,UAAMa,GAAG,yBAAyB2H,KAE/G,SAAKnJ,UAAU,aAAa2B,MAAM,uBAC/BM,IAAAA,UACC,CACEjC,UAAW,yBACXyJ,KAAM,SACNvH,QAAS1C,KAAK0C,SAEhBvB,IAAAA,WAAAA,MAAqB,mDAPzB,IASGsB,IAAAA,UACC,CACEjC,UAAW,mCACXkC,QAAS1C,KAAK0C,QACdC,QAAS,WACP,EAAKC,SAGTzB,IAAAA,WAAAA,MAAqB,sD,EAQjCmJ,mBAAA,WAEE,IAAIE,EAAwB1H,EAAE,0BAA0B2H,MAClDb,EAAkB5J,KAAKU,aAAamJ,SAAS7J,KAAKU,aAAaoJ,YAElEU,EAAsBZ,IACvBY,EAAwBZ,EACxB9G,EAAE,0BAA0B2H,IAAIb,IAGlC,IAAMc,EAAe1K,KAAKyJ,UAAYe,EAAsBxK,KAAKU,aAAaiK,OACxEC,EAAuB5K,KAAKkB,UAAUQ,QAAQ,UAAWgJ,GAE/D5H,EAAE,0BAA0BjB,KAAK+I,I,EAGnCC,SAAA,SAASxD,GAAG,WACVA,EAAE0B,iBAEF,IAAMyB,EAAwB1H,EAAE,0BAA0B2H,MACpDjH,EAAaxD,KAAKU,aAAasB,KAErC,GAAGwI,EAAsB,EAAE,CACzB,IAAMM,EAAuB,CAC3BtH,WAAWA,EACXgH,sBAAsBA,GAGxBxK,KAAK0C,SAAU,EAEfvB,IAAAA,MAAAA,aACgB,oBACbwH,KAAKmC,GACLC,MACC,SAAC9K,GACCkB,IAAAA,MAAAA,YAAsBlB,GACtBkB,IAAAA,MAAAA,KAAetB,EAAsB,CAACI,gBAAAA,IACtCkB,IAAAA,QAAAA,KAAAA,KAAAA,WAAAA,OAAwC,EAAKsI,UAC7C,EAAK/G,SAAU,KARrB,OAWS,SAAC2E,GACN,EAAK3E,SAAU,O,EAnGJ8G,CAA8BF,KAA9BE,EACZD,eAAgB,E,ICFJyB,EAAAA,SAAAA,G,oFAGnBlL,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACbC,KAAKU,aAAeV,KAAKE,MAAMQ,c,EAGjCF,UAAA,WACE,MAAO,gB,EAGTC,MAAA,WACE,OAAOT,KAAKU,aAAaD,S,EAG3BE,QAAA,WAAU,WACR,OACE,SAAKH,UAAU,cACb,SAAKA,UAAU,QACb,SAAKA,UAAU,cACb,SAAK2B,MAAM,uCAAuChB,IAAAA,WAAAA,MAAqB,oDAEzE,SAAKX,UAAU,aAAa2B,MAAM,uBAC/BM,IAAAA,UACC,CACEjC,UAAW,yBACXkC,QAAS1C,KAAK0C,QACdC,QAAS,WACP,EAAKC,SAGTzB,IAAAA,WAAAA,MAAqB,kD,EAhCd6J,CAA6B1B,KAA7B0B,EACZzB,eAAgB,ECLzB,MAAM,EAA+BtK,OAAOC,KAAKC,OAAO,yB,aCAnBF,OAAOC,KAAKC,OAAO,oB,ICQnC8L,EAAAA,SAAAA,G,oFACnBnL,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACbC,KAAK0C,SAAU,EACf1C,KAAKkL,qBAAuB,GAC5BlL,KAAKmL,e,EAGPC,KAAA,WAgBE,IAhBK,WACE1K,EAAgBV,KAAKE,MAArBQ,aACD8C,EAAa9C,EAAasB,KAC1BuB,EAAgB7C,EAAaD,QAC7B4K,EAAe3K,EAAa4K,OAC5BC,EAAe7K,EAAaiK,OAC5BtH,EAAgB3C,EAAa4C,QAC7BoB,EAAgBhE,EAAa8K,QAC7BC,EAAoB/K,EAAamJ,SAASnJ,EAAaoJ,YAEvD4B,GADuBhL,EAAaoK,uBAClB,yBAAyC,OAAhBzH,EAAqB,GAAGA,GAAe,wBAAwBqB,GAA4B1E,KAAKwC,mBAAmBe,EAAc,IAAIC,IAAa,KAC7LmI,EAA4B,OAAhBtI,EAAqB,GAAG,mCACpCnC,EAAYC,IAAIyK,MAAMC,UAAU,8BAAgC,UAChEX,EAAuBlL,KAAKkL,qBAC9BY,EAA4B,GAExBnG,EAAE,EAAEA,EAAEuF,EAAqBtF,OAAOD,IAAI,CAC5C,IACMoG,EADeb,EAAqBvF,GAAGqG,gBACPH,UAAU,YAC1CI,EAAgBf,EAAqBvF,GAAGP,iBACxC8G,EAAmBhB,EAAqBvF,GAAGwG,qBAC3CC,EAAuBlL,EAAUQ,QAAQ,UAAWwK,GAE1DJ,GAA4B3K,IAAIkL,WAAWC,MAAM,+CAA+C,CAC9FP,iBAAiBA,EACjBG,iBAAiBE,EACjBH,cAAcA,IACZM,KAAK,IAEN5G,EAAE3F,KAAKkL,qBAAqBtF,OAAO,IACpCkG,GAA2B,OAIA,KAA5BA,IACDA,EAA4B3K,IAAIkL,WAAWC,MAAM,uDAGnD,IAAME,EAAoBf,GAAmB,EAAE,IAAItK,IAAIkL,WAAWC,MAAM,wDAAwD,KAAK5L,EAAaD,QAAQC,EAAaD,QACjKgM,EAAwBhB,GAAmB,EAAEtK,IAAIkL,WAAWC,MAAM,mDAAmDnL,IAAIkL,WAAWC,MAAM,oDAAoD,CAACI,UAAUjB,IACzMkB,EAAmBzL,EAAUQ,QAAQ,UAAW6J,GAGhDqB,EAAuB,uBAFR1B,EAAqBtF,OAAO,EAAkC,GAA/BsF,EAAqBtF,OAAO,GAAK,IAEnB,oBAElE,OACE,SAAKiH,MAAM,oBAAoBlK,QAAS,kBAAM,EAAKmK,YAAYpM,EAAa+K,IAAoBtJ,MAAOuJ,GACrG,SAAKmB,MAAM,eAAe1K,MAAOwJ,GAC/B,SAAKkB,MAAM,iBACRL,GAEH,SAAKrK,MAAM,eACT,SAAK0K,MAAM,uBACRxB,IAGL,SAAKrJ,GAAG,2BAA2B6K,MAAM,6BACrC7M,KAAK0C,SACL,OAAGmK,MAAM,WACR,UAAM1K,MAAOyK,GACRd,KAKV,SAAKe,MAAM,mBACR1L,IAAIkL,WAAWC,MAAM,+CAA+C,CAAC3B,KAAKgC,IAD7E,MAEGF,M,EAOXK,YAAA,SAAYpM,EAAa+K,GAEnBtK,IAAI4L,QAAQC,KACd7L,IAAI8L,MAAMC,KAAKzB,GAAmB,EAAET,EAAqBxB,EAAuB,CAAC9I,aAAAA,IAEjFS,IAAI8L,MAAMC,KAAKC,M,EAInB3K,mBAAA,SAAmBiD,GAEjB,IADA,IAAIC,EAAO,EACFC,EAAI,EAAGA,EAAIF,EAAIG,OAAQD,IAC9BD,EAAOD,EAAII,WAAWF,KAAOD,GAAQ,GAAKA,GAG5C,IADA,IAAII,EAAS,IACJH,EAAI,EAAGA,EAAI,EAAGA,IAErBG,IAAW,MADEJ,GAAa,EAAJC,EAAU,KACRI,SAAS,KAAKC,QAAQ,GAEhD,OAAOF,G,EAGTsH,aAAA,SAAaC,GAKX,MAJA,GAAGzL,KAAK0L,MAAMtN,KAAKkL,qBAAsBmC,GACzCrN,KAAK0C,SAAU,EACf6K,EAAEC,SAEKH,G,EAGTlC,YAAA,SAAYsC,GACV,YADsB,IAAZA,IAAAA,EAAS,GACZtM,IAAIuM,MACRC,KAAK,uBAAwB,CAC5BC,MAAO5N,KAAKE,MAAMQ,aAAasB,OAF5B,OAIE,eACN+I,KAAK/K,KAAKoN,aAAalL,KAAKlC,Q,EAtHdiL,CAAyB4C,KCDzBC,EAAAA,SAAAA,G,oFACnBhO,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACbC,KAAK+N,UAAY,aACjB/N,KAAK0C,SAAU,EACf1C,KAAKgO,aAAe,GACpBhO,KAAKmL,e,EAIPC,KAAA,WAAO,IACD1I,EADC,OAOL,OAJI1C,KAAK0C,UACPA,EAAUuL,IAAAA,UAA2B,CAAEC,KAAM,WAI7C,SAAK1N,UAAU,aACZ2N,IAAAA,UAAAA,OAED,SAAK3N,UAAU,aACb,SAAKA,UAAU,oBACb,SAAKA,UAAU,yBACb,YAAK4N,GAAAA,CAAUD,IAAAA,UAAAA,eAAmCE,aAGpD,SAAKxB,MAAM,2BACT,SAAK1K,MAAM,wBACRM,IAAAA,UAAiB,CACdjC,UAAW,SACXmC,QAAS,WACP,EAAK2L,wBAGTnN,IAAIkL,WAAWC,MAAM,gDAGxBtM,KAAKgO,aAAa5L,KAAI,SAAC1B,GACtB,OACE,SAAKyB,MAAM,mBACR8I,EAAiBsD,UAAU,CAAE7N,aAAAA,SAKlCV,KAAK0C,SAAsC,IAA3B1C,KAAKgO,aAAapI,QAClC,aACE,SAAKzD,MAAM,uGAAuGhB,IAAIkL,WAAWC,MAAM,8CAI1I5J,GAAW,SAAKlC,UAAU,qBAAqBkC,Q,EAS5D0K,aAAA,SAAaC,GAIX,MAHA,GAAGzL,KAAK0L,MAAMtN,KAAKgO,aAAcX,GACjCrN,KAAK0C,SAAU,EACf6K,EAAEC,SACKH,G,EAGTlC,YAAA,WACE,OAAOhK,IAAIuM,MACRC,KAAK,gBADD,OAEE,eACN5C,KAAK/K,KAAKoN,aAAalL,KAAKlC,Q,EAGjCsO,oBAAA,WACEf,EAAEiB,MAAMC,IAAItN,IAAIqN,MAAM,+BAAgC,CACpDE,SAAUvN,IAAI4L,QAAQC,KAAK0B,e,EA7EZZ,CAA0Ba,KCP/C,MAAM,EAA+B1P,OAAOC,KAAKC,OAAO,uB,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAY,I,aCGxCyP,EAAAA,SAAAA,G,oFACnBxD,KAAA,WAAO,WACEnL,EAAmBD,KAAKE,MAAxBD,gBACDyI,EAAqBzI,EAAgB+B,KACrC6M,EAA4B5O,EAAgB6O,sBAC5CtE,EAAwBvK,EAAgBmF,iBAExC2J,GADuB9O,EAAgB+O,gBAChB/O,EAAgBgP,cACvCC,EAAyBjP,EAAgBG,SAEzCmD,EADetD,EAAgBS,eACFD,QAE7BS,EAAYC,IAAIyK,MAAMC,UAAU,8BAAgC,UAChEsD,EAAwBjO,EAAUQ,QAAQ,UAAWmN,GACrD/N,EAAiBC,KAAKC,MAAMf,EAAgBgB,mBAC9CZ,EAAmB,EAEvB,IAAI,IAAIe,KAASN,EAAe,CAC9B,IAAIS,EAAWC,WAAWJ,GACtBC,EAAYC,SAASR,EAAeM,IAE1B,IAAXG,IACDlB,GAAmBkB,EAASF,GAIhC,IAAMT,EAAuBM,EAAUQ,QAAQ,UAAW1B,KAAK8B,eAAezB,EAAiB,IAE/F,OACE,SAAKG,UAAU,4BACb,SAAK2B,MAAM,qBACT,WAAIhB,IAAIkL,WAAWC,MAAM,iDAAzB,MACC5D,EAFH,MAGE,WAAIvH,IAAIkL,WAAWC,MAAM,oDAAzB,MACC/I,GAEH,SAAKpB,MAAM,qBACT,WAAIhB,IAAIkL,WAAWC,MAAM,oDAAzB,MACC9B,EAAuBrJ,IAAIkL,WAAWC,MAAM,2CAF/C,MAGE,WAAInL,IAAIkL,WAAWC,MAAM,mDAAzB,MACC6C,EAJH,MAKE,WAAIhO,IAAIkL,WAAWC,MAAM,wDAAzB,MACCyC,EANH,MAQE,WAAI5N,IAAIkL,WAAWC,MAAM,0DAAzB,MAE0B,IAAzB4C,GACC,SAAK/M,MAAM,0BACT,UAAMA,MAAM,sBACTvB,GAEH,UAAMiM,MAAM,8BAA8BlK,QAAS,kBAAM,EAAKyM,WAAWnP,EAAgBiP,KACtF/N,IAAIkL,WAAWC,MAAM,wDAIF,IAAzB4C,GACC,SAAK/M,MAAM,0BACT,UAAMA,MAAM,sBACThB,IAAIkL,WAAWC,MAAM,2DAExB,UAAMnK,MAAM,cAAc0K,MAAM,8BAA8BlK,QAAS,kBAAM,EAAKyM,WAAWnP,KAC1FkB,IAAIkL,WAAWC,MAAM,uD,EASpC8C,WAAA,SAAWnP,GACTkB,IAAI8L,MAAMC,KAAKrN,EAAsB,CAACI,gBAAAA,K,EAGxC6B,eAAA,SAAe8G,EAAQC,GACrB,IAAIC,EAAS1C,KAAKE,IAAI,GAAIuC,GAC1B,OAAOzC,KAAKe,MAAMyB,EAASE,GAAUA,G,EA7EpB8F,CAAgCf,KCKhCwB,EAAAA,SAAAA,G,oFACnBvP,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACbC,KAAK0C,SAAU,EACf1C,KAAKsP,aAAc,EACnBtP,KAAKC,gBAAkB,GACvBD,KAAKuP,uBAAyB,KAC9BvP,KAAKgN,KAAOhN,KAAKE,MAAMsP,OAAOxC,KAC9BhN,KAAKmL,e,EAGPC,KAAA,WAAO,IACD1I,EAEAyM,EAAsB/C,EAHrB,OAEClL,EAAYC,IAAAA,MAAAA,UAAoB,8BAAgC,UAYtE,OATGnB,KAAK0C,SACNA,EAAUuL,IAAAA,UAA2B,CAAEC,KAAM,UAC7CiB,EAAwBjO,EAAUQ,QAAQ,UAAW,KACrD0K,EAAuBlL,EAAUQ,QAAQ,UAAW,OAEpDyN,EAAwBjO,EAAUQ,QAAQ,UAAW1B,KAAKuP,uBAAuBE,aACjFrD,EAAuBlL,EAAUQ,QAAQ,UAAW1B,KAAKuP,uBAAuBG,aAIhF,aACE,SAAKvN,MAAM,oCACT,SAAKA,MAAM,+EACRhB,IAAAA,WAAAA,MAAqB,mDAExB,EAAC,IAAD,CAAQX,UAAW,yBAA0BmC,QAAS,kBAAM4K,EAAEiB,MAAMC,IAAItN,IAAAA,MAAU,eAC/EA,IAAAA,WAAAA,MAAqB,gDAG1B,SAAKgB,MAAM,iDACRhB,IAAAA,WAAAA,MAAqB,iDAAiD,CACrEwO,kBAAkBR,EAClBjD,iBAAiBE,KAGrB,QAAIjK,MAAM,kEACPnC,KAAKC,gBAAgBmC,KAAI,SAACnC,GACzB,OACE,QAAIkC,MAAM,qBAAqB,UAASlC,EAAgB+B,MACrD4M,EAAwBL,UAAU,CAAEtO,gBAAAA,UAM3CD,KAAK0C,SAAyC,IAA9B1C,KAAKC,gBAAgB2F,QACrC,aACE,SAAKzD,MAAM,uGAAuGhB,IAAAA,WAAAA,MAAqB,uDAIzIuB,GAAW1C,KAAK4P,kBAChB,SAAKzN,MAAM,kCACT,EAAC,IAAD,CAAQ3B,UAAW,yBAA0BqP,SAAU7P,KAAK0C,QAASA,QAAS1C,KAAK0C,QAASC,QAAS,kBAAM,EAAKmN,aAC7G3O,IAAAA,WAAAA,MAAqB,0DAK3BuB,GAAW,SAAKlC,UAAU,qBAAqBkC,K,EAKtDoN,SAAA,WACE9P,KAAK0C,SAAU,EACf1C,KAAKmL,YAAYnL,KAAKC,gBAAgB2F,QAAQmF,KAAK/K,KAAKoN,aAAalL,KAAKlC,Q,EAG5EoN,aAAA,SAAaC,GAAS,WAIpB,OAHArN,KAAKsP,cAAgBjC,EAAQ0C,QAAQC,SAAW3C,EAAQ0C,QAAQC,MAAMC,KACtE,GAAGrO,KAAK0L,MAAMtN,KAAKC,gBAAiBoN,GAE7BlM,IAAAA,MAAAA,KACC,kCADD,OAEE,eACN4J,MAAK,SAACmF,GAKL,OAJA,EAAKX,uBAAyBW,EAAc,GAC5C,EAAKxN,SAAU,EACf6K,EAAEC,SAEKH,M,EAKbuC,eAAA,WACE,OAAO5P,KAAKsP,a,EAGdnE,YAAA,SAAYsC,GACV,YADsB,IAAZA,IAAAA,EAAS,GACZtM,IAAAA,MAAAA,KACC,0BAA2B,CAC/BgP,OAAQ,CACNnD,KAAMhN,KAAKgN,KAAKhL,MAElBoO,KAAM,CACJ3C,OAAAA,KANC,OASE,eACN1C,KAAK/K,KAAKoN,aAAalL,KAAKlC,Q,EA3GdqP,CAA4BxB,KCL5BwC,EAAAA,SAAAA,G,oFACnBvQ,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACbC,KAAKsQ,SAAS/C,EAAEiB,MAAM+B,MAAM,c,EAG9B5P,QAAA,WACE,OACE,SAAKH,UAAU,+BACZ6O,EAAoBd,UAAU,CAC7BiB,OAAQ,CACNxC,KAAMhN,KAAKgN,U,EAXFqD,CAAoCG,KCHzD,MAAM,EAA+BvR,OAAOC,KAAKC,OAAc,M,aCE1CsR,EAAAA,SAAAA,G,kEAAAA,CAAiBC,KACtCrS,OAAOsS,OAAOF,EAAS9R,UAAW,CAChCqD,GAAI0O,IAAAA,UAAgB,MACpBjQ,MAAOiQ,IAAAA,UAAgB,SACvBpF,KAAMoF,IAAAA,UAAgB,QACtBlF,MAAOkF,IAAAA,UAAgB,SACvBpN,MAAOoN,IAAAA,UAAgB,SACvB7G,OAAQ6G,IAAAA,UAAgB,UACxB5G,UAAW4G,IAAAA,UAAgB,aAC3BE,SAAUF,IAAAA,UAAgB,YAC1B/F,KAAM+F,IAAAA,UAAgB,QACtBzB,WAAYyB,IAAAA,UAAgB,eAC5BG,UAAWH,IAAAA,UAAgB,aAC3B5F,qBAAsB4F,IAAAA,QAAc,0B,ICbjBI,EAAAA,SAAAA,G,kEAAAA,CAAyBJ,KAC9CrS,OAAOsS,OAAOG,EAAiBnS,UAAW,CACxCqD,GAAI0O,IAAAA,UAAgB,MACpBjQ,MAAOiQ,IAAAA,UAAgB,SACvBK,OAAQL,IAAAA,UAAgB,UACxBM,QAASN,IAAAA,UAAgB,WACzBtL,eAAgBsL,IAAAA,UAAgB,kBAChC1B,cAAe0B,IAAAA,UAAgB,iBAC/B5B,oBAAqB4B,IAAAA,UAAgB,uBACrCvE,mBAAoBuE,IAAAA,UAAgB,sBACpCzP,gBAAiByP,IAAAA,UAAgB,mBACjCtQ,OAAQsQ,IAAAA,UAAgB,UACxBzB,WAAYyB,IAAAA,UAAgB,eAC5BO,OAAQP,IAAAA,UAAgB,WACxBhQ,aAAcgQ,IAAAA,OAAa,gBAC3B1E,cAAe0E,IAAAA,OAAa,mB,ICfTQ,EAAAA,SAAAA,G,kEAAAA,CAAgCR,KACrDrS,OAAOsS,OAAOO,EAAwBvS,UAAW,CAC/C8Q,UAAWiB,IAAAA,UAAgB,aAC3BhB,SAAUgB,IAAAA,UAAgB,cCL5B,MAAM,EAA+BzR,OAAOC,KAAKC,OAAO,2B,ICGnCgS,EAAAA,SAAAA,G,oFACnBC,KAAA,WACE,MAAO,qB,EAGTC,KAAA,WACE,OAAOlQ,IAAAA,MAAU,+BAAgC,CAC/CuN,SAAUvN,IAAAA,QAAAA,KAAAA,c,EAIdR,QAAA,WACE,IAAM2Q,EAAetR,KAAKE,MAAMoR,aAAaC,UACvChO,EAAgB+N,EAAa7Q,QAC7B+J,EAAwB8G,EAAalM,iBAE3C,OAAOjE,IAAAA,WAAAA,MAAqB,2DAA4D,CACtFoC,cAAeA,EACfiH,sBAAsBA,K,EAI1BgH,QAAA,WACE,IAAMF,EAAetR,KAAKE,MAAMoR,aAAaC,UACvCE,EAAmBH,EAAanF,qBAChCuF,EAAoBJ,EAAaxC,sBACjCpG,EAAqB4I,EAAatP,KAClCd,EAAYC,IAAAA,MAAAA,UAAoB,8BAAgC,UAChEwQ,EAAuBzQ,EAAUQ,QAAQ,UAAW+P,GACpDG,EAAwB1Q,EAAUQ,QAAQ,UAAWgQ,GAE3D,OAAOvQ,IAAAA,WAAAA,MAAqB,wDAAyD,CACnFsO,UAAWmC,EACXlC,SAAUiC,EACVE,WAAYnJ,K,EAlCGyI,C,MAA6BW,ICHlD,MAAM,EAA+B7S,OAAOC,KAAKC,OAAO,+B,aCYxDgC,IAAAA,aAAAA,IAAqB,uBAAuB,WAC1CA,IAAAA,MAAAA,OAAAA,aAAgCsP,EAChCtP,IAAAA,MAAAA,OAAAA,iBAAoC2P,EACpC3P,IAAAA,MAAAA,OAAAA,+BAAkD+P,EAClD/P,IAAAA,uBAAAA,iBAA8CgQ,EAE9ChQ,IAAAA,OAAAA,SAAyB,CACvB4Q,KAAM,YACNxD,UAAWT,ICdbkE,EAAAA,EAAAA,QAAO7D,IAAAA,UAAqB,YAAY,SAAU8D,GAChD,IAAIC,EAA6B/Q,IAAAA,MAAAA,UAAoB,uBACjDgR,EAAmD,KAA7BD,EAAgC/Q,IAAAA,WAAAA,MAAqB,sDAAsD+Q,EAUrI,OARAD,EAAMG,IACJ,WACA,EAAC,IAAD,CAAYhB,KAAK,oBAAoBC,KAAMlQ,IAAAA,MAAU,aAClDgR,GAEH,IAGKF,KCZT9Q,IAAIkR,OAAO,gCAAkC,CAC3CN,KAAM,uCACNxD,UAAW8B,IAGb2B,EAAAA,EAAAA,QAAOxB,IAAAA,UAAoB,YAAY,SAAUyB,EAAMjF,GAC7B7L,IAAI4L,QAAQC,KAAKhL,MAClBhC,KAAKgN,KAAKhL,MAG7BiQ,EAAMG,IACJ,0BACAE,IAAAA,UAAqB,CACjBjB,KAAMlQ,IAAIqN,MAAM,+BAAgC,CAC9CE,SAAU1O,KAAKgN,KAAK0B,aAEtB0C,KAAM,qBAER,CACEjQ,IAAIkL,WAAWC,MACb,oDAIN,QFJR0F,EAAAA,EAAAA,QAAOO,IAAAA,UAA4B,qBAAqB,SAAUN,GAChEA,EAAMG,IAAI,mBAAoB,CAC5BI,KAAM,mBACNpB,KAAM,oBACNqB,MAAOtR,IAAAA,WAAAA,MACL,8D", "sources": ["webpack://@wusong8899/flarum-daily-check-in/webpack/bootstrap", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/compat get default export", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/define property getters", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/hasOwnProperty shorthand", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/make namespace object", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['forum/app']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['extend']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/IndexPage']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/LinkButton']\"", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Page']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/helpers/listItems']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/LoadingIndicator']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['Component']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Link']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Button']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Modal']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/GuaGuaLeScratchModal.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/GuaGuaLePurchaseModal.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/GuaGuaLeSoldOutModal.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/LogInModal']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['helpers/username']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/GuaGuaLeListItem.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/GuaGuaLeIndexPage.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/UserPage']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['app']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/GuaGuaLeHistoryListItem.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/GuaGuaLeHistoryList.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/GuaGuaLePurchaseHistoryPage.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['Model']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/model/GuaGuaLe.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/model/GuaGuaLePurchase.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/model/GuaGuaLePurchaseSummary.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Notification']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/GuaGuaLeNotification.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/NotificationGrid']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/index.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/addSidebarMenu.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/addUserPage.js"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['forum/app'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['extend'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/IndexPage'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/LinkButton'];", "export default function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}", "import setPrototypeOf from \"./setPrototypeOf.js\";\nexport default function _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  setPrototypeOf(subClass, superClass);\n}", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Page'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/helpers/listItems'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/LoadingIndicator'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['Component'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Link'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Button'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Modal'];", "import app from 'flarum/forum/app';\nimport Modal from 'flarum/components/Modal';\nimport Button from 'flarum/components/Button';\n\nexport default class GuaGuaLeScratchModal extends Modal {\n  static isDismissible = false;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.guagualeHistory = this.attrs.guagualeHistory;\n    this.brushRadius = 30;\n    this.opened = 0;\n    this.guagualeWinPrice = 0;\n  }\n\n  onGuaualeResultContainerReady(vnode) {\n    this.initTicket();\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    const guagualeData = this.guagualeHistory.guagualeData();\n    return guagualeData.title();\n  }\n\n  content() {\n    let guagualeWinPrice = 0;\n    let guagualeWinPriceText = \"\";\n    let guagualeResultList = [];\n    const guagualeResult = JSON.parse(this.guagualeHistory.pruchase_result());\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n\n    for(let index in guagualeResult){\n      let winAmount = parseInt(guagualeResult[index]);\n      let winPrice = parseFloat(index);\n      const winPriceText = moneyName.replace('[money]', winPrice);\n      const unitName = app.translator.trans('wusong8899-guaguale.forum.guaguale-unit-name');\n\n      guagualeResultList.push({text:winPriceText+\" x\"+guagualeResult[index]+unitName,winPrice:winPrice});\n\n      if(winPrice!==0){\n        guagualeWinPrice+=(winPrice*winAmount);\n      }\n    }\n\n    this.guagualeWinPrice = this.precisionRound(guagualeWinPrice,2);\n    guagualeWinPriceText = app.translator.trans('wusong8899-guaguale.forum.guaguale-scratch-result',{money:moneyName.replace('[money]', this.guagualeWinPrice)});\n\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form\">\n          <div className=\"Form-group\">\n            <div id=\"guagualeResultContainer\" oncreate={this.onGuaualeResultContainerReady.bind(this)} style=\"position: relative;-webkit-user-select: none;-ms-user-select: none;user-select: none;\">\n              <canvas style=\"z-index:999;position:absolute;border-radius:var(--border-radius)\" id=\"ticketCanvas\"></canvas>\n              <div id=\"guagualeResultTextContainer\" style=\"text-align:center;border:2px dotted;border-radius:var(--border-radius);visibility:hidden;\">\n                <div style=\"padding: 6px 10px;font-weight: bold;color: red;\">{guagualeWinPriceText}</div>\n                <div style=\"font-size: 10px;height: 65px;overflow-y: auto;padding: 0px 10px;display: flex;flex-wrap: wrap;justify-content: center;align-items: center;\">\n                  {guagualeResultList.map((guagualeResult) => {\n                    const guagualeResultText = guagualeResult.text;\n                    const guagualeResultWinPrice = guagualeResult.winPrice;\n                    const borderColor = this.getBackgroundColor(guagualeResultWinPrice+\"color\");\n                    const guagualeResultStyle = \"height:27px;margin-top: 4px;padding:5px;border:2px \"+borderColor+\" solid;display: inline-block;margin-left: 4px;\";\n                    \n                    return (\n                      <div style={guagualeResultStyle}>\n                        {guagualeResultText}\n                      </div>\n                    );\n                  })}\n                </div>\n              </div>\n            </div>\n          </div>\n          <div className=\"Form-group\" style=\"text-align: center;\">\n            {Button.component(\n              {\n                className: 'Button Button--primary',\n                loading: this.loading,\n                onclick: () => {\n                  // $('.Modal.modal-dialog.fade.Modal--small').fadeTo(0,0);\n                  this.hide();\n                }\n              },\n              app.translator.trans('wusong8899-guaguale.forum.guaguale-close')\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  initTicket(){\n    //\n    const canvasWidth = $(\"#guagualeResultContainer\").parent().width();\n    const canvasHeight = 100;\n\n    $(\"#guagualeResultContainer\").width(canvasWidth);\n    $(\"#guagualeResultContainer\").height(canvasHeight);\n    $(\"#guagualeResultTextContainer\").width(canvasWidth-4);\n    $(\"#guagualeResultTextContainer\").height(canvasHeight-4+5);\n    document.getElementById('ticketCanvas').width = canvasWidth;\n    document.getElementById('ticketCanvas').height = canvasHeight;\n\n    const guagualeData = this.guagualeHistory.guagualeData();\n    const guagualeImage = guagualeData.image();\n    const guagualeTitle = guagualeData.title();\n    const guagualeID = guagualeData.id();\n    const guagualeOpened = this.opened = this.guagualeHistory.opened();\n\n    this.ticketCanvas = document.getElementById('ticketCanvas');\n\n    if(guagualeOpened===0){\n      this.isDrawing = false; \n      this.lastPoint;\n      this.ctx = this.ticketCanvas.getContext('2d',{willReadFrequently:true});\n\n      let _this = this;\n\n      if(guagualeImage){\n        let image = new Image();\n        image.src = guagualeImage;\n        image.onload = function() {\n          _this.ctx.drawImage(image, 0, 0, _this.ticketCanvas.width, _this.ticketCanvas.height);\n          _this.ctx.beginPath();\n          _this.ctx.rect(0, 0, canvasWidth, canvasHeight);\n          _this.ctx.fillStyle = \"rgba(0, 0, 0, 0.25)\";\n          _this.ctx.fill();\n          _this.drawText(canvasWidth,canvasHeight);\n        };\n      }else{\n        const guagualeColor = this.getBackgroundColor(guagualeTitle+\"_\"+guagualeID);\n        this.ctx.beginPath();\n        this.ctx.rect(0, 0, canvasWidth, canvasHeight);\n        this.ctx.fillStyle = guagualeColor;\n        this.ctx.fill();\n        this.drawText(canvasWidth,canvasHeight);\n      }\n\n      this.brush = new Image();\n      this.brush.src = 'data:image/png;base64,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';\n      \n      this.ticketCanvas._this = this;\n      this.ticketCanvas.addEventListener('mousedown', this.handleMouseDown, false);\n      this.ticketCanvas.addEventListener('touchstart', this.handleMouseDown, false);\n      this.ticketCanvas.addEventListener('mousemove', this.handleMouseMove, false);\n      this.ticketCanvas.addEventListener('touchmove', this.handleMouseMove, false);\n      this.ticketCanvas.addEventListener('mouseup', this.handleMouseUp, false);\n      this.ticketCanvas.addEventListener('touchend', this.handleMouseUp, false);\n    }else{\n      this.ticketCanvas.style.display = \"none\";\n      document.getElementById('guagualeResultTextContainer').style.visibility = 'visible';\n    }\n  }\n\n  drawText(canvasWidth,canvasHeight){\n    const scratchItText = app.translator.trans('wusong8899-guaguale.forum.guaguale-scratch-it');\n    const pruchaseCountText = this.guagualeHistory.pruchase_count()+\" \"+app.translator.trans('wusong8899-guaguale.forum.guaguale-unit-name');\n\n    this.ctx.font = \"30px Arial\";\n    this.ctx.textAlign = 'center';\n    this.ctx.textBaseline = 'middle';\n    this.ctx.fillStyle = \"white\";\n    this.ctx.fillText(scratchItText, parseInt(canvasWidth/2), parseInt(canvasHeight/2)-10);\n\n    this.ctx.font = \"16px Arial\";\n    this.ctx.fillText(pruchaseCountText, parseInt(canvasWidth/2), parseInt(canvasHeight/2)+30);\n    document.getElementById('guagualeResultTextContainer').style.visibility = 'visible';\n  }\n\n  getBackgroundColor(str) {\n    let hash = 0;\n    for (let i = 0; i < str.length; i++) {\n      hash = str.charCodeAt(i) + ((hash << 5) - hash);\n    }\n    let colour = '#';\n    for (let i = 0; i < 3; i++) {\n      let value = (hash >> (i * 8)) & 0xFF;\n      colour += ('00' + value.toString(16)).substr(-2);\n    }\n    return colour;\n  }\n  \n  distanceBetween(point1, point2) {\n    return Math.sqrt(Math.pow(point2.x - point1.x, 2) + Math.pow(point2.y - point1.y, 2));\n  }\n  \n  angleBetween(point1, point2) {\n    return Math.atan2( point2.x - point1.x, point2.y - point1.y );\n  }\n  \n  getFilledInPixels(stride) {\n    if (!stride || stride < 1) { stride = 1; }\n    \n    let canvasWidth  = this.ticketCanvas.width;\n    let canvasHeight = this.ticketCanvas.height;\n    let pixels   = this.ctx.getImageData(0, 0, canvasWidth, canvasHeight),\n        pdata    = pixels.data,\n        l        = pdata.length,\n        total    = (l / stride),\n        count    = 0;\n    \n    for(let i = count = 0; i < l; i += stride) {\n      if (parseInt(pdata[i]) === 0) {\n        count++;\n      }\n    }\n    \n    return Math.round((count / total) * 100);\n  }\n  \n  getMouse(e, ticketCanvas) {\n    var offsetX = 0, offsetY = 0, mx, my;\n\n    if (ticketCanvas.offsetParent !== undefined) {\n      do {\n        offsetX += ticketCanvas.offsetLeft;\n        offsetY += ticketCanvas.offsetTop;\n      } while ((ticketCanvas = ticketCanvas.offsetParent));\n    }\n\n    mx = (e.pageX || e.touches[0].clientX) - offsetX - window.pageXOffset;\n    my = (e.pageY || e.touches[0].clientY) - offsetY - window.pageYOffset;\n\n    return {x: mx, y: my};\n  }\n  \n  handlePercentage(filledInPixels) {\n    filledInPixels = filledInPixels || 0;\n    // console.log(filledInPixels + '%');\n    if (filledInPixels>=25) {\n      $('#ticketCanvas').fadeTo(200,0);\n      $('#ticketCanvas').css(\"pointer-events\",\"none\");\n    }\n  }\n  \n  handleMouseDown(e) {\n    let _this = e.currentTarget._this;\n    _this.isDrawing = true;\n    _this.lastPoint = _this.getMouse(e, _this.ticketCanvas);\n\n    if(_this.opened===0){\n      _this.opened = 1;\n      _this.openTicket();\n    }\n  }\n\n  openTicket() {\n    const guagualePurchaseID = this.guagualeHistory.id();\n    this.guagualeHistory.save({guagualePurchaseID});\n\n    const guagualeWinPrice = this.guagualeWinPrice;\n    app.session.user.data.attributes.money+=guagualeWinPrice;\n\n    if(app.store.data.guagualePurchaseHistorySummary){\n      app.store.data.guagualePurchaseHistorySummary[\"\"].data.attributes.winTotal+=guagualeWinPrice;\n    }\n  }\n\n  precisionRound(number, precision) {\n    let factor = Math.pow(10, precision);\n    return Math.round(number * factor) / factor;\n  }\n\n  handleMouseMove(e) {\n    let _this = e.currentTarget._this;\n\n    if (!_this.isDrawing) { return; }\n    \n    e.preventDefault();\n\n    let currentPoint = _this.getMouse(e, _this.ticketCanvas),\n        dist = _this.distanceBetween(_this.lastPoint, currentPoint),\n        angle = _this.angleBetween(_this.lastPoint, currentPoint),\n        x, y;\n    \n    for (let i = 0; i < dist; i++) {\n      x = _this.lastPoint.x + (Math.sin(angle) * i) - 25;\n      y = _this.lastPoint.y + (Math.cos(angle) * i) - 25;\n\n      _this.ctx.globalCompositeOperation = 'destination-out';\n      _this.ctx.drawImage(_this.brush, x, y);\n    }\n    \n    _this.lastPoint = currentPoint;\n    _this.handlePercentage(_this.getFilledInPixels());\n  }\n\n  handleMouseUp(e) {\n    let _this = e.currentTarget._this;\n    _this.isDrawing = false;\n    // $('#ticketCanvas').fadeTo(200,0);\n  }\n}\n", "import app from 'flarum/forum/app';\nimport Modal from 'flarum/components/Modal';\nimport Button from 'flarum/components/Button';\nimport GuaGuaLeScratchModal from './GuaGuaLeScratchModal';\n\nexport default class GuaGuaLePurchaseModal extends Modal {\n  static isDismissible = false;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.guagualeData = this.attrs.guagualeData;\n    this.moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n    this.moneyCost = 0;\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    return this.guagualeData.title();\n  }\n\n  content() {\n    const moneyNameText = this.moneyName.replace('[money]', app.session.user.attribute(\"money\"));\n    const moneyCostText = this.moneyName.replace('[money]', 0);\n    const availableAmount = this.guagualeData.amount()-this.guagualeData.purchased();\n\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form\">\n          <div className=\"Form-group\">\n            <label>{app.translator.trans('wusong8899-guaguale.forum.guaguale-available-amount',{count:availableAmount})}</label>\n            <input placeholder={app.translator.trans('wusong8899-guaguale.forum.guaguale-purchase-input-placeholder')} id=\"guagualePurchaseInput\" required className=\"FormControl\" type=\"number\" step=\"1\" min=\"1\" max={availableAmount} onchange={() => this.updatePurchaseCost()} onkeyup={() => this.updatePurchaseCost()} />\n            <div style=\"padding-top:10px\">{app.translator.trans('wusong8899-guaguale.forum.guaguale-current-money-amount')}{moneyNameText}</div>\n            <div>{app.translator.trans('wusong8899-guaguale.forum.guaguale-purchase-money')}<span id=\"guagualePruchaseMoney\">{moneyCostText}</span></div>\n          </div>\n          <div className=\"Form-group\" style=\"text-align: center;\">\n            {Button.component(\n              {\n                className: 'Button Button--primary',\n                type: 'submit',\n                loading: this.loading,\n              },\n              app.translator.trans('wusong8899-guaguale.forum.guaguale-purchase-confirm')\n            )}&nbsp;\n            {Button.component(\n              {\n                className: 'Button transferMoneyButton--gray',\n                loading: this.loading,\n                onclick: () => {\n                  this.hide();\n                }\n              },\n              app.translator.trans('wusong8899-guaguale.forum.guaguale-purchase-cancel')\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  updatePurchaseCost(){\n    //\n    let guagualePurchaseCount = $(\"#guagualePurchaseInput\").val();\n    const availableAmount = this.guagualeData.amount()-this.guagualeData.purchased();\n\n    if(guagualePurchaseCount>availableAmount){\n      guagualePurchaseCount = availableAmount;\n      $(\"#guagualePurchaseInput\").val(availableAmount);\n    }\n\n    const updatedValue = this.moneyCost = guagualePurchaseCount*this.guagualeData.cost();\n    const updatedMoneyCostText = this.moneyName.replace('[money]', updatedValue);\n\n    $(\"#guagualePruchaseMoney\").text(updatedMoneyCostText);\n  }\n\n  onsubmit(e) {\n    e.preventDefault();\n\n    const guagualePurchaseCount = $(\"#guagualePurchaseInput\").val();\n    const guagualeID = this.guagualeData.id();\n\n    if(guagualePurchaseCount>0){\n      const guagualePurchaseData = {\n        guagualeID:guagualeID,\n        guagualePurchaseCount:guagualePurchaseCount,\n      };\n\n      this.loading = true;\n\n      app.store\n        .createRecord(\"guagualePurchase\")\n        .save(guagualePurchaseData)\n        .then(\n          (guagualeHistory) => {\n            app.store.pushPayload(guagualeHistory);\n            app.modal.show(GuaGuaLeScratchModal, {guagualeHistory});\n            app.session.user.data.attributes.money-=this.moneyCost;\n            this.loading = false;\n          }\n        )\n        .catch((e) => {\n          this.loading = false;\n        });\n    }\n  }\n}\n", "import app from 'flarum/forum/app';\nimport Modal from 'flarum/components/Modal';\nimport Button from 'flarum/components/Button';\n\nexport default class GuaGuaLeSoldOutModal extends Modal {\n  static isDismissible = false;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.guagualeData = this.attrs.guagualeData;\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    return this.guagualeData.title();\n  }\n\n  content() {\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form\">\n          <div className=\"Form-group\">\n            <div style=\"text-align: center;font-size: 18px;\">{app.translator.trans('wusong8899-guaguale.forum.guaguale-purchase-soldout')}</div>\n          </div>\n          <div className=\"Form-group\" style=\"text-align: center;\">\n            {Button.component(\n              {\n                className: 'Button Button--primary',\n                loading: this.loading,\n                onclick: () => {\n                  this.hide();\n                },\n              },\n              app.translator.trans('wusong8899-guaguale.forum.guaguale-purchase-ok')\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/LogInModal'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['helpers/username'];", "import Component from \"flarum/Component\";\nimport Link from \"flarum/components/Link\";\nimport Button from 'flarum/components/Button';\nimport GuaGuaLePurchaseModal from './GuaGuaLePurchaseModal';\nimport GuaGuaLeSoldOutModal from './GuaGuaLeSoldOutModal';\nimport LogInModal from \"flarum/components/LogInModal\";\nimport username from \"flarum/helpers/username\";\n\nexport default class GuaGuaLeListItem extends Component {\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.loading = true;\n    this.guagualePurchaseList = [];\n    this.loadResults();\n  }\n\n  view() {\n    const {guagualeData} = this.attrs;\n    const guagualeID = guagualeData.id();\n    const guagualeTitle = guagualeData.title();\n    const guagualeDesc = guagualeData.desc();\n    const guagualeCost = guagualeData.cost();\n    const guagualeImage = guagualeData.image();\n    const guagualeColor = guagualeData.color();\n    const guagualeAvailable = guagualeData.amount()-guagualeData.purchased();\n    const guagualePurchaseData = guagualeData.guagualePurchaseData();\n    const backgroundStyle = \"background-image:url(\"+(guagualeImage===null?'':guagualeImage)+\");background-color: \"+(guagualeColor?guagualeColor:this.getBackgroundColor(guagualeTitle+\"_\"+guagualeID))+\";\";\n    const maskStyle = guagualeImage===null?\"\":\"background: var(--shadow-color);\";\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n    const guagualePurchaseList = this.guagualePurchaseList;\n    let guagualePurchasedUserText = \"\";\n\n    for(let i=0;i<guagualePurchaseList.length;i++){\n      const purchaseUser = guagualePurchaseList[i].purchasedUser();\n      const purchaseUserName = purchaseUser.attribute(\"username\");\n      const purchaseCount = guagualePurchaseList[i].pruchase_count();\n      const purchaseWinTotal = guagualePurchaseList[i].pruchase_win_total();\n      const purchaseWinTotalText = moneyName.replace('[money]', purchaseWinTotal);\n\n      guagualePurchasedUserText+=(app.translator.trans('wusong8899-guaguale.forum.guaguale-purchased-user',{\n        purchaseUserName:purchaseUserName,\n        purchaseWinTotal:purchaseWinTotalText,\n        purchaseCount:purchaseCount\n      })).join(\"\")\n\n      if(i<this.guagualePurchaseList.length-1){\n        guagualePurchasedUserText+=\" | \";\n      }\n    }\n\n    if(guagualePurchasedUserText===\"\"){\n      guagualePurchasedUserText = app.translator.trans('wusong8899-guaguale.forum.guaguale-purchased-user-empty');\n    }\n\n    const guagualeTitleText = guagualeAvailable<=0?\"[\"+app.translator.trans('wusong8899-guaguale.forum.guaguale-list-item-soldout-text')+\"] \"+guagualeData.title():guagualeData.title();\n    const guagualeAvailableText = guagualeAvailable<=0?app.translator.trans('wusong8899-guaguale.forum.guaguale-list-item-soldout'):app.translator.trans('wusong8899-guaguale.forum.guaguale-list-item-available',{available:guagualeAvailable});\n    const guagualeCostText = moneyName.replace('[money]', guagualeCost);\n    let animationSpeed = guagualePurchaseList.length>2?(guagualePurchaseList.length+1)*5:15;\n\n    const guagualeMarqueeStyle = \"animation: marquee \"+animationSpeed+\"s linear infinite\";\n\n    return (\n      <div class=\"GuaGuaLeContainer\" onclick={() => this.showDetails(guagualeData,guagualeAvailable)} style={backgroundStyle}>\n        <div class=\"GuaGuaLeMask\" style={maskStyle}>\n          <div class=\"GuaGuaLeTitle\">\n            {guagualeTitleText}\n          </div>\n          <div style=\"height:60px\">\n            <div class=\"GuaGuaLeDescription\">\n              {guagualeDesc}\n            </div>\n          </div>\n          <div id=\"GuaGuaLeUserPurchaseList\" class=\"GuaGuaLeUserPurchaseList\">\n            {!this.loading && (\n              <p class=\"marquee\">\n               <span style={guagualeMarqueeStyle}>\n                   {guagualePurchasedUserText}\n               </span>\n              </p>\n            )}\n          </div>\n          <div class=\"GuaGuaLeDetails\">\n            {app.translator.trans('wusong8899-guaguale.forum.guaguale-list-item-cost',{cost:guagualeCostText})}&nbsp;|&nbsp;\n            {guagualeAvailableText}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  showDetails(guagualeData,guagualeAvailable) {\n    //\n    if (app.session.user) {\n      app.modal.show(guagualeAvailable<=0?GuaGuaLeSoldOutModal:GuaGuaLePurchaseModal, {guagualeData});\n    } else {\n      app.modal.show(LogInModal);\n    }\n  }\n\n  getBackgroundColor(str) {\n    let hash = 0;\n    for (let i = 0; i < str.length; i++) {\n      hash = str.charCodeAt(i) + ((hash << 5) - hash);\n    }\n    let colour = '#';\n    for (let i = 0; i < 3; i++) {\n      let value = (hash >> (i * 8)) & 0xFF;\n      colour += ('00' + value.toString(16)).substr(-2);\n    }\n    return colour;\n  }\n\n  parseResults(results) {\n    [].push.apply(this.guagualePurchaseList, results);\n    this.loading = false;\n    m.redraw();\n\n    return results;\n  }\n\n  loadResults(offset = 0) {\n    return app.store\n      .find(\"guagualePurchaseList\", {\n        guaID: this.attrs.guagualeData.id()\n      })\n      .catch(() => {})\n      .then(this.parseResults.bind(this));\n  }\n}\n", "import Page from 'flarum/components/Page';\nimport IndexPage from 'flarum/components/IndexPage';\nimport listItems from 'flarum/common/helpers/listItems';\nimport LoadingIndicator from \"flarum/components/LoadingIndicator\";\nimport GuaGuaLeListItem from \"./GuaGuaLeListItem\";\nimport Button from 'flarum/components/Button';\n\nexport default class GuaGuaLeIndexPage extends Page {\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.bodyClass = 'App--index';\n    this.loading = true;\n    this.guagualeList = [];\n    this.loadResults();\n    //display: grid;grid-template-columns: repeat(2, 1fr);grid-template-rows: 150px;gap: 30px 10px;\n  }\n\n  view() {\n    let loading;\n\n    if (this.loading) {\n      loading = LoadingIndicator.component({ size: \"large\" });\n    }\n\n    return (\n      <div className=\"IndexPage\">\n        {IndexPage.prototype.hero()}\n\n        <div className=\"container\">\n          <div className=\"sideNavContainer\">\n            <nav className=\"IndexPage-nav sideNav\">\n              <ul>{listItems(IndexPage.prototype.sidebarItems().toArray())}</ul>\n            </nav>\n\n            <div class=\"GuaGuaLeGlobalContainer\">\n              <div style=\"padding-bottom:10px;\">\n                {Button.component({\n                    className: 'Button',\n                    onclick: () => {\n                      this.showPurchaseHistory();\n                    }\n                  },\n                  app.translator.trans('wusong8899-guaguale.forum.guaguale-view-history')\n                )}\n              </div>\n              {this.guagualeList.map((guagualeData) => {\n                return (\n                  <div style=\"padding-top:5px\">\n                    {GuaGuaLeListItem.component({ guagualeData })}\n                  </div>\n                );\n              })}\n\n              {!this.loading && this.guagualeList.length===0 && (\n                <div>\n                  <div style=\"font-size:1.4em;color: var(--muted-more-color);text-align: center;height: 300px;line-height: 100px;\">{app.translator.trans(\"wusong8899-guaguale.forum.guaguale-list-empty\")}</div>\n                </div>\n              )}\n\n              {loading && <div className=\"GuaGuaLe-loadMore\">{loading}</div>}\n            </div>\n\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  parseResults(results) {\n    [].push.apply(this.guagualeList, results);\n    this.loading = false;\n    m.redraw();\n    return results;\n  }\n\n  loadResults() {\n    return app.store\n      .find(\"guagualeList\")\n      .catch(() => {})\n      .then(this.parseResults.bind(this));\n  }\n\n  showPurchaseHistory() {\n    m.route.set(app.route(\"user.guagualePurchaseHistory\", {\n      username: app.session.user.username(),\n    }));\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/UserPage'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['app'];", "import Component from \"flarum/Component\";\nimport GuaGuaLeScratchModal from './GuaGuaLeScratchModal';\n\nexport default class GuaGuaLeHistoryListItem extends Component {\n  view() {\n    const {guagualeHistory} = this.attrs;\n    const guagualePurchaseID = guagualeHistory.id();\n    const guagualePurchaseTotalCost = guagualeHistory.pruchase_cost_total();\n    const guagualePurchaseCount = guagualeHistory.pruchase_count();\n    const guagualePurchaseCost = guagualeHistory.pruchase_cost();\n    const guagualePurchaseDate = guagualeHistory.assignedAt();\n    const guagualePurchaseOpened = guagualeHistory.opened();\n    const guagualeData = guagualeHistory.guagualeData();\n    const guagualeTitle = guagualeData.title();\n\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n    const purchaseCostTotalText = moneyName.replace('[money]', guagualePurchaseTotalCost);\n    const guagualeResult = JSON.parse(guagualeHistory.pruchase_result());\n    let guagualeWinPrice = 0;\n\n    for(let index in guagualeResult){\n      let winPrice = parseFloat(index);\n      let winAmount = parseInt(guagualeResult[index]);\n\n      if(winPrice!==0){\n        guagualeWinPrice+=(winPrice*winAmount);\n      }\n    }\n\n    const guagualeWinPriceText = moneyName.replace('[money]', this.precisionRound(guagualeWinPrice,2));\n\n    return (\n      <div className=\"guagualeHistoryContainer\">\n        <div style=\"padding-top: 5px;\">\n          <b>{app.translator.trans('wusong8899-guaguale.forum.guaguale-history-list-id')}: </b>\n          {guagualePurchaseID}&nbsp;|&nbsp;\n          <b>{app.translator.trans('wusong8899-guaguale.forum.guaguale-history-list-title')}: </b>\n          {guagualeTitle}\n        </div>\n        <div style=\"padding-top: 5px;\">\n          <b>{app.translator.trans('wusong8899-guaguale.forum.guaguale-history-list-count')}: </b>\n          {guagualePurchaseCount}{app.translator.trans(\"wusong8899-guaguale.forum.guaguale-unit-name\")}&nbsp;|&nbsp;\n          <b>{app.translator.trans('wusong8899-guaguale.forum.guaguale-history-list-cost')}: </b>\n          {purchaseCostTotalText}&nbsp;|&nbsp;\n          <b>{app.translator.trans('wusong8899-guaguale.forum.guaguale-history-list-assign-at')}: </b>\n          {guagualePurchaseDate}&nbsp;|&nbsp;\n\n          <b>{app.translator.trans('wusong8899-guaguale.forum.guaguale-history-list-open-result')}: </b>\n          \n          {guagualePurchaseOpened===1 && (\n            <div style=\"display: inline-block;\">\n              <span style=\"padding-right:8px;\">\n                {guagualeWinPriceText}\n              </span>\n              <span class=\"GuaGuaLeButton--transparent\" onclick={() => this.showResult(guagualeHistory,guagualePurchaseOpened)}>\n                {app.translator.trans(\"wusong8899-guaguale.forum.guaguale-purchase-view-result\")}\n              </span>\n            </div>\n          )}\n          {guagualePurchaseOpened===0 && (\n            <div style=\"display: inline-block;\">\n              <span style=\"padding-right:8px;\">\n                {app.translator.trans(\"wusong8899-guaguale.forum.guaguale-purchase-not-scratch-yet\")}\n              </span>\n              <span style=\"color:green\" class=\"GuaGuaLeButton--transparent\" onclick={() => this.showResult(guagualeHistory)}>\n                {app.translator.trans(\"wusong8899-guaguale.forum.guaguale-purchase-scratch\")}\n              </span>\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  showResult(guagualeHistory) {\n    app.modal.show(GuaGuaLeScratchModal, {guagualeHistory});\n  }\n\n  precisionRound(number, precision) {\n    let factor = Math.pow(10, precision);\n    return Math.round(number * factor) / factor;\n  }\n}\n", "import Component from \"flarum/Component\";\nimport app from \"flarum/app\";\nimport LoadingIndicator from \"flarum/components/LoadingIndicator\";\nimport Button from \"flarum/components/Button\";\nimport Link from \"flarum/components/Link\";\n\nimport GuaGuaLeHistoryListItem from \"./GuaGuaLeHistoryListItem\";\n\nexport default class GuaGuaLeHistoryList extends Component {\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.loading = true;\n    this.moreResults = false;\n    this.guagualeHistory = [];\n    this.guagualeHistorySummary = null;\n    this.user = this.attrs.params.user;\n    this.loadResults();\n  }\n\n  view() {\n    let loading;\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n    let purchaseCostTotalText,purchaseWinTotalText;\n\n    if(this.loading){\n      loading = LoadingIndicator.component({ size: \"large\" });\n      purchaseCostTotalText = moneyName.replace('[money]', \"-\");\n      purchaseWinTotalText = moneyName.replace('[money]', \"-\");\n    }else{\n      purchaseCostTotalText = moneyName.replace('[money]', this.guagualeHistorySummary.costTotal());\n      purchaseWinTotalText = moneyName.replace('[money]', this.guagualeHistorySummary.winTotal());\n    }\n\n    return (\n      <div>\n        <div style=\"display:flex;padding-bottom:10px\">\n          <div style=\"font-size: 24px;font-weight: bold;display:inline-block;padding-right: 10px;\">\n            {app.translator.trans(\"wusong8899-guaguale.forum.guaguale-purchase-history\")}\n          </div>\n          <Button className={'Button Button--primary'} onclick={() => m.route.set(app.route(\"guaguale\"))}>\n            {app.translator.trans(\"wusong8899-guaguale.forum.guaguale-purchase-now\")}\n          </Button>\n        </div>\n        <div style=\"color:var(--muted-color);padding-bottom:10px;\">\n          {app.translator.trans(\"wusong8899-guaguale.forum.guaguale-purchase-summary\",{\n            purchaseCostTotal:purchaseCostTotalText,\n            purchaseWinTotal:purchaseWinTotalText\n          })}\n        </div>\n        <ul style=\"margin: 0;padding: 0;list-style-type: none;position: relative;\">\n          {this.guagualeHistory.map((guagualeHistory) => {\n            return (\n              <li style=\"padding-bottom:5px\" data-id={guagualeHistory.id()}>\n                {GuaGuaLeHistoryListItem.component({ guagualeHistory })}\n              </li>\n            );\n          })}\n        </ul>\n          \n        {!this.loading && this.guagualeHistory.length===0 && (\n          <div>\n            <div style=\"font-size:1.4em;color: var(--muted-more-color);text-align: center;height: 300px;line-height: 100px;\">{app.translator.trans(\"wusong8899-guaguale.forum.guaguale-history-list-empty\")}</div>\n          </div>\n        )}\n\n        {!loading && this.hasMoreResults() && (\n          <div style=\"text-align:center;padding:20px\">\n            <Button className={'Button Button--primary'} disabled={this.loading} loading={this.loading} onclick={() => this.loadMore()}>\n              {app.translator.trans('wusong8899-guaguale.forum.guaguale-history-list-load-more')}\n            </Button>\n          </div>\n        )}\n\n        {loading && <div className=\"GuaGuaLe-loadMore\">{loading}</div>}\n      </div>\n    );\n  }\n\n  loadMore() {\n    this.loading = true;\n    this.loadResults(this.guagualeHistory.length).then(this.parseResults.bind(this));\n  }\n\n  parseResults(results) {\n    this.moreResults = !!results.payload.links && !!results.payload.links.next;\n    [].push.apply(this.guagualeHistory, results);\n\n    return app.store\n      .find(\"guagualePurchaseHistorySummary\")\n      .catch(() => {})\n      .then((summaryResult) => {\n        this.guagualeHistorySummary = summaryResult[0];\n        this.loading = false;\n        m.redraw();\n\n        return results;\n      });\n\n  }\n\n  hasMoreResults() {\n    return this.moreResults;\n  }\n\n  loadResults(offset = 0) {\n    return app.store\n      .find(\"guagualePurchaseHistory\", {\n        filter: {\n          user: this.user.id(),\n        },\n        page: {\n          offset,\n        },\n      })\n      .catch(() => {})\n      .then(this.parseResults.bind(this));\n  }\n}\n", "import UserPage from \"flarum/components/UserPage\";\nimport GuaGuaLeHistoryList from \"./GuaGuaLeHistoryList\";\n\nexport default class GuaGuaLePurchaseHistoryPage extends UserPage {\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.loadUser(m.route.param(\"username\"));\n  }\n\n  content() {\n    return (\n      <div className=\"GuaGuaLePurchaseHistoryPage\">\n        {GuaGuaLeHistoryList.component({\n          params: {\n            user: this.user,\n          },\n        })}\n      </div>\n    );\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['Model'];", "import Model from \"flarum/Model\";\n\nexport default class GuaGuaLe extends Model {}\nObject.assign(GuaGuaLe.prototype, {\n  id: Model.attribute(\"id\"),\n  title: Model.attribute(\"title\"),\n  desc: Model.attribute(\"desc\"),\n  color: Model.attribute(\"color\"),\n  image: Model.attribute(\"image\"),\n  amount: Model.attribute(\"amount\"),\n  purchased: Model.attribute(\"purchased\"),\n  settings: Model.attribute(\"settings\"),\n  cost: Model.attribute(\"cost\"),\n  assignedAt: Model.attribute(\"assigned_at\"),\n  activated: Model.attribute(\"activated\"),\n  guagualePurchaseData: Model.hasMany(\"guagualePurchaseData\"),\n});\n", "import Model from \"flarum/Model\";\n\nexport default class GuaGuaLePurchase extends Model {}\nObject.assign(GuaGuaLePurchase.prototype, {\n  id: Model.attribute(\"id\"),\n  title: Model.attribute(\"title\"),\n  gua_id: Model.attribute(\"gua_id\"),\n  user_id: Model.attribute(\"user_id\"),\n  pruchase_count: Model.attribute(\"pruchase_count\"),\n  pruchase_cost: Model.attribute(\"pruchase_cost\"),\n  pruchase_cost_total: Model.attribute(\"pruchase_cost_total\"),\n  pruchase_win_total: Model.attribute(\"pruchase_win_total\"),\n  pruchase_result: Model.attribute(\"pruchase_result\"),\n  opened: Model.attribute(\"opened\"),\n  assignedAt: Model.attribute(\"assigned_at\"),\n  openAt: Model.attribute(\"open_at\"),\n  guagualeData: Model.hasOne(\"guagualeData\"),\n  purchasedUser: Model.hasOne(\"purchasedUser\"),\n});\n", "import Model from \"flarum/Model\";\n\nexport default class GuaGuaLePurchaseSummary extends Model {}\nObject.assign(GuaGuaLePurchaseSummary.prototype, {\n  costTotal: Model.attribute(\"costTotal\"),\n  winTotal: Model.attribute(\"winTotal\")\n});\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Notification'];", "import app from 'flarum/forum/app';\nimport Notification from \"flarum/components/Notification\";\n\nexport default class GuaGuaLeNotification extends Notification {\n  icon() {\n    return \"fas fa-ticket-alt\";\n  }\n\n  href() {\n    return app.route(\"user.guagualePurchaseHistory\", {\n      username: app.session.user.username(),\n    });\n  }\n\n  content() {\n    const notification = this.attrs.notification.subject();\n    const guagualeTitle = notification.title();\n    const guagualePurchaseCount = notification.pruchase_count();\n\n    return app.translator.trans('wusong8899-guaguale.forum.notifications.guaguale-open-content', {\n      guagualeTitle: guagualeTitle,\n      guagualePurchaseCount:guagualePurchaseCount\n    });\n  }\n\n  excerpt() {\n    const notification = this.attrs.notification.subject();\n    const guagualeWinTotal = notification.pruchase_win_total();\n    const guagualeCostTotal = notification.pruchase_cost_total();\n    const guagualePurchaseID = notification.id();\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n    const guagualeWinTotalText = moneyName.replace('[money]', guagualeWinTotal);\n    const guagualeCostTotalText = moneyName.replace('[money]', guagualeCostTotal);\n\n    return app.translator.trans('wusong8899-guaguale.forum.notifications.guaguale-win-total', {\n      costTotal: guagualeCostTotalText,\n      winTotal: guagualeWinTotalText,\n      purchaseID: guagualePurchaseID\n    });\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/NotificationGrid'];", "import app from 'flarum/forum/app';\r\nimport { extend } from 'flarum/extend';\r\nimport addSidebarMenu from './addSidebarMenu';\r\nimport GuaGuaLeIndexPage from './components/GuaGuaLeIndexPage';\r\nimport addUserPage from \"./addUserPage\";\r\n\r\nimport GuaGuaLe from \"./model/GuaGuaLe\";\r\nimport GuaGuaLePurchase from \"./model/GuaGuaLePurchase\";\r\nimport GuaGuaLePurchaseSummary from \"./model/GuaGuaLePurchaseSummary\";\r\nimport GuaGuaLeNotification from \"./components/GuaGuaLeNotification\";\r\nimport NotificationGrid from \"flarum/components/NotificationGrid\";\r\n\r\napp.initializers.add('wusong8899-raffle-ticket', () => {\r\n  app.store.models.guagualeList = GuaGuaLe;\r\n  app.store.models.guagualePurchase = GuaGuaLePurchase;\r\n  app.store.models.guagualePurchaseHistorySummary = GuaGuaLePurchaseSummary;\r\n  app.notificationComponents.guagualePurchase = GuaGuaLeNotification;\r\n\r\n  app.routes['guaguale'] = {\r\n    path: '/guaguale',\r\n    component: GuaGuaLeIndexPage,\r\n  };\r\n\r\n  addSidebarMenu();\r\n  addUserPage();\r\n\r\n  extend(NotificationGrid.prototype, \"notificationTypes\", function (items) {\r\n    items.add(\"guagualePurchase\", {\r\n      name: \"guagualePurchase\",\r\n      icon: \"fas fa-ticket-alt\",\r\n      label: app.translator.trans(\r\n        \"wusong8899-guaguale.forum.guaguale-receive-open-result\"\r\n      ),\r\n    });\r\n  });\r\n});", "import { extend } from 'flarum/extend';\nimport app from 'flarum/forum/app';\nimport IndexPage from 'flarum/components/IndexPage';\nimport LinkButton from 'flarum/components/LinkButton';\n\nexport default function addSidebarMenu() {\n  extend(IndexPage.prototype, 'navItems', function (items) {\n    let guagualeDisplayNameSetting = app.forum.attribute(\"guagualeDisplayName\");\n    let guagualeDisplayName = guagualeDisplayNameSetting===\"\"?app.translator.trans('wusong8899-guaguale.forum.guaguale-display-name-default'):guagualeDisplayNameSetting;\n\n    items.add(\n      'GuaGuaLe',\n      <LinkButton icon=\"fas fa-ticket-alt\" href={app.route('guaguale')}>\n        {guagualeDisplayName}\n      </LinkButton>,\n      15\n    );\n\n    return items;\n  });\n}\n", "import { extend } from \"flarum/extend\";\nimport UserPage from \"flarum/components/UserPage\";\nimport LinkButton from \"flarum/components/LinkButton\";\nimport GuaGuaLePurchaseHistoryPage from './components/GuaGuaLePurchaseHistoryPage';\n\nexport default function () {\n  app.routes[\"user.guagualePurchaseHistory\"] = {\n    path: \"/u/:username/guagualePurchaseHistory\",\n    component: GuaGuaLePurchaseHistoryPage,\n  };\n\n  extend(UserPage.prototype, \"navItems\", function (items,user) {\n      const currentUserID = app.session.user.id();\n      const targetUserID = this.user.id();\n\n      if(currentUserID==targetUserID){\n        items.add(\n          \"guagualePurchaseHistory\",\n          LinkButton.component({\n              href: app.route(\"user.guagualePurchaseHistory\", {\n                username: this.user.username(),\n              }),\n              icon: \"fas fa-ticket-alt\",\n            },\n            [\n              app.translator.trans(\n                \"wusong8899-guaguale.forum.guaguale-purchase-history\"\n              )\n            ]\n          ),\n          10\n        );\n      }\n  });\n}\n"], "names": ["__webpack_require__", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "flarum", "core", "compat", "_setPrototypeOf", "p", "setPrototypeOf", "__proto__", "_inherits<PERSON><PERSON>e", "subClass", "superClass", "create", "constructor", "GuaGuaLeScratchModal", "oninit", "vnode", "this", "guagualeHistory", "attrs", "brushRadius", "opened", "guagualeWinPrice", "onGuaualeResultContainerReady", "initTicket", "className", "title", "guagualeData", "content", "guagualeWinPriceText", "guagualeResultList", "guagualeResult", "JSON", "parse", "pruchase_result", "moneyName", "app", "index", "winAmount", "parseInt", "winPrice", "parseFloat", "winPriceText", "replace", "unitName", "push", "text", "precisionRound", "money", "id", "oncreate", "bind", "style", "map", "guagualeResultText", "guagualeResultWinPrice", "borderColor", "getBackgroundColor", "<PERSON><PERSON>", "loading", "onclick", "hide", "canvasWidth", "$", "parent", "width", "canvasHeight", "height", "document", "getElementById", "guagualeImage", "image", "guagualeTitle", "guagualeID", "guagualeOpened", "ticketCanvas", "isDrawing", "lastPoint", "ctx", "getContext", "willReadFrequently", "_this", "Image", "src", "onload", "drawImage", "beginPath", "rect", "fillStyle", "fill", "drawText", "guagualeColor", "brush", "addEventListener", "handleMouseDown", "handleMouseMove", "handleMouseUp", "display", "visibility", "scratchItText", "pruchaseCountText", "pruchase_count", "font", "textAlign", "textBaseline", "fillText", "str", "hash", "i", "length", "charCodeAt", "colour", "toString", "substr", "distanceBetween", "point1", "point2", "Math", "sqrt", "pow", "x", "y", "angleBetween", "atan2", "getFilledInPixels", "stride", "pdata", "getImageData", "data", "l", "total", "count", "round", "getMouse", "e", "offsetX", "offsetY", "undefined", "offsetParent", "offsetLeft", "offsetTop", "pageX", "touches", "clientX", "window", "pageXOffset", "pageY", "clientY", "pageYOffset", "handlePercentage", "filledInPixels", "fadeTo", "css", "currentTarget", "openTicket", "guagualePurchaseID", "save", "number", "precision", "factor", "preventDefault", "currentPoint", "dist", "angle", "sin", "cos", "globalCompositeOperation", "Modal", "isDismissible", "GuaGuaLePurchaseModal", "moneyCost", "moneyNameText", "moneyCostText", "availableAmount", "amount", "purchased", "placeholder", "required", "type", "step", "min", "max", "onchange", "updatePurchaseCost", "onkeyup", "guagualePurchaseCount", "val", "updatedValue", "cost", "updatedMoneyCostText", "onsubmit", "guagualePurchaseData", "then", "GuaGuaLeSoldOutModal", "GuaGuaLeListItem", "guagualePurchaseList", "loadResults", "view", "guagualeDesc", "desc", "guagualeCost", "color", "guagualeAvailable", "backgroundStyle", "maskStyle", "forum", "attribute", "guagualePurchasedUserText", "purchaseUserName", "purchasedUser", "purchaseCount", "purchaseWinTotal", "pruchase_win_total", "purchaseWinTotalText", "translator", "trans", "join", "guagualeTitleText", "guagualeAvailableText", "available", "guagualeCostText", "guagualeMarqueeStyle", "class", "showDetails", "session", "user", "modal", "show", "LogInModal", "parseResults", "results", "apply", "m", "redraw", "offset", "store", "find", "guaID", "Component", "GuaGuaLeIndexPage", "bodyClass", "guagualeList", "LoadingIndicator", "size", "IndexPage", "listItems", "toArray", "showPurchaseHistory", "component", "route", "set", "username", "Page", "GuaGuaLeHistoryListItem", "guagualePurchaseTotalCost", "pruchase_cost_total", "guagualePurchaseDate", "pruchase_cost", "assignedAt", "guagualePurchaseOpened", "purchaseCostTotalText", "showResult", "GuaGuaLeHistoryList", "moreResults", "guagualeHistorySummary", "params", "costTotal", "winTotal", "purchaseCostTotal", "hasMoreResults", "disabled", "loadMore", "payload", "links", "next", "summaryResult", "filter", "page", "GuaGuaLePurchaseHistoryPage", "loadUser", "param", "UserPage", "GuaGuaLe", "Model", "assign", "settings", "activated", "GuaGuaLePurchase", "gua_id", "user_id", "openAt", "GuaGuaLePurchaseSummary", "GuaGuaLeNotification", "icon", "href", "notification", "subject", "excerpt", "guagualeWinTotal", "guagualeCostTotal", "guagualeWinTotalText", "guagualeCostTotalText", "purchaseID", "Notification", "path", "extend", "items", "guagualeDisplayNameSetting", "guagualeDisplayName", "add", "routes", "LinkButton", "NotificationGrid", "name", "label"], "sourceRoot": ""}