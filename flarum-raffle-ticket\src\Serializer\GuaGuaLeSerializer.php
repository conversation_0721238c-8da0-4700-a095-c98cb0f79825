<?php

namespace wusong8899\GuaGuaLe\Serializer;

use <PERSON>larum\Api\Serializer\AbstractSerializer;
use wusong8899\GuaGuaLe\Serializer\GuaGuaLePurchaseCountSerializer;

class GuaGuaLeSerializer extends AbstractSerializer
{
    protected $type = 'guagualeList';

    protected function getDefaultAttributes($data)
    {
        $attributes = [
            'id' => $data->id,
            'title' => $data->title,
            'desc' => $data->desc,
            'color' => $data->color,
            'image' => $data->image,
            'amount' => $data->amount,
            'purchased' => $data->purchased,
            'settings' => $data->settings,
            'cost' => $data->cost,
            'limit' => $data->limit,
            'activated' => $data->activated,
            'assigned_at' => date("Y-m-d H:i:s", strtotime($data->assigned_at))
        ];

        return $attributes;
    }
}
