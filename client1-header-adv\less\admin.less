// Header Icon Setting Component Styles
.HeaderIconSetting {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  background: #f9f9f9;

  &-input {
    margin-bottom: 15px;

    .FormControl {
      width: 100%;
    }
  }

  &-preview {
    margin-bottom: 15px;

    &Label {
      font-weight: 600;
      margin-bottom: 8px;
      color: #555;
    }

    &Container {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 60px;
      background: white;
      border: 1px solid #eee;
      border-radius: 4px;
      padding: 10px;
    }

    &Image {
      max-height: 40px;
      max-width: 200px;
      object-fit: contain;
    }

    &Error {
      color: #d83e3e;
      font-style: italic;
    }
  }

  &-upload {
    border-top: 1px solid #ddd;
    padding-top: 15px;

    &Label {
      font-weight: 600;
      margin-bottom: 8px;
      color: #555;
    }

    &Help {
      margin-top: 8px;
      font-size: 12px;
      color: #777;
    }
  }
}

// Dynamic Slide Settings Component Styles
.DynamicSlideSettings {
  &-slide {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
    background: #f9f9f9;
  }

  &-slideHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;

    h4 {
      margin: 0;
      color: #555;
      font-size: 16px;
      font-weight: 600;
    }
  }

  &-slideFields {
    .Form-group {
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .FormControl {
      width: 100%;
    }
  }

  &-addButton {
    text-align: center;
    padding: 20px 0;
  }
}