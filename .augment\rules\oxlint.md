---
type: "agent_requested"
description: "Oxlint Linter 使用与配置规则"
---

# Oxlint Linter 使用与配置规则

## 1. 目的与适用范围

本规则旨在指导 AI 编码代理在**任何 JavaScript/TypeScript 前端项目**中配置和使用 `Oxlint` 作为主要的静态代码检查工具。其核心目标是利用 `Oxlint` 的高性能优势，快速发现代码中的错误和无用部分。

- **适用范围**:
  - **所有前端代码库**: 包括 `js/`、`src/`、`app/` 等包含 `.(c|m)js`、`.(c|m)ts`、`.jsx`、`.tsx` 的目录。
  - **框架文件**: 也适用于 `.vue`、`.astro` 和 `.svelte` 文件中的 `<script>` 标签内容。
- **与 ESLint 的关系**: 在大型项目中，推荐 `Oxlint` 与 `ESLint` 协同工作。`Oxlint` 优先运行以提供快速反馈，`ESLint` 则负责处理 `Oxlint` 不支持的类型感知规则和风格规则。

## 2. 结构与格式

- **配置文件**: 项目根目录必须存在 `.oxlintrc.json` 文件。
- **规则原子化**: 本文档中的每条规则都应清晰、可验证，直接对应 `Oxlint` 的配置项或命令。

## 3. 工具使用与工作循环

- **代理角色**: 你是一名负责代码质量的前端工程师，主要任务是集成和维护 `Oxlint`。
- **环境与工具**:
  - **运行方式**:
    - **一次性检查**: `pnpm dlx oxlint@latest`
    - **项目依赖**: `pnpm add -D oxlint`
  - **可用命令**: `oxlint`, `oxlint-migrate`。
- **工作循环**: **检查 → 修复 → 验证**
  - **计划 (Plan)**: 识别任务目标，是初始化配置、添加规则，还是修复已有问题。
  - **执行 (Execute)**:
    - **检查**: `pnpm oxlint` 或 `pnpm oxlint <path>`。
    - **自动修复**: `pnpm oxlint --fix`。
  - **验证 (Verify)**: 再次运行 `pnpm oxlint`，确认没有错误或警告。对于 CI 环境，检查退出码是否为 0。

## 4. `Oxlint` 配置 (`.oxlintrc.json`)

### 4.1 基本结构 (JSONC)

- **文件名**: **必须**为 `.oxlintrc.json`。
- **格式**: 使用支持注释的 JSON (JSONC)。
- **核心属性**:
  - `rules`: 配置具体规则的严重性级别和选项。
  - `plugins`: 启用特定框架或库的规则集。
  - `categories`: 批量启用规则类别。
  - `ignorePatterns`: 定义需要忽略的文件或目录。
  - `overrides`: 对特定文件类型应用不同的配置。

### 4.2 规则配置 (`rules`)

- **严重性级别**:
  - `off` 或 `0`: 关闭规则。
  - `warn` 或 `1`: 警告，不影响退出码（除非使用 `--deny-warnings`）。
  - `error` 或 `2`: 错误，会导致非零退出码。
- **格式**:
  - **仅级别**: `"no-console": "error"`
  - **带选项**: `"no-plusplus": ["error", { "allowForLoopAfterthoughts": true }]`
- **默认启用**: `correctness` 类别下的所有规则默认级别为 `error`。

### 4.3 插件与类别 (`plugins`, `categories`)

- **默认插件**: `react`, `unicorn`, `typescript`, `oxc` 默认启用。若要修改，需在 `plugins` 数组中明确指定。
- **启用插件**:
  `"plugins": ["react", "jest", "jsx-a11y"]`
- **启用类别**:
  `"categories": { "correctness": "error", "suspicious": "warn" }`

### 4.4 忽略文件 (`ignorePatterns`)

- **必须**在 `.oxlintrc.json` 中定义忽略模式。
- **示例**: `"ignorePatterns": ["dist/**", "**/__snapshots__/", "*.test.js"]`
- **兼容性**: `Oxlint` 也会自动读取 `.eslintignore` 文件。

### 4.5 文件覆盖 (`overrides`)

- **用于特定文件**: 针对测试文件、脚本或特定框架文件应用不同规则。
- **示例**:
  ```json
  "overrides": [
    {
      "files": ["**/*.test.ts", "**/__tests__/**"],
      "plugins": ["jest"],
      "rules": {
        "jest/no-focused-tests": "error"
      }
    }
  ]
  ```

## 5. 命令行接口 (CLI)

- **基本运行**: `oxlint [PATH...]`
- **指定配置**: `oxlint --config ./path/to/config.json`
- **动态规则调整 (覆盖配置文件)**:
  - `-D, --deny=RULE_OR_CATEGORY` (设置为 error)
  - `-W, --warn=RULE_OR_CATEGORY`
  - `-A, --allow=RULE_OR_CATEGORY`
- **自动修复**:
  - **安全修复**: `oxlint --fix`
  - **含建议修复**: `oxlint --fix-suggestions` (可能改变行为)
- **处理警告**:
  - `oxlint --quiet`: 只报告错误。
  - `oxlint --deny-warnings`: 将警告视为错误，影响退出码。
- **插件启用/禁用**:
  - `--import-plugin`
  - `--jest-plugin`
  - `--disable-unicorn-plugin`

## 6. 与其他工具集成

### 6.1 VS Code

- **必须**安装官方 `Oxlint` 扩展，以获得实时编辑器内反馈和快速修复。

### 6.2 `lint-staged`

- 为实现预提交钩子，**必须**在 `package.json` 中配置 `lint-staged`：
  ```json
  "lint-staged": {
    "**/*.{js,mjs,cjs,jsx,ts,mts,cts,tsx,vue,astro,svelte}": "oxlint --fix"
  }
  ```

### 6.3 持续集成 (CI)

- **推荐**: 在 CI 流程中，`Oxlint` 应在 `ESLint` 之前运行。
- **GitHub Actions**:
  ```yaml
  jobs:
    oxlint:
      name: Lint Code with Oxlint
      runs-on: ubuntu-latest
      steps:
        - uses: actions/checkout@v4
        - run: pnpm install --frozen-lockfile
        - run: pnpm oxlint --deny-warnings
  ```
- **版本固定**: CI 命令中的 `oxlint` 版本应被固定，以避免新版本引入破坏性变更。

## 7. 非目标与不支持项

- **类型感知规则**: `Oxlint` 不支持 `typescript-eslint` 的类型感知规则 (如 `no-floating-promises`)。这些仍需由 `ESLint` 处理。
- **风格规则**: 大部分纯风格规则（如缩进、引号）不受支持，应由 `Prettier` 或 `dprint` 等格式化工具处理。

## 8. 最小示例

### 示例 1: 初始化项目并启用 Jest 规则

**任务**: 为新项目添加 `Oxlint`，并为 `test/` 目录下的文件启用 `Jest` 插件规则。

**验收标准**:

1. `package.json` 中包含 `oxlint` 开发依赖。
2. `.oxlintrc.json` 文件被创建。
3. 测试文件中的 `test.only` 会被 `Oxlint` 标记为错误。

**代理工作流**:

1.  **计划**:
    - 安装 `oxlint`。
    - 创建 `.oxlintrc.json` 文件。
    - 在配置文件中添加 `overrides` 部分以启用 `jest` 插件。
2.  **执行**:
    - **安装**: `pnpm add -D oxlint`
    - **创建 `.oxlintrc.json`**:
      ```json
      {
        "rules": {
          "no-console": "warn"
        },
        "overrides": [
          {
            "files": ["**/test/**", "**/*.spec.ts"],
            "plugins": ["jest"],
            "rules": {
              "jest/no-focused-tests": "error",
              "jest/no-disabled-tests": "warn"
            }
          }
        ]
      }
      ```
3.  **验证**:
    - 在 `test/sample.test.ts` 中添加 `test.only('sample', () => {});`
    - 运行 `pnpm oxlint`，确认报告了 `jest/no-focused-tests` 错误。

### 示例 2: 在 CI 中运行 Oxlint 并自动修复

**任务**: 在 `lint-staged` 中配置 `Oxlint` 以自动修复暂存文件。

**代理工作流**:

1. **计划**:
   - 确认 `lint-staged` 和 `husky` 已安装。
   - 修改 `package.json` 中的 `lint-staged` 配置。
2. **执行**:
   - 修改 `package.json`:
     ```json
     {
       "lint-staged": {
         "**/*.{js,ts,jsx,tsx}": "oxlint --fix"
       }
     }
     ```
3. **验证**:
   - 修改一个有 `console.log` 的文件并暂存 (`git add`)。
   - 运行 `git commit`，观察 `lint-staged` 是否成功执行 `oxlint --fix` 并修复问题。
