{"version": 3, "file": "admin.js", "sources": ["../src/admin/components/LinksQueueAddModal.jsx", "../src/admin/components/LinksQueueDeleteModal.jsx", "../src/admin/components/LinksQueueListItem.jsx", "../node_modules/.pnpm/sortablejs@1.15.6/node_modules/sortablejs/modular/sortable.esm.js", "../src/admin/components/SettingsPage.jsx", "../src/forum/model/LinksQueue.js", "../src/admin/index.js"], "sourcesContent": ["import Modal from 'flarum/components/Modal';\nimport Button from 'flarum/components/Button';\nimport Stream from 'flarum/utils/Stream';\n\nexport default class LinksQueueAddModal extends Modal {\n  static isDismissible = false;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.LinksQueueItemData = this.attrs.LinksQueueItemData;\n    this.settingType = \"add\";\n\n    if(this.LinksQueueItemData){\n      this.settingType = \"edit\";\n      this.itemName = Stream(this.LinksQueueItemData.name());\n      this.itemUrl = Stream(this.LinksQueueItemData.links());\n    }else{\n      this.itemName = Stream(\"\");\n      this.itemUrl = Stream(\"\");\n    }\n  }\n\n  className() {\n    return 'Modal--Medium';\n  }\n\n  title() {\n    return this.settingType===\"add\"?app.translator.trans('wusong8899-links-queue.admin.settings.item-add'):app.translator.trans('wusong8899-links-queue.admin.settings.item-edit');\n  }\n\n  content() {\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form\">\n          <div className=\"Form-group\" style=\"text-align: center;\">\n            <div>\n              <div class=\"GuaGuaLeSettingsLabel\">{app.translator.trans('wusong8899-links-queue.admin.settings.item-name')}</div>\n              <input maxlength=\"255\" required className=\"FormControl\" bidi={this.itemName} />\n              <div class=\"GuaGuaLeSettingsLabel\">{app.translator.trans('wusong8899-links-queue.admin.settings.item-url')}</div>\n              <input maxlength=\"500\" required className=\"FormControl\" bidi={this.itemUrl} />\n            </div>\n          </div>\n\n          <div className=\"Form-group\" style=\"text-align: center;\">\n            {Button.component(\n              {\n                className: 'Button Button--primary',\n                type: 'submit',\n                loading: this.loading,\n              },\n              this.settingType===\"add\"?app.translator.trans('wusong8899-links-queue.admin.data-add'):app.translator.trans('wusong8899-links-queue.admin.data-save')\n            )}&nbsp;\n            {Button.component(\n              {\n                className: 'Button guagualeButton--gray',\n                loading: this.loading,\n                onclick: () => {\n                  this.hide();\n                }\n              },\n              app.translator.trans('wusong8899-links-queue.admin.cancel')\n            )}\n          </div>\n\n        </div>\n      </div>\n    );\n  }\n\n  onsubmit(e) {\n    e.preventDefault();\n\n    this.loading = true;\n\n    if(this.settingType===\"edit\"){\n      this.LinksQueueItemData.save({\n          name:this.itemName(),\n          url:this.itemUrl(),\n      })\n      .then(\n        () => this.hide(),\n        (response) => {\n          this.loading = false;\n          this.handleErrors(response);\n        }\n      );\n    }else{\n      app.store\n        .createRecord(\"linksQueueList\")\n        .save({\n          name:this.itemName(),\n          url:this.itemUrl(),\n        })\n        .then(\n          () => {\n            location.reload();\n          }\n        )\n        .catch(() => {\n          this.loading = false;\n          this.handleErrors();\n        });\n    }\n  }\n}\n", "import Modal from 'flarum/components/Modal';\nimport Button from 'flarum/components/Button';\n\nexport default class LinksQueueDeleteModal extends Modal {\n  static isDismissible = false;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.LinksQueueItemData = this.attrs.LinksQueueItemData;\n    this.loading = false;\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    return app.translator.trans('wusong8899-links-queue.admin.settings.item-delete-confirmation');\n  }\n\n  content() {\n    //\n\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form-group\" style=\"text-align: center;\">\n          {Button.component(\n            {\n              className: 'Button Button--primary',\n              type: 'submit',\n              loading: this.loading,\n            },\n            app.translator.trans('wusong8899-links-queue.admin.confirm')\n          )}&nbsp;\n          {Button.component(\n            {\n              className: 'Button guagualeButton--gray',\n              loading: this.loading,\n              onclick: () => {\n                this.hide();\n              }\n            },\n            app.translator.trans('wusong8899-links-queue.admin.cancel')\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  onsubmit(e) {\n    e.preventDefault();\n\n    this.loading = true;\n\n    this.LinksQueueItemData.delete()\n    .then(\n      () => {\n        location.reload();\n      }\n    );\n  }\n}\n", "import Component from \"flarum/Component\";\nimport Button from 'flarum/components/Button';\nimport LinksQueueAddModal from './LinksQueueAddModal.jsx';\nimport LinksQueueDeleteModal from './LinksQueueDeleteModal.jsx';\n\nexport default class LinksQueueListItem extends Component {\n  view() {\n    const {LinksQueueItemData} = this.attrs;\n    const linkID = LinksQueueItemData.id();\n    const linkName = LinksQueueItemData.name();\n    const linkUrl = LinksQueueItemData.links();\n\n    return (\n      <div style=\"border: 1px dotted var(--control-color);padding: 10px;border-radius: 4px;\">\n        <div>\n          <div style=\"padding-top: 5px;\">\n            <Button className={'Button Button--primary'} onclick={() => this.editItem(LinksQueueItemData)}>\n              {app.translator.trans('wusong8899-links-queue.admin.settings.item-edit')}\n            </Button>\n            &nbsp;\n            <Button style=\"font-weight:bold;width:66px;\" className={'Button Button--danger'} onclick={() => this.deleteItem(LinksQueueItemData)}>\n              {app.translator.trans('wusong8899-links-queue.admin.settings.item-delete')}\n            </Button>&nbsp;&nbsp;\n\n            <b>{app.translator.trans('wusong8899-links-queue.admin.settings.item-id')}: </b>\n            {linkID}&nbsp;|&nbsp;\n            <b>{app.translator.trans('wusong8899-links-queue.admin.settings.item-name')}: </b>\n            {linkName}&nbsp;|&nbsp;\n            <b>{app.translator.trans('wusong8899-links-queue.admin.settings.item-url')}: </b>\n            {linkUrl}&nbsp;\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  editItem(LinksQueueItemData) {\n    app.modal.show(LinksQueueAddModal, {LinksQueueItemData})\n  }\n\n  deleteItem(LinksQueueItemData) {\n    app.modal.show(LinksQueueDeleteModal, {LinksQueueItemData})\n  }\n}\n", "/**!\n * Sortable 1.15.6\n * <AUTHOR>   <<EMAIL>>\n * <AUTHOR>    <<EMAIL>>\n * @license MIT\n */\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n  return _typeof(obj);\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar version = \"1.15.6\";\n\nfunction userAgent(pattern) {\n  if (typeof window !== 'undefined' && window.navigator) {\n    return !! /*@__PURE__*/navigator.userAgent.match(pattern);\n  }\n}\nvar IE11OrLess = userAgent(/(?:Trident.*rv[ :]?11\\.|msie|iemobile|Windows Phone)/i);\nvar Edge = userAgent(/Edge/i);\nvar FireFox = userAgent(/firefox/i);\nvar Safari = userAgent(/safari/i) && !userAgent(/chrome/i) && !userAgent(/android/i);\nvar IOS = userAgent(/iP(ad|od|hone)/i);\nvar ChromeForAndroid = userAgent(/chrome/i) && userAgent(/android/i);\n\nvar captureMode = {\n  capture: false,\n  passive: false\n};\nfunction on(el, event, fn) {\n  el.addEventListener(event, fn, !IE11OrLess && captureMode);\n}\nfunction off(el, event, fn) {\n  el.removeEventListener(event, fn, !IE11OrLess && captureMode);\n}\nfunction matches( /**HTMLElement*/el, /**String*/selector) {\n  if (!selector) return;\n  selector[0] === '>' && (selector = selector.substring(1));\n  if (el) {\n    try {\n      if (el.matches) {\n        return el.matches(selector);\n      } else if (el.msMatchesSelector) {\n        return el.msMatchesSelector(selector);\n      } else if (el.webkitMatchesSelector) {\n        return el.webkitMatchesSelector(selector);\n      }\n    } catch (_) {\n      return false;\n    }\n  }\n  return false;\n}\nfunction getParentOrHost(el) {\n  return el.host && el !== document && el.host.nodeType ? el.host : el.parentNode;\n}\nfunction closest( /**HTMLElement*/el, /**String*/selector, /**HTMLElement*/ctx, includeCTX) {\n  if (el) {\n    ctx = ctx || document;\n    do {\n      if (selector != null && (selector[0] === '>' ? el.parentNode === ctx && matches(el, selector) : matches(el, selector)) || includeCTX && el === ctx) {\n        return el;\n      }\n      if (el === ctx) break;\n      /* jshint boss:true */\n    } while (el = getParentOrHost(el));\n  }\n  return null;\n}\nvar R_SPACE = /\\s+/g;\nfunction toggleClass(el, name, state) {\n  if (el && name) {\n    if (el.classList) {\n      el.classList[state ? 'add' : 'remove'](name);\n    } else {\n      var className = (' ' + el.className + ' ').replace(R_SPACE, ' ').replace(' ' + name + ' ', ' ');\n      el.className = (className + (state ? ' ' + name : '')).replace(R_SPACE, ' ');\n    }\n  }\n}\nfunction css(el, prop, val) {\n  var style = el && el.style;\n  if (style) {\n    if (val === void 0) {\n      if (document.defaultView && document.defaultView.getComputedStyle) {\n        val = document.defaultView.getComputedStyle(el, '');\n      } else if (el.currentStyle) {\n        val = el.currentStyle;\n      }\n      return prop === void 0 ? val : val[prop];\n    } else {\n      if (!(prop in style) && prop.indexOf('webkit') === -1) {\n        prop = '-webkit-' + prop;\n      }\n      style[prop] = val + (typeof val === 'string' ? '' : 'px');\n    }\n  }\n}\nfunction matrix(el, selfOnly) {\n  var appliedTransforms = '';\n  if (typeof el === 'string') {\n    appliedTransforms = el;\n  } else {\n    do {\n      var transform = css(el, 'transform');\n      if (transform && transform !== 'none') {\n        appliedTransforms = transform + ' ' + appliedTransforms;\n      }\n      /* jshint boss:true */\n    } while (!selfOnly && (el = el.parentNode));\n  }\n  var matrixFn = window.DOMMatrix || window.WebKitCSSMatrix || window.CSSMatrix || window.MSCSSMatrix;\n  /*jshint -W056 */\n  return matrixFn && new matrixFn(appliedTransforms);\n}\nfunction find(ctx, tagName, iterator) {\n  if (ctx) {\n    var list = ctx.getElementsByTagName(tagName),\n      i = 0,\n      n = list.length;\n    if (iterator) {\n      for (; i < n; i++) {\n        iterator(list[i], i);\n      }\n    }\n    return list;\n  }\n  return [];\n}\nfunction getWindowScrollingElement() {\n  var scrollingElement = document.scrollingElement;\n  if (scrollingElement) {\n    return scrollingElement;\n  } else {\n    return document.documentElement;\n  }\n}\n\n/**\r\n * Returns the \"bounding client rect\" of given element\r\n * @param  {HTMLElement} el                       The element whose boundingClientRect is wanted\r\n * @param  {[Boolean]} relativeToContainingBlock  Whether the rect should be relative to the containing block of (including) the container\r\n * @param  {[Boolean]} relativeToNonStaticParent  Whether the rect should be relative to the relative parent of (including) the contaienr\r\n * @param  {[Boolean]} undoScale                  Whether the container's scale() should be undone\r\n * @param  {[HTMLElement]} container              The parent the element will be placed in\r\n * @return {Object}                               The boundingClientRect of el, with specified adjustments\r\n */\nfunction getRect(el, relativeToContainingBlock, relativeToNonStaticParent, undoScale, container) {\n  if (!el.getBoundingClientRect && el !== window) return;\n  var elRect, top, left, bottom, right, height, width;\n  if (el !== window && el.parentNode && el !== getWindowScrollingElement()) {\n    elRect = el.getBoundingClientRect();\n    top = elRect.top;\n    left = elRect.left;\n    bottom = elRect.bottom;\n    right = elRect.right;\n    height = elRect.height;\n    width = elRect.width;\n  } else {\n    top = 0;\n    left = 0;\n    bottom = window.innerHeight;\n    right = window.innerWidth;\n    height = window.innerHeight;\n    width = window.innerWidth;\n  }\n  if ((relativeToContainingBlock || relativeToNonStaticParent) && el !== window) {\n    // Adjust for translate()\n    container = container || el.parentNode;\n\n    // solves #1123 (see: https://stackoverflow.com/a/37953806/6088312)\n    // Not needed on <= IE11\n    if (!IE11OrLess) {\n      do {\n        if (container && container.getBoundingClientRect && (css(container, 'transform') !== 'none' || relativeToNonStaticParent && css(container, 'position') !== 'static')) {\n          var containerRect = container.getBoundingClientRect();\n\n          // Set relative to edges of padding box of container\n          top -= containerRect.top + parseInt(css(container, 'border-top-width'));\n          left -= containerRect.left + parseInt(css(container, 'border-left-width'));\n          bottom = top + elRect.height;\n          right = left + elRect.width;\n          break;\n        }\n        /* jshint boss:true */\n      } while (container = container.parentNode);\n    }\n  }\n  if (undoScale && el !== window) {\n    // Adjust for scale()\n    var elMatrix = matrix(container || el),\n      scaleX = elMatrix && elMatrix.a,\n      scaleY = elMatrix && elMatrix.d;\n    if (elMatrix) {\n      top /= scaleY;\n      left /= scaleX;\n      width /= scaleX;\n      height /= scaleY;\n      bottom = top + height;\n      right = left + width;\n    }\n  }\n  return {\n    top: top,\n    left: left,\n    bottom: bottom,\n    right: right,\n    width: width,\n    height: height\n  };\n}\n\n/**\r\n * Checks if a side of an element is scrolled past a side of its parents\r\n * @param  {HTMLElement}  el           The element who's side being scrolled out of view is in question\r\n * @param  {String}       elSide       Side of the element in question ('top', 'left', 'right', 'bottom')\r\n * @param  {String}       parentSide   Side of the parent in question ('top', 'left', 'right', 'bottom')\r\n * @return {HTMLElement}               The parent scroll element that the el's side is scrolled past, or null if there is no such element\r\n */\nfunction isScrolledPast(el, elSide, parentSide) {\n  var parent = getParentAutoScrollElement(el, true),\n    elSideVal = getRect(el)[elSide];\n\n  /* jshint boss:true */\n  while (parent) {\n    var parentSideVal = getRect(parent)[parentSide],\n      visible = void 0;\n    if (parentSide === 'top' || parentSide === 'left') {\n      visible = elSideVal >= parentSideVal;\n    } else {\n      visible = elSideVal <= parentSideVal;\n    }\n    if (!visible) return parent;\n    if (parent === getWindowScrollingElement()) break;\n    parent = getParentAutoScrollElement(parent, false);\n  }\n  return false;\n}\n\n/**\r\n * Gets nth child of el, ignoring hidden children, sortable's elements (does not ignore clone if it's visible)\r\n * and non-draggable elements\r\n * @param  {HTMLElement} el       The parent element\r\n * @param  {Number} childNum      The index of the child\r\n * @param  {Object} options       Parent Sortable's options\r\n * @return {HTMLElement}          The child at index childNum, or null if not found\r\n */\nfunction getChild(el, childNum, options, includeDragEl) {\n  var currentChild = 0,\n    i = 0,\n    children = el.children;\n  while (i < children.length) {\n    if (children[i].style.display !== 'none' && children[i] !== Sortable.ghost && (includeDragEl || children[i] !== Sortable.dragged) && closest(children[i], options.draggable, el, false)) {\n      if (currentChild === childNum) {\n        return children[i];\n      }\n      currentChild++;\n    }\n    i++;\n  }\n  return null;\n}\n\n/**\r\n * Gets the last child in the el, ignoring ghostEl or invisible elements (clones)\r\n * @param  {HTMLElement} el       Parent element\r\n * @param  {selector} selector    Any other elements that should be ignored\r\n * @return {HTMLElement}          The last child, ignoring ghostEl\r\n */\nfunction lastChild(el, selector) {\n  var last = el.lastElementChild;\n  while (last && (last === Sortable.ghost || css(last, 'display') === 'none' || selector && !matches(last, selector))) {\n    last = last.previousElementSibling;\n  }\n  return last || null;\n}\n\n/**\r\n * Returns the index of an element within its parent for a selected set of\r\n * elements\r\n * @param  {HTMLElement} el\r\n * @param  {selector} selector\r\n * @return {number}\r\n */\nfunction index(el, selector) {\n  var index = 0;\n  if (!el || !el.parentNode) {\n    return -1;\n  }\n\n  /* jshint boss:true */\n  while (el = el.previousElementSibling) {\n    if (el.nodeName.toUpperCase() !== 'TEMPLATE' && el !== Sortable.clone && (!selector || matches(el, selector))) {\n      index++;\n    }\n  }\n  return index;\n}\n\n/**\r\n * Returns the scroll offset of the given element, added with all the scroll offsets of parent elements.\r\n * The value is returned in real pixels.\r\n * @param  {HTMLElement} el\r\n * @return {Array}             Offsets in the format of [left, top]\r\n */\nfunction getRelativeScrollOffset(el) {\n  var offsetLeft = 0,\n    offsetTop = 0,\n    winScroller = getWindowScrollingElement();\n  if (el) {\n    do {\n      var elMatrix = matrix(el),\n        scaleX = elMatrix.a,\n        scaleY = elMatrix.d;\n      offsetLeft += el.scrollLeft * scaleX;\n      offsetTop += el.scrollTop * scaleY;\n    } while (el !== winScroller && (el = el.parentNode));\n  }\n  return [offsetLeft, offsetTop];\n}\n\n/**\r\n * Returns the index of the object within the given array\r\n * @param  {Array} arr   Array that may or may not hold the object\r\n * @param  {Object} obj  An object that has a key-value pair unique to and identical to a key-value pair in the object you want to find\r\n * @return {Number}      The index of the object in the array, or -1\r\n */\nfunction indexOfObject(arr, obj) {\n  for (var i in arr) {\n    if (!arr.hasOwnProperty(i)) continue;\n    for (var key in obj) {\n      if (obj.hasOwnProperty(key) && obj[key] === arr[i][key]) return Number(i);\n    }\n  }\n  return -1;\n}\nfunction getParentAutoScrollElement(el, includeSelf) {\n  // skip to window\n  if (!el || !el.getBoundingClientRect) return getWindowScrollingElement();\n  var elem = el;\n  var gotSelf = false;\n  do {\n    // we don't need to get elem css if it isn't even overflowing in the first place (performance)\n    if (elem.clientWidth < elem.scrollWidth || elem.clientHeight < elem.scrollHeight) {\n      var elemCSS = css(elem);\n      if (elem.clientWidth < elem.scrollWidth && (elemCSS.overflowX == 'auto' || elemCSS.overflowX == 'scroll') || elem.clientHeight < elem.scrollHeight && (elemCSS.overflowY == 'auto' || elemCSS.overflowY == 'scroll')) {\n        if (!elem.getBoundingClientRect || elem === document.body) return getWindowScrollingElement();\n        if (gotSelf || includeSelf) return elem;\n        gotSelf = true;\n      }\n    }\n    /* jshint boss:true */\n  } while (elem = elem.parentNode);\n  return getWindowScrollingElement();\n}\nfunction extend(dst, src) {\n  if (dst && src) {\n    for (var key in src) {\n      if (src.hasOwnProperty(key)) {\n        dst[key] = src[key];\n      }\n    }\n  }\n  return dst;\n}\nfunction isRectEqual(rect1, rect2) {\n  return Math.round(rect1.top) === Math.round(rect2.top) && Math.round(rect1.left) === Math.round(rect2.left) && Math.round(rect1.height) === Math.round(rect2.height) && Math.round(rect1.width) === Math.round(rect2.width);\n}\nvar _throttleTimeout;\nfunction throttle(callback, ms) {\n  return function () {\n    if (!_throttleTimeout) {\n      var args = arguments,\n        _this = this;\n      if (args.length === 1) {\n        callback.call(_this, args[0]);\n      } else {\n        callback.apply(_this, args);\n      }\n      _throttleTimeout = setTimeout(function () {\n        _throttleTimeout = void 0;\n      }, ms);\n    }\n  };\n}\nfunction cancelThrottle() {\n  clearTimeout(_throttleTimeout);\n  _throttleTimeout = void 0;\n}\nfunction scrollBy(el, x, y) {\n  el.scrollLeft += x;\n  el.scrollTop += y;\n}\nfunction clone(el) {\n  var Polymer = window.Polymer;\n  var $ = window.jQuery || window.Zepto;\n  if (Polymer && Polymer.dom) {\n    return Polymer.dom(el).cloneNode(true);\n  } else if ($) {\n    return $(el).clone(true)[0];\n  } else {\n    return el.cloneNode(true);\n  }\n}\nfunction setRect(el, rect) {\n  css(el, 'position', 'absolute');\n  css(el, 'top', rect.top);\n  css(el, 'left', rect.left);\n  css(el, 'width', rect.width);\n  css(el, 'height', rect.height);\n}\nfunction unsetRect(el) {\n  css(el, 'position', '');\n  css(el, 'top', '');\n  css(el, 'left', '');\n  css(el, 'width', '');\n  css(el, 'height', '');\n}\nfunction getChildContainingRectFromElement(container, options, ghostEl) {\n  var rect = {};\n  Array.from(container.children).forEach(function (child) {\n    var _rect$left, _rect$top, _rect$right, _rect$bottom;\n    if (!closest(child, options.draggable, container, false) || child.animated || child === ghostEl) return;\n    var childRect = getRect(child);\n    rect.left = Math.min((_rect$left = rect.left) !== null && _rect$left !== void 0 ? _rect$left : Infinity, childRect.left);\n    rect.top = Math.min((_rect$top = rect.top) !== null && _rect$top !== void 0 ? _rect$top : Infinity, childRect.top);\n    rect.right = Math.max((_rect$right = rect.right) !== null && _rect$right !== void 0 ? _rect$right : -Infinity, childRect.right);\n    rect.bottom = Math.max((_rect$bottom = rect.bottom) !== null && _rect$bottom !== void 0 ? _rect$bottom : -Infinity, childRect.bottom);\n  });\n  rect.width = rect.right - rect.left;\n  rect.height = rect.bottom - rect.top;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\nvar expando = 'Sortable' + new Date().getTime();\n\nfunction AnimationStateManager() {\n  var animationStates = [],\n    animationCallbackId;\n  return {\n    captureAnimationState: function captureAnimationState() {\n      animationStates = [];\n      if (!this.options.animation) return;\n      var children = [].slice.call(this.el.children);\n      children.forEach(function (child) {\n        if (css(child, 'display') === 'none' || child === Sortable.ghost) return;\n        animationStates.push({\n          target: child,\n          rect: getRect(child)\n        });\n        var fromRect = _objectSpread2({}, animationStates[animationStates.length - 1].rect);\n\n        // If animating: compensate for current animation\n        if (child.thisAnimationDuration) {\n          var childMatrix = matrix(child, true);\n          if (childMatrix) {\n            fromRect.top -= childMatrix.f;\n            fromRect.left -= childMatrix.e;\n          }\n        }\n        child.fromRect = fromRect;\n      });\n    },\n    addAnimationState: function addAnimationState(state) {\n      animationStates.push(state);\n    },\n    removeAnimationState: function removeAnimationState(target) {\n      animationStates.splice(indexOfObject(animationStates, {\n        target: target\n      }), 1);\n    },\n    animateAll: function animateAll(callback) {\n      var _this = this;\n      if (!this.options.animation) {\n        clearTimeout(animationCallbackId);\n        if (typeof callback === 'function') callback();\n        return;\n      }\n      var animating = false,\n        animationTime = 0;\n      animationStates.forEach(function (state) {\n        var time = 0,\n          target = state.target,\n          fromRect = target.fromRect,\n          toRect = getRect(target),\n          prevFromRect = target.prevFromRect,\n          prevToRect = target.prevToRect,\n          animatingRect = state.rect,\n          targetMatrix = matrix(target, true);\n        if (targetMatrix) {\n          // Compensate for current animation\n          toRect.top -= targetMatrix.f;\n          toRect.left -= targetMatrix.e;\n        }\n        target.toRect = toRect;\n        if (target.thisAnimationDuration) {\n          // Could also check if animatingRect is between fromRect and toRect\n          if (isRectEqual(prevFromRect, toRect) && !isRectEqual(fromRect, toRect) &&\n          // Make sure animatingRect is on line between toRect & fromRect\n          (animatingRect.top - toRect.top) / (animatingRect.left - toRect.left) === (fromRect.top - toRect.top) / (fromRect.left - toRect.left)) {\n            // If returning to same place as started from animation and on same axis\n            time = calculateRealTime(animatingRect, prevFromRect, prevToRect, _this.options);\n          }\n        }\n\n        // if fromRect != toRect: animate\n        if (!isRectEqual(toRect, fromRect)) {\n          target.prevFromRect = fromRect;\n          target.prevToRect = toRect;\n          if (!time) {\n            time = _this.options.animation;\n          }\n          _this.animate(target, animatingRect, toRect, time);\n        }\n        if (time) {\n          animating = true;\n          animationTime = Math.max(animationTime, time);\n          clearTimeout(target.animationResetTimer);\n          target.animationResetTimer = setTimeout(function () {\n            target.animationTime = 0;\n            target.prevFromRect = null;\n            target.fromRect = null;\n            target.prevToRect = null;\n            target.thisAnimationDuration = null;\n          }, time);\n          target.thisAnimationDuration = time;\n        }\n      });\n      clearTimeout(animationCallbackId);\n      if (!animating) {\n        if (typeof callback === 'function') callback();\n      } else {\n        animationCallbackId = setTimeout(function () {\n          if (typeof callback === 'function') callback();\n        }, animationTime);\n      }\n      animationStates = [];\n    },\n    animate: function animate(target, currentRect, toRect, duration) {\n      if (duration) {\n        css(target, 'transition', '');\n        css(target, 'transform', '');\n        var elMatrix = matrix(this.el),\n          scaleX = elMatrix && elMatrix.a,\n          scaleY = elMatrix && elMatrix.d,\n          translateX = (currentRect.left - toRect.left) / (scaleX || 1),\n          translateY = (currentRect.top - toRect.top) / (scaleY || 1);\n        target.animatingX = !!translateX;\n        target.animatingY = !!translateY;\n        css(target, 'transform', 'translate3d(' + translateX + 'px,' + translateY + 'px,0)');\n        this.forRepaintDummy = repaint(target); // repaint\n\n        css(target, 'transition', 'transform ' + duration + 'ms' + (this.options.easing ? ' ' + this.options.easing : ''));\n        css(target, 'transform', 'translate3d(0,0,0)');\n        typeof target.animated === 'number' && clearTimeout(target.animated);\n        target.animated = setTimeout(function () {\n          css(target, 'transition', '');\n          css(target, 'transform', '');\n          target.animated = false;\n          target.animatingX = false;\n          target.animatingY = false;\n        }, duration);\n      }\n    }\n  };\n}\nfunction repaint(target) {\n  return target.offsetWidth;\n}\nfunction calculateRealTime(animatingRect, fromRect, toRect, options) {\n  return Math.sqrt(Math.pow(fromRect.top - animatingRect.top, 2) + Math.pow(fromRect.left - animatingRect.left, 2)) / Math.sqrt(Math.pow(fromRect.top - toRect.top, 2) + Math.pow(fromRect.left - toRect.left, 2)) * options.animation;\n}\n\nvar plugins = [];\nvar defaults = {\n  initializeByDefault: true\n};\nvar PluginManager = {\n  mount: function mount(plugin) {\n    // Set default static properties\n    for (var option in defaults) {\n      if (defaults.hasOwnProperty(option) && !(option in plugin)) {\n        plugin[option] = defaults[option];\n      }\n    }\n    plugins.forEach(function (p) {\n      if (p.pluginName === plugin.pluginName) {\n        throw \"Sortable: Cannot mount plugin \".concat(plugin.pluginName, \" more than once\");\n      }\n    });\n    plugins.push(plugin);\n  },\n  pluginEvent: function pluginEvent(eventName, sortable, evt) {\n    var _this = this;\n    this.eventCanceled = false;\n    evt.cancel = function () {\n      _this.eventCanceled = true;\n    };\n    var eventNameGlobal = eventName + 'Global';\n    plugins.forEach(function (plugin) {\n      if (!sortable[plugin.pluginName]) return;\n      // Fire global events if it exists in this sortable\n      if (sortable[plugin.pluginName][eventNameGlobal]) {\n        sortable[plugin.pluginName][eventNameGlobal](_objectSpread2({\n          sortable: sortable\n        }, evt));\n      }\n\n      // Only fire plugin event if plugin is enabled in this sortable,\n      // and plugin has event defined\n      if (sortable.options[plugin.pluginName] && sortable[plugin.pluginName][eventName]) {\n        sortable[plugin.pluginName][eventName](_objectSpread2({\n          sortable: sortable\n        }, evt));\n      }\n    });\n  },\n  initializePlugins: function initializePlugins(sortable, el, defaults, options) {\n    plugins.forEach(function (plugin) {\n      var pluginName = plugin.pluginName;\n      if (!sortable.options[pluginName] && !plugin.initializeByDefault) return;\n      var initialized = new plugin(sortable, el, sortable.options);\n      initialized.sortable = sortable;\n      initialized.options = sortable.options;\n      sortable[pluginName] = initialized;\n\n      // Add default options from plugin\n      _extends(defaults, initialized.defaults);\n    });\n    for (var option in sortable.options) {\n      if (!sortable.options.hasOwnProperty(option)) continue;\n      var modified = this.modifyOption(sortable, option, sortable.options[option]);\n      if (typeof modified !== 'undefined') {\n        sortable.options[option] = modified;\n      }\n    }\n  },\n  getEventProperties: function getEventProperties(name, sortable) {\n    var eventProperties = {};\n    plugins.forEach(function (plugin) {\n      if (typeof plugin.eventProperties !== 'function') return;\n      _extends(eventProperties, plugin.eventProperties.call(sortable[plugin.pluginName], name));\n    });\n    return eventProperties;\n  },\n  modifyOption: function modifyOption(sortable, name, value) {\n    var modifiedValue;\n    plugins.forEach(function (plugin) {\n      // Plugin must exist on the Sortable\n      if (!sortable[plugin.pluginName]) return;\n\n      // If static option listener exists for this option, call in the context of the Sortable's instance of this plugin\n      if (plugin.optionListeners && typeof plugin.optionListeners[name] === 'function') {\n        modifiedValue = plugin.optionListeners[name].call(sortable[plugin.pluginName], value);\n      }\n    });\n    return modifiedValue;\n  }\n};\n\nfunction dispatchEvent(_ref) {\n  var sortable = _ref.sortable,\n    rootEl = _ref.rootEl,\n    name = _ref.name,\n    targetEl = _ref.targetEl,\n    cloneEl = _ref.cloneEl,\n    toEl = _ref.toEl,\n    fromEl = _ref.fromEl,\n    oldIndex = _ref.oldIndex,\n    newIndex = _ref.newIndex,\n    oldDraggableIndex = _ref.oldDraggableIndex,\n    newDraggableIndex = _ref.newDraggableIndex,\n    originalEvent = _ref.originalEvent,\n    putSortable = _ref.putSortable,\n    extraEventProperties = _ref.extraEventProperties;\n  sortable = sortable || rootEl && rootEl[expando];\n  if (!sortable) return;\n  var evt,\n    options = sortable.options,\n    onName = 'on' + name.charAt(0).toUpperCase() + name.substr(1);\n  // Support for new CustomEvent feature\n  if (window.CustomEvent && !IE11OrLess && !Edge) {\n    evt = new CustomEvent(name, {\n      bubbles: true,\n      cancelable: true\n    });\n  } else {\n    evt = document.createEvent('Event');\n    evt.initEvent(name, true, true);\n  }\n  evt.to = toEl || rootEl;\n  evt.from = fromEl || rootEl;\n  evt.item = targetEl || rootEl;\n  evt.clone = cloneEl;\n  evt.oldIndex = oldIndex;\n  evt.newIndex = newIndex;\n  evt.oldDraggableIndex = oldDraggableIndex;\n  evt.newDraggableIndex = newDraggableIndex;\n  evt.originalEvent = originalEvent;\n  evt.pullMode = putSortable ? putSortable.lastPutMode : undefined;\n  var allEventProperties = _objectSpread2(_objectSpread2({}, extraEventProperties), PluginManager.getEventProperties(name, sortable));\n  for (var option in allEventProperties) {\n    evt[option] = allEventProperties[option];\n  }\n  if (rootEl) {\n    rootEl.dispatchEvent(evt);\n  }\n  if (options[onName]) {\n    options[onName].call(sortable, evt);\n  }\n}\n\nvar _excluded = [\"evt\"];\nvar pluginEvent = function pluginEvent(eventName, sortable) {\n  var _ref = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {},\n    originalEvent = _ref.evt,\n    data = _objectWithoutProperties(_ref, _excluded);\n  PluginManager.pluginEvent.bind(Sortable)(eventName, sortable, _objectSpread2({\n    dragEl: dragEl,\n    parentEl: parentEl,\n    ghostEl: ghostEl,\n    rootEl: rootEl,\n    nextEl: nextEl,\n    lastDownEl: lastDownEl,\n    cloneEl: cloneEl,\n    cloneHidden: cloneHidden,\n    dragStarted: moved,\n    putSortable: putSortable,\n    activeSortable: Sortable.active,\n    originalEvent: originalEvent,\n    oldIndex: oldIndex,\n    oldDraggableIndex: oldDraggableIndex,\n    newIndex: newIndex,\n    newDraggableIndex: newDraggableIndex,\n    hideGhostForTarget: _hideGhostForTarget,\n    unhideGhostForTarget: _unhideGhostForTarget,\n    cloneNowHidden: function cloneNowHidden() {\n      cloneHidden = true;\n    },\n    cloneNowShown: function cloneNowShown() {\n      cloneHidden = false;\n    },\n    dispatchSortableEvent: function dispatchSortableEvent(name) {\n      _dispatchEvent({\n        sortable: sortable,\n        name: name,\n        originalEvent: originalEvent\n      });\n    }\n  }, data));\n};\nfunction _dispatchEvent(info) {\n  dispatchEvent(_objectSpread2({\n    putSortable: putSortable,\n    cloneEl: cloneEl,\n    targetEl: dragEl,\n    rootEl: rootEl,\n    oldIndex: oldIndex,\n    oldDraggableIndex: oldDraggableIndex,\n    newIndex: newIndex,\n    newDraggableIndex: newDraggableIndex\n  }, info));\n}\nvar dragEl,\n  parentEl,\n  ghostEl,\n  rootEl,\n  nextEl,\n  lastDownEl,\n  cloneEl,\n  cloneHidden,\n  oldIndex,\n  newIndex,\n  oldDraggableIndex,\n  newDraggableIndex,\n  activeGroup,\n  putSortable,\n  awaitingDragStarted = false,\n  ignoreNextClick = false,\n  sortables = [],\n  tapEvt,\n  touchEvt,\n  lastDx,\n  lastDy,\n  tapDistanceLeft,\n  tapDistanceTop,\n  moved,\n  lastTarget,\n  lastDirection,\n  pastFirstInvertThresh = false,\n  isCircumstantialInvert = false,\n  targetMoveDistance,\n  // For positioning ghost absolutely\n  ghostRelativeParent,\n  ghostRelativeParentInitialScroll = [],\n  // (left, top)\n\n  _silent = false,\n  savedInputChecked = [];\n\n/** @const */\nvar documentExists = typeof document !== 'undefined',\n  PositionGhostAbsolutely = IOS,\n  CSSFloatProperty = Edge || IE11OrLess ? 'cssFloat' : 'float',\n  // This will not pass for IE9, because IE9 DnD only works on anchors\n  supportDraggable = documentExists && !ChromeForAndroid && !IOS && 'draggable' in document.createElement('div'),\n  supportCssPointerEvents = function () {\n    if (!documentExists) return;\n    // false when <= IE11\n    if (IE11OrLess) {\n      return false;\n    }\n    var el = document.createElement('x');\n    el.style.cssText = 'pointer-events:auto';\n    return el.style.pointerEvents === 'auto';\n  }(),\n  _detectDirection = function _detectDirection(el, options) {\n    var elCSS = css(el),\n      elWidth = parseInt(elCSS.width) - parseInt(elCSS.paddingLeft) - parseInt(elCSS.paddingRight) - parseInt(elCSS.borderLeftWidth) - parseInt(elCSS.borderRightWidth),\n      child1 = getChild(el, 0, options),\n      child2 = getChild(el, 1, options),\n      firstChildCSS = child1 && css(child1),\n      secondChildCSS = child2 && css(child2),\n      firstChildWidth = firstChildCSS && parseInt(firstChildCSS.marginLeft) + parseInt(firstChildCSS.marginRight) + getRect(child1).width,\n      secondChildWidth = secondChildCSS && parseInt(secondChildCSS.marginLeft) + parseInt(secondChildCSS.marginRight) + getRect(child2).width;\n    if (elCSS.display === 'flex') {\n      return elCSS.flexDirection === 'column' || elCSS.flexDirection === 'column-reverse' ? 'vertical' : 'horizontal';\n    }\n    if (elCSS.display === 'grid') {\n      return elCSS.gridTemplateColumns.split(' ').length <= 1 ? 'vertical' : 'horizontal';\n    }\n    if (child1 && firstChildCSS[\"float\"] && firstChildCSS[\"float\"] !== 'none') {\n      var touchingSideChild2 = firstChildCSS[\"float\"] === 'left' ? 'left' : 'right';\n      return child2 && (secondChildCSS.clear === 'both' || secondChildCSS.clear === touchingSideChild2) ? 'vertical' : 'horizontal';\n    }\n    return child1 && (firstChildCSS.display === 'block' || firstChildCSS.display === 'flex' || firstChildCSS.display === 'table' || firstChildCSS.display === 'grid' || firstChildWidth >= elWidth && elCSS[CSSFloatProperty] === 'none' || child2 && elCSS[CSSFloatProperty] === 'none' && firstChildWidth + secondChildWidth > elWidth) ? 'vertical' : 'horizontal';\n  },\n  _dragElInRowColumn = function _dragElInRowColumn(dragRect, targetRect, vertical) {\n    var dragElS1Opp = vertical ? dragRect.left : dragRect.top,\n      dragElS2Opp = vertical ? dragRect.right : dragRect.bottom,\n      dragElOppLength = vertical ? dragRect.width : dragRect.height,\n      targetS1Opp = vertical ? targetRect.left : targetRect.top,\n      targetS2Opp = vertical ? targetRect.right : targetRect.bottom,\n      targetOppLength = vertical ? targetRect.width : targetRect.height;\n    return dragElS1Opp === targetS1Opp || dragElS2Opp === targetS2Opp || dragElS1Opp + dragElOppLength / 2 === targetS1Opp + targetOppLength / 2;\n  },\n  /**\r\n   * Detects first nearest empty sortable to X and Y position using emptyInsertThreshold.\r\n   * @param  {Number} x      X position\r\n   * @param  {Number} y      Y position\r\n   * @return {HTMLElement}   Element of the first found nearest Sortable\r\n   */\n  _detectNearestEmptySortable = function _detectNearestEmptySortable(x, y) {\n    var ret;\n    sortables.some(function (sortable) {\n      var threshold = sortable[expando].options.emptyInsertThreshold;\n      if (!threshold || lastChild(sortable)) return;\n      var rect = getRect(sortable),\n        insideHorizontally = x >= rect.left - threshold && x <= rect.right + threshold,\n        insideVertically = y >= rect.top - threshold && y <= rect.bottom + threshold;\n      if (insideHorizontally && insideVertically) {\n        return ret = sortable;\n      }\n    });\n    return ret;\n  },\n  _prepareGroup = function _prepareGroup(options) {\n    function toFn(value, pull) {\n      return function (to, from, dragEl, evt) {\n        var sameGroup = to.options.group.name && from.options.group.name && to.options.group.name === from.options.group.name;\n        if (value == null && (pull || sameGroup)) {\n          // Default pull value\n          // Default pull and put value if same group\n          return true;\n        } else if (value == null || value === false) {\n          return false;\n        } else if (pull && value === 'clone') {\n          return value;\n        } else if (typeof value === 'function') {\n          return toFn(value(to, from, dragEl, evt), pull)(to, from, dragEl, evt);\n        } else {\n          var otherGroup = (pull ? to : from).options.group.name;\n          return value === true || typeof value === 'string' && value === otherGroup || value.join && value.indexOf(otherGroup) > -1;\n        }\n      };\n    }\n    var group = {};\n    var originalGroup = options.group;\n    if (!originalGroup || _typeof(originalGroup) != 'object') {\n      originalGroup = {\n        name: originalGroup\n      };\n    }\n    group.name = originalGroup.name;\n    group.checkPull = toFn(originalGroup.pull, true);\n    group.checkPut = toFn(originalGroup.put);\n    group.revertClone = originalGroup.revertClone;\n    options.group = group;\n  },\n  _hideGhostForTarget = function _hideGhostForTarget() {\n    if (!supportCssPointerEvents && ghostEl) {\n      css(ghostEl, 'display', 'none');\n    }\n  },\n  _unhideGhostForTarget = function _unhideGhostForTarget() {\n    if (!supportCssPointerEvents && ghostEl) {\n      css(ghostEl, 'display', '');\n    }\n  };\n\n// #1184 fix - Prevent click event on fallback if dragged but item not changed position\nif (documentExists && !ChromeForAndroid) {\n  document.addEventListener('click', function (evt) {\n    if (ignoreNextClick) {\n      evt.preventDefault();\n      evt.stopPropagation && evt.stopPropagation();\n      evt.stopImmediatePropagation && evt.stopImmediatePropagation();\n      ignoreNextClick = false;\n      return false;\n    }\n  }, true);\n}\nvar nearestEmptyInsertDetectEvent = function nearestEmptyInsertDetectEvent(evt) {\n  if (dragEl) {\n    evt = evt.touches ? evt.touches[0] : evt;\n    var nearest = _detectNearestEmptySortable(evt.clientX, evt.clientY);\n    if (nearest) {\n      // Create imitation event\n      var event = {};\n      for (var i in evt) {\n        if (evt.hasOwnProperty(i)) {\n          event[i] = evt[i];\n        }\n      }\n      event.target = event.rootEl = nearest;\n      event.preventDefault = void 0;\n      event.stopPropagation = void 0;\n      nearest[expando]._onDragOver(event);\n    }\n  }\n};\nvar _checkOutsideTargetEl = function _checkOutsideTargetEl(evt) {\n  if (dragEl) {\n    dragEl.parentNode[expando]._isOutsideThisEl(evt.target);\n  }\n};\n\n/**\r\n * @class  Sortable\r\n * @param  {HTMLElement}  el\r\n * @param  {Object}       [options]\r\n */\nfunction Sortable(el, options) {\n  if (!(el && el.nodeType && el.nodeType === 1)) {\n    throw \"Sortable: `el` must be an HTMLElement, not \".concat({}.toString.call(el));\n  }\n  this.el = el; // root element\n  this.options = options = _extends({}, options);\n\n  // Export instance\n  el[expando] = this;\n  var defaults = {\n    group: null,\n    sort: true,\n    disabled: false,\n    store: null,\n    handle: null,\n    draggable: /^[uo]l$/i.test(el.nodeName) ? '>li' : '>*',\n    swapThreshold: 1,\n    // percentage; 0 <= x <= 1\n    invertSwap: false,\n    // invert always\n    invertedSwapThreshold: null,\n    // will be set to same as swapThreshold if default\n    removeCloneOnHide: true,\n    direction: function direction() {\n      return _detectDirection(el, this.options);\n    },\n    ghostClass: 'sortable-ghost',\n    chosenClass: 'sortable-chosen',\n    dragClass: 'sortable-drag',\n    ignore: 'a, img',\n    filter: null,\n    preventOnFilter: true,\n    animation: 0,\n    easing: null,\n    setData: function setData(dataTransfer, dragEl) {\n      dataTransfer.setData('Text', dragEl.textContent);\n    },\n    dropBubble: false,\n    dragoverBubble: false,\n    dataIdAttr: 'data-id',\n    delay: 0,\n    delayOnTouchOnly: false,\n    touchStartThreshold: (Number.parseInt ? Number : window).parseInt(window.devicePixelRatio, 10) || 1,\n    forceFallback: false,\n    fallbackClass: 'sortable-fallback',\n    fallbackOnBody: false,\n    fallbackTolerance: 0,\n    fallbackOffset: {\n      x: 0,\n      y: 0\n    },\n    // Disabled on Safari: #1571; Enabled on Safari IOS: #2244\n    supportPointer: Sortable.supportPointer !== false && 'PointerEvent' in window && (!Safari || IOS),\n    emptyInsertThreshold: 5\n  };\n  PluginManager.initializePlugins(this, el, defaults);\n\n  // Set default options\n  for (var name in defaults) {\n    !(name in options) && (options[name] = defaults[name]);\n  }\n  _prepareGroup(options);\n\n  // Bind all private methods\n  for (var fn in this) {\n    if (fn.charAt(0) === '_' && typeof this[fn] === 'function') {\n      this[fn] = this[fn].bind(this);\n    }\n  }\n\n  // Setup drag mode\n  this.nativeDraggable = options.forceFallback ? false : supportDraggable;\n  if (this.nativeDraggable) {\n    // Touch start threshold cannot be greater than the native dragstart threshold\n    this.options.touchStartThreshold = 1;\n  }\n\n  // Bind events\n  if (options.supportPointer) {\n    on(el, 'pointerdown', this._onTapStart);\n  } else {\n    on(el, 'mousedown', this._onTapStart);\n    on(el, 'touchstart', this._onTapStart);\n  }\n  if (this.nativeDraggable) {\n    on(el, 'dragover', this);\n    on(el, 'dragenter', this);\n  }\n  sortables.push(this.el);\n\n  // Restore sorting\n  options.store && options.store.get && this.sort(options.store.get(this) || []);\n\n  // Add animation state manager\n  _extends(this, AnimationStateManager());\n}\nSortable.prototype = /** @lends Sortable.prototype */{\n  constructor: Sortable,\n  _isOutsideThisEl: function _isOutsideThisEl(target) {\n    if (!this.el.contains(target) && target !== this.el) {\n      lastTarget = null;\n    }\n  },\n  _getDirection: function _getDirection(evt, target) {\n    return typeof this.options.direction === 'function' ? this.options.direction.call(this, evt, target, dragEl) : this.options.direction;\n  },\n  _onTapStart: function _onTapStart( /** Event|TouchEvent */evt) {\n    if (!evt.cancelable) return;\n    var _this = this,\n      el = this.el,\n      options = this.options,\n      preventOnFilter = options.preventOnFilter,\n      type = evt.type,\n      touch = evt.touches && evt.touches[0] || evt.pointerType && evt.pointerType === 'touch' && evt,\n      target = (touch || evt).target,\n      originalTarget = evt.target.shadowRoot && (evt.path && evt.path[0] || evt.composedPath && evt.composedPath()[0]) || target,\n      filter = options.filter;\n    _saveInputCheckedState(el);\n\n    // Don't trigger start event when an element is been dragged, otherwise the evt.oldindex always wrong when set option.group.\n    if (dragEl) {\n      return;\n    }\n    if (/mousedown|pointerdown/.test(type) && evt.button !== 0 || options.disabled) {\n      return; // only left button and enabled\n    }\n\n    // cancel dnd if original target is content editable\n    if (originalTarget.isContentEditable) {\n      return;\n    }\n\n    // Safari ignores further event handling after mousedown\n    if (!this.nativeDraggable && Safari && target && target.tagName.toUpperCase() === 'SELECT') {\n      return;\n    }\n    target = closest(target, options.draggable, el, false);\n    if (target && target.animated) {\n      return;\n    }\n    if (lastDownEl === target) {\n      // Ignoring duplicate `down`\n      return;\n    }\n\n    // Get the index of the dragged element within its parent\n    oldIndex = index(target);\n    oldDraggableIndex = index(target, options.draggable);\n\n    // Check filter\n    if (typeof filter === 'function') {\n      if (filter.call(this, evt, target, this)) {\n        _dispatchEvent({\n          sortable: _this,\n          rootEl: originalTarget,\n          name: 'filter',\n          targetEl: target,\n          toEl: el,\n          fromEl: el\n        });\n        pluginEvent('filter', _this, {\n          evt: evt\n        });\n        preventOnFilter && evt.preventDefault();\n        return; // cancel dnd\n      }\n    } else if (filter) {\n      filter = filter.split(',').some(function (criteria) {\n        criteria = closest(originalTarget, criteria.trim(), el, false);\n        if (criteria) {\n          _dispatchEvent({\n            sortable: _this,\n            rootEl: criteria,\n            name: 'filter',\n            targetEl: target,\n            fromEl: el,\n            toEl: el\n          });\n          pluginEvent('filter', _this, {\n            evt: evt\n          });\n          return true;\n        }\n      });\n      if (filter) {\n        preventOnFilter && evt.preventDefault();\n        return; // cancel dnd\n      }\n    }\n    if (options.handle && !closest(originalTarget, options.handle, el, false)) {\n      return;\n    }\n\n    // Prepare `dragstart`\n    this._prepareDragStart(evt, touch, target);\n  },\n  _prepareDragStart: function _prepareDragStart( /** Event */evt, /** Touch */touch, /** HTMLElement */target) {\n    var _this = this,\n      el = _this.el,\n      options = _this.options,\n      ownerDocument = el.ownerDocument,\n      dragStartFn;\n    if (target && !dragEl && target.parentNode === el) {\n      var dragRect = getRect(target);\n      rootEl = el;\n      dragEl = target;\n      parentEl = dragEl.parentNode;\n      nextEl = dragEl.nextSibling;\n      lastDownEl = target;\n      activeGroup = options.group;\n      Sortable.dragged = dragEl;\n      tapEvt = {\n        target: dragEl,\n        clientX: (touch || evt).clientX,\n        clientY: (touch || evt).clientY\n      };\n      tapDistanceLeft = tapEvt.clientX - dragRect.left;\n      tapDistanceTop = tapEvt.clientY - dragRect.top;\n      this._lastX = (touch || evt).clientX;\n      this._lastY = (touch || evt).clientY;\n      dragEl.style['will-change'] = 'all';\n      dragStartFn = function dragStartFn() {\n        pluginEvent('delayEnded', _this, {\n          evt: evt\n        });\n        if (Sortable.eventCanceled) {\n          _this._onDrop();\n          return;\n        }\n        // Delayed drag has been triggered\n        // we can re-enable the events: touchmove/mousemove\n        _this._disableDelayedDragEvents();\n        if (!FireFox && _this.nativeDraggable) {\n          dragEl.draggable = true;\n        }\n\n        // Bind the events: dragstart/dragend\n        _this._triggerDragStart(evt, touch);\n\n        // Drag start event\n        _dispatchEvent({\n          sortable: _this,\n          name: 'choose',\n          originalEvent: evt\n        });\n\n        // Chosen item\n        toggleClass(dragEl, options.chosenClass, true);\n      };\n\n      // Disable \"draggable\"\n      options.ignore.split(',').forEach(function (criteria) {\n        find(dragEl, criteria.trim(), _disableDraggable);\n      });\n      on(ownerDocument, 'dragover', nearestEmptyInsertDetectEvent);\n      on(ownerDocument, 'mousemove', nearestEmptyInsertDetectEvent);\n      on(ownerDocument, 'touchmove', nearestEmptyInsertDetectEvent);\n      if (options.supportPointer) {\n        on(ownerDocument, 'pointerup', _this._onDrop);\n        // Native D&D triggers pointercancel\n        !this.nativeDraggable && on(ownerDocument, 'pointercancel', _this._onDrop);\n      } else {\n        on(ownerDocument, 'mouseup', _this._onDrop);\n        on(ownerDocument, 'touchend', _this._onDrop);\n        on(ownerDocument, 'touchcancel', _this._onDrop);\n      }\n\n      // Make dragEl draggable (must be before delay for FireFox)\n      if (FireFox && this.nativeDraggable) {\n        this.options.touchStartThreshold = 4;\n        dragEl.draggable = true;\n      }\n      pluginEvent('delayStart', this, {\n        evt: evt\n      });\n\n      // Delay is impossible for native DnD in Edge or IE\n      if (options.delay && (!options.delayOnTouchOnly || touch) && (!this.nativeDraggable || !(Edge || IE11OrLess))) {\n        if (Sortable.eventCanceled) {\n          this._onDrop();\n          return;\n        }\n        // If the user moves the pointer or let go the click or touch\n        // before the delay has been reached:\n        // disable the delayed drag\n        if (options.supportPointer) {\n          on(ownerDocument, 'pointerup', _this._disableDelayedDrag);\n          on(ownerDocument, 'pointercancel', _this._disableDelayedDrag);\n        } else {\n          on(ownerDocument, 'mouseup', _this._disableDelayedDrag);\n          on(ownerDocument, 'touchend', _this._disableDelayedDrag);\n          on(ownerDocument, 'touchcancel', _this._disableDelayedDrag);\n        }\n        on(ownerDocument, 'mousemove', _this._delayedDragTouchMoveHandler);\n        on(ownerDocument, 'touchmove', _this._delayedDragTouchMoveHandler);\n        options.supportPointer && on(ownerDocument, 'pointermove', _this._delayedDragTouchMoveHandler);\n        _this._dragStartTimer = setTimeout(dragStartFn, options.delay);\n      } else {\n        dragStartFn();\n      }\n    }\n  },\n  _delayedDragTouchMoveHandler: function _delayedDragTouchMoveHandler( /** TouchEvent|PointerEvent **/e) {\n    var touch = e.touches ? e.touches[0] : e;\n    if (Math.max(Math.abs(touch.clientX - this._lastX), Math.abs(touch.clientY - this._lastY)) >= Math.floor(this.options.touchStartThreshold / (this.nativeDraggable && window.devicePixelRatio || 1))) {\n      this._disableDelayedDrag();\n    }\n  },\n  _disableDelayedDrag: function _disableDelayedDrag() {\n    dragEl && _disableDraggable(dragEl);\n    clearTimeout(this._dragStartTimer);\n    this._disableDelayedDragEvents();\n  },\n  _disableDelayedDragEvents: function _disableDelayedDragEvents() {\n    var ownerDocument = this.el.ownerDocument;\n    off(ownerDocument, 'mouseup', this._disableDelayedDrag);\n    off(ownerDocument, 'touchend', this._disableDelayedDrag);\n    off(ownerDocument, 'touchcancel', this._disableDelayedDrag);\n    off(ownerDocument, 'pointerup', this._disableDelayedDrag);\n    off(ownerDocument, 'pointercancel', this._disableDelayedDrag);\n    off(ownerDocument, 'mousemove', this._delayedDragTouchMoveHandler);\n    off(ownerDocument, 'touchmove', this._delayedDragTouchMoveHandler);\n    off(ownerDocument, 'pointermove', this._delayedDragTouchMoveHandler);\n  },\n  _triggerDragStart: function _triggerDragStart( /** Event */evt, /** Touch */touch) {\n    touch = touch || evt.pointerType == 'touch' && evt;\n    if (!this.nativeDraggable || touch) {\n      if (this.options.supportPointer) {\n        on(document, 'pointermove', this._onTouchMove);\n      } else if (touch) {\n        on(document, 'touchmove', this._onTouchMove);\n      } else {\n        on(document, 'mousemove', this._onTouchMove);\n      }\n    } else {\n      on(dragEl, 'dragend', this);\n      on(rootEl, 'dragstart', this._onDragStart);\n    }\n    try {\n      if (document.selection) {\n        _nextTick(function () {\n          document.selection.empty();\n        });\n      } else {\n        window.getSelection().removeAllRanges();\n      }\n    } catch (err) {}\n  },\n  _dragStarted: function _dragStarted(fallback, evt) {\n    awaitingDragStarted = false;\n    if (rootEl && dragEl) {\n      pluginEvent('dragStarted', this, {\n        evt: evt\n      });\n      if (this.nativeDraggable) {\n        on(document, 'dragover', _checkOutsideTargetEl);\n      }\n      var options = this.options;\n\n      // Apply effect\n      !fallback && toggleClass(dragEl, options.dragClass, false);\n      toggleClass(dragEl, options.ghostClass, true);\n      Sortable.active = this;\n      fallback && this._appendGhost();\n\n      // Drag start event\n      _dispatchEvent({\n        sortable: this,\n        name: 'start',\n        originalEvent: evt\n      });\n    } else {\n      this._nulling();\n    }\n  },\n  _emulateDragOver: function _emulateDragOver() {\n    if (touchEvt) {\n      this._lastX = touchEvt.clientX;\n      this._lastY = touchEvt.clientY;\n      _hideGhostForTarget();\n      var target = document.elementFromPoint(touchEvt.clientX, touchEvt.clientY);\n      var parent = target;\n      while (target && target.shadowRoot) {\n        target = target.shadowRoot.elementFromPoint(touchEvt.clientX, touchEvt.clientY);\n        if (target === parent) break;\n        parent = target;\n      }\n      dragEl.parentNode[expando]._isOutsideThisEl(target);\n      if (parent) {\n        do {\n          if (parent[expando]) {\n            var inserted = void 0;\n            inserted = parent[expando]._onDragOver({\n              clientX: touchEvt.clientX,\n              clientY: touchEvt.clientY,\n              target: target,\n              rootEl: parent\n            });\n            if (inserted && !this.options.dragoverBubble) {\n              break;\n            }\n          }\n          target = parent; // store last element\n        }\n        /* jshint boss:true */ while (parent = getParentOrHost(parent));\n      }\n      _unhideGhostForTarget();\n    }\n  },\n  _onTouchMove: function _onTouchMove( /**TouchEvent*/evt) {\n    if (tapEvt) {\n      var options = this.options,\n        fallbackTolerance = options.fallbackTolerance,\n        fallbackOffset = options.fallbackOffset,\n        touch = evt.touches ? evt.touches[0] : evt,\n        ghostMatrix = ghostEl && matrix(ghostEl, true),\n        scaleX = ghostEl && ghostMatrix && ghostMatrix.a,\n        scaleY = ghostEl && ghostMatrix && ghostMatrix.d,\n        relativeScrollOffset = PositionGhostAbsolutely && ghostRelativeParent && getRelativeScrollOffset(ghostRelativeParent),\n        dx = (touch.clientX - tapEvt.clientX + fallbackOffset.x) / (scaleX || 1) + (relativeScrollOffset ? relativeScrollOffset[0] - ghostRelativeParentInitialScroll[0] : 0) / (scaleX || 1),\n        dy = (touch.clientY - tapEvt.clientY + fallbackOffset.y) / (scaleY || 1) + (relativeScrollOffset ? relativeScrollOffset[1] - ghostRelativeParentInitialScroll[1] : 0) / (scaleY || 1);\n\n      // only set the status to dragging, when we are actually dragging\n      if (!Sortable.active && !awaitingDragStarted) {\n        if (fallbackTolerance && Math.max(Math.abs(touch.clientX - this._lastX), Math.abs(touch.clientY - this._lastY)) < fallbackTolerance) {\n          return;\n        }\n        this._onDragStart(evt, true);\n      }\n      if (ghostEl) {\n        if (ghostMatrix) {\n          ghostMatrix.e += dx - (lastDx || 0);\n          ghostMatrix.f += dy - (lastDy || 0);\n        } else {\n          ghostMatrix = {\n            a: 1,\n            b: 0,\n            c: 0,\n            d: 1,\n            e: dx,\n            f: dy\n          };\n        }\n        var cssMatrix = \"matrix(\".concat(ghostMatrix.a, \",\").concat(ghostMatrix.b, \",\").concat(ghostMatrix.c, \",\").concat(ghostMatrix.d, \",\").concat(ghostMatrix.e, \",\").concat(ghostMatrix.f, \")\");\n        css(ghostEl, 'webkitTransform', cssMatrix);\n        css(ghostEl, 'mozTransform', cssMatrix);\n        css(ghostEl, 'msTransform', cssMatrix);\n        css(ghostEl, 'transform', cssMatrix);\n        lastDx = dx;\n        lastDy = dy;\n        touchEvt = touch;\n      }\n      evt.cancelable && evt.preventDefault();\n    }\n  },\n  _appendGhost: function _appendGhost() {\n    // Bug if using scale(): https://stackoverflow.com/questions/2637058\n    // Not being adjusted for\n    if (!ghostEl) {\n      var container = this.options.fallbackOnBody ? document.body : rootEl,\n        rect = getRect(dragEl, true, PositionGhostAbsolutely, true, container),\n        options = this.options;\n\n      // Position absolutely\n      if (PositionGhostAbsolutely) {\n        // Get relatively positioned parent\n        ghostRelativeParent = container;\n        while (css(ghostRelativeParent, 'position') === 'static' && css(ghostRelativeParent, 'transform') === 'none' && ghostRelativeParent !== document) {\n          ghostRelativeParent = ghostRelativeParent.parentNode;\n        }\n        if (ghostRelativeParent !== document.body && ghostRelativeParent !== document.documentElement) {\n          if (ghostRelativeParent === document) ghostRelativeParent = getWindowScrollingElement();\n          rect.top += ghostRelativeParent.scrollTop;\n          rect.left += ghostRelativeParent.scrollLeft;\n        } else {\n          ghostRelativeParent = getWindowScrollingElement();\n        }\n        ghostRelativeParentInitialScroll = getRelativeScrollOffset(ghostRelativeParent);\n      }\n      ghostEl = dragEl.cloneNode(true);\n      toggleClass(ghostEl, options.ghostClass, false);\n      toggleClass(ghostEl, options.fallbackClass, true);\n      toggleClass(ghostEl, options.dragClass, true);\n      css(ghostEl, 'transition', '');\n      css(ghostEl, 'transform', '');\n      css(ghostEl, 'box-sizing', 'border-box');\n      css(ghostEl, 'margin', 0);\n      css(ghostEl, 'top', rect.top);\n      css(ghostEl, 'left', rect.left);\n      css(ghostEl, 'width', rect.width);\n      css(ghostEl, 'height', rect.height);\n      css(ghostEl, 'opacity', '0.8');\n      css(ghostEl, 'position', PositionGhostAbsolutely ? 'absolute' : 'fixed');\n      css(ghostEl, 'zIndex', '100000');\n      css(ghostEl, 'pointerEvents', 'none');\n      Sortable.ghost = ghostEl;\n      container.appendChild(ghostEl);\n\n      // Set transform-origin\n      css(ghostEl, 'transform-origin', tapDistanceLeft / parseInt(ghostEl.style.width) * 100 + '% ' + tapDistanceTop / parseInt(ghostEl.style.height) * 100 + '%');\n    }\n  },\n  _onDragStart: function _onDragStart( /**Event*/evt, /**boolean*/fallback) {\n    var _this = this;\n    var dataTransfer = evt.dataTransfer;\n    var options = _this.options;\n    pluginEvent('dragStart', this, {\n      evt: evt\n    });\n    if (Sortable.eventCanceled) {\n      this._onDrop();\n      return;\n    }\n    pluginEvent('setupClone', this);\n    if (!Sortable.eventCanceled) {\n      cloneEl = clone(dragEl);\n      cloneEl.removeAttribute(\"id\");\n      cloneEl.draggable = false;\n      cloneEl.style['will-change'] = '';\n      this._hideClone();\n      toggleClass(cloneEl, this.options.chosenClass, false);\n      Sortable.clone = cloneEl;\n    }\n\n    // #1143: IFrame support workaround\n    _this.cloneId = _nextTick(function () {\n      pluginEvent('clone', _this);\n      if (Sortable.eventCanceled) return;\n      if (!_this.options.removeCloneOnHide) {\n        rootEl.insertBefore(cloneEl, dragEl);\n      }\n      _this._hideClone();\n      _dispatchEvent({\n        sortable: _this,\n        name: 'clone'\n      });\n    });\n    !fallback && toggleClass(dragEl, options.dragClass, true);\n\n    // Set proper drop events\n    if (fallback) {\n      ignoreNextClick = true;\n      _this._loopId = setInterval(_this._emulateDragOver, 50);\n    } else {\n      // Undo what was set in _prepareDragStart before drag started\n      off(document, 'mouseup', _this._onDrop);\n      off(document, 'touchend', _this._onDrop);\n      off(document, 'touchcancel', _this._onDrop);\n      if (dataTransfer) {\n        dataTransfer.effectAllowed = 'move';\n        options.setData && options.setData.call(_this, dataTransfer, dragEl);\n      }\n      on(document, 'drop', _this);\n\n      // #1276 fix:\n      css(dragEl, 'transform', 'translateZ(0)');\n    }\n    awaitingDragStarted = true;\n    _this._dragStartId = _nextTick(_this._dragStarted.bind(_this, fallback, evt));\n    on(document, 'selectstart', _this);\n    moved = true;\n    window.getSelection().removeAllRanges();\n    if (Safari) {\n      css(document.body, 'user-select', 'none');\n    }\n  },\n  // Returns true - if no further action is needed (either inserted or another condition)\n  _onDragOver: function _onDragOver( /**Event*/evt) {\n    var el = this.el,\n      target = evt.target,\n      dragRect,\n      targetRect,\n      revert,\n      options = this.options,\n      group = options.group,\n      activeSortable = Sortable.active,\n      isOwner = activeGroup === group,\n      canSort = options.sort,\n      fromSortable = putSortable || activeSortable,\n      vertical,\n      _this = this,\n      completedFired = false;\n    if (_silent) return;\n    function dragOverEvent(name, extra) {\n      pluginEvent(name, _this, _objectSpread2({\n        evt: evt,\n        isOwner: isOwner,\n        axis: vertical ? 'vertical' : 'horizontal',\n        revert: revert,\n        dragRect: dragRect,\n        targetRect: targetRect,\n        canSort: canSort,\n        fromSortable: fromSortable,\n        target: target,\n        completed: completed,\n        onMove: function onMove(target, after) {\n          return _onMove(rootEl, el, dragEl, dragRect, target, getRect(target), evt, after);\n        },\n        changed: changed\n      }, extra));\n    }\n\n    // Capture animation state\n    function capture() {\n      dragOverEvent('dragOverAnimationCapture');\n      _this.captureAnimationState();\n      if (_this !== fromSortable) {\n        fromSortable.captureAnimationState();\n      }\n    }\n\n    // Return invocation when dragEl is inserted (or completed)\n    function completed(insertion) {\n      dragOverEvent('dragOverCompleted', {\n        insertion: insertion\n      });\n      if (insertion) {\n        // Clones must be hidden before folding animation to capture dragRectAbsolute properly\n        if (isOwner) {\n          activeSortable._hideClone();\n        } else {\n          activeSortable._showClone(_this);\n        }\n        if (_this !== fromSortable) {\n          // Set ghost class to new sortable's ghost class\n          toggleClass(dragEl, putSortable ? putSortable.options.ghostClass : activeSortable.options.ghostClass, false);\n          toggleClass(dragEl, options.ghostClass, true);\n        }\n        if (putSortable !== _this && _this !== Sortable.active) {\n          putSortable = _this;\n        } else if (_this === Sortable.active && putSortable) {\n          putSortable = null;\n        }\n\n        // Animation\n        if (fromSortable === _this) {\n          _this._ignoreWhileAnimating = target;\n        }\n        _this.animateAll(function () {\n          dragOverEvent('dragOverAnimationComplete');\n          _this._ignoreWhileAnimating = null;\n        });\n        if (_this !== fromSortable) {\n          fromSortable.animateAll();\n          fromSortable._ignoreWhileAnimating = null;\n        }\n      }\n\n      // Null lastTarget if it is not inside a previously swapped element\n      if (target === dragEl && !dragEl.animated || target === el && !target.animated) {\n        lastTarget = null;\n      }\n\n      // no bubbling and not fallback\n      if (!options.dragoverBubble && !evt.rootEl && target !== document) {\n        dragEl.parentNode[expando]._isOutsideThisEl(evt.target);\n\n        // Do not detect for empty insert if already inserted\n        !insertion && nearestEmptyInsertDetectEvent(evt);\n      }\n      !options.dragoverBubble && evt.stopPropagation && evt.stopPropagation();\n      return completedFired = true;\n    }\n\n    // Call when dragEl has been inserted\n    function changed() {\n      newIndex = index(dragEl);\n      newDraggableIndex = index(dragEl, options.draggable);\n      _dispatchEvent({\n        sortable: _this,\n        name: 'change',\n        toEl: el,\n        newIndex: newIndex,\n        newDraggableIndex: newDraggableIndex,\n        originalEvent: evt\n      });\n    }\n    if (evt.preventDefault !== void 0) {\n      evt.cancelable && evt.preventDefault();\n    }\n    target = closest(target, options.draggable, el, true);\n    dragOverEvent('dragOver');\n    if (Sortable.eventCanceled) return completedFired;\n    if (dragEl.contains(evt.target) || target.animated && target.animatingX && target.animatingY || _this._ignoreWhileAnimating === target) {\n      return completed(false);\n    }\n    ignoreNextClick = false;\n    if (activeSortable && !options.disabled && (isOwner ? canSort || (revert = parentEl !== rootEl) // Reverting item into the original list\n    : putSortable === this || (this.lastPutMode = activeGroup.checkPull(this, activeSortable, dragEl, evt)) && group.checkPut(this, activeSortable, dragEl, evt))) {\n      vertical = this._getDirection(evt, target) === 'vertical';\n      dragRect = getRect(dragEl);\n      dragOverEvent('dragOverValid');\n      if (Sortable.eventCanceled) return completedFired;\n      if (revert) {\n        parentEl = rootEl; // actualization\n        capture();\n        this._hideClone();\n        dragOverEvent('revert');\n        if (!Sortable.eventCanceled) {\n          if (nextEl) {\n            rootEl.insertBefore(dragEl, nextEl);\n          } else {\n            rootEl.appendChild(dragEl);\n          }\n        }\n        return completed(true);\n      }\n      var elLastChild = lastChild(el, options.draggable);\n      if (!elLastChild || _ghostIsLast(evt, vertical, this) && !elLastChild.animated) {\n        // Insert to end of list\n\n        // If already at end of list: Do not insert\n        if (elLastChild === dragEl) {\n          return completed(false);\n        }\n\n        // if there is a last element, it is the target\n        if (elLastChild && el === evt.target) {\n          target = elLastChild;\n        }\n        if (target) {\n          targetRect = getRect(target);\n        }\n        if (_onMove(rootEl, el, dragEl, dragRect, target, targetRect, evt, !!target) !== false) {\n          capture();\n          if (elLastChild && elLastChild.nextSibling) {\n            // the last draggable element is not the last node\n            el.insertBefore(dragEl, elLastChild.nextSibling);\n          } else {\n            el.appendChild(dragEl);\n          }\n          parentEl = el; // actualization\n\n          changed();\n          return completed(true);\n        }\n      } else if (elLastChild && _ghostIsFirst(evt, vertical, this)) {\n        // Insert to start of list\n        var firstChild = getChild(el, 0, options, true);\n        if (firstChild === dragEl) {\n          return completed(false);\n        }\n        target = firstChild;\n        targetRect = getRect(target);\n        if (_onMove(rootEl, el, dragEl, dragRect, target, targetRect, evt, false) !== false) {\n          capture();\n          el.insertBefore(dragEl, firstChild);\n          parentEl = el; // actualization\n\n          changed();\n          return completed(true);\n        }\n      } else if (target.parentNode === el) {\n        targetRect = getRect(target);\n        var direction = 0,\n          targetBeforeFirstSwap,\n          differentLevel = dragEl.parentNode !== el,\n          differentRowCol = !_dragElInRowColumn(dragEl.animated && dragEl.toRect || dragRect, target.animated && target.toRect || targetRect, vertical),\n          side1 = vertical ? 'top' : 'left',\n          scrolledPastTop = isScrolledPast(target, 'top', 'top') || isScrolledPast(dragEl, 'top', 'top'),\n          scrollBefore = scrolledPastTop ? scrolledPastTop.scrollTop : void 0;\n        if (lastTarget !== target) {\n          targetBeforeFirstSwap = targetRect[side1];\n          pastFirstInvertThresh = false;\n          isCircumstantialInvert = !differentRowCol && options.invertSwap || differentLevel;\n        }\n        direction = _getSwapDirection(evt, target, targetRect, vertical, differentRowCol ? 1 : options.swapThreshold, options.invertedSwapThreshold == null ? options.swapThreshold : options.invertedSwapThreshold, isCircumstantialInvert, lastTarget === target);\n        var sibling;\n        if (direction !== 0) {\n          // Check if target is beside dragEl in respective direction (ignoring hidden elements)\n          var dragIndex = index(dragEl);\n          do {\n            dragIndex -= direction;\n            sibling = parentEl.children[dragIndex];\n          } while (sibling && (css(sibling, 'display') === 'none' || sibling === ghostEl));\n        }\n        // If dragEl is already beside target: Do not insert\n        if (direction === 0 || sibling === target) {\n          return completed(false);\n        }\n        lastTarget = target;\n        lastDirection = direction;\n        var nextSibling = target.nextElementSibling,\n          after = false;\n        after = direction === 1;\n        var moveVector = _onMove(rootEl, el, dragEl, dragRect, target, targetRect, evt, after);\n        if (moveVector !== false) {\n          if (moveVector === 1 || moveVector === -1) {\n            after = moveVector === 1;\n          }\n          _silent = true;\n          setTimeout(_unsilent, 30);\n          capture();\n          if (after && !nextSibling) {\n            el.appendChild(dragEl);\n          } else {\n            target.parentNode.insertBefore(dragEl, after ? nextSibling : target);\n          }\n\n          // Undo chrome's scroll adjustment (has no effect on other browsers)\n          if (scrolledPastTop) {\n            scrollBy(scrolledPastTop, 0, scrollBefore - scrolledPastTop.scrollTop);\n          }\n          parentEl = dragEl.parentNode; // actualization\n\n          // must be done before animation\n          if (targetBeforeFirstSwap !== undefined && !isCircumstantialInvert) {\n            targetMoveDistance = Math.abs(targetBeforeFirstSwap - getRect(target)[side1]);\n          }\n          changed();\n          return completed(true);\n        }\n      }\n      if (el.contains(dragEl)) {\n        return completed(false);\n      }\n    }\n    return false;\n  },\n  _ignoreWhileAnimating: null,\n  _offMoveEvents: function _offMoveEvents() {\n    off(document, 'mousemove', this._onTouchMove);\n    off(document, 'touchmove', this._onTouchMove);\n    off(document, 'pointermove', this._onTouchMove);\n    off(document, 'dragover', nearestEmptyInsertDetectEvent);\n    off(document, 'mousemove', nearestEmptyInsertDetectEvent);\n    off(document, 'touchmove', nearestEmptyInsertDetectEvent);\n  },\n  _offUpEvents: function _offUpEvents() {\n    var ownerDocument = this.el.ownerDocument;\n    off(ownerDocument, 'mouseup', this._onDrop);\n    off(ownerDocument, 'touchend', this._onDrop);\n    off(ownerDocument, 'pointerup', this._onDrop);\n    off(ownerDocument, 'pointercancel', this._onDrop);\n    off(ownerDocument, 'touchcancel', this._onDrop);\n    off(document, 'selectstart', this);\n  },\n  _onDrop: function _onDrop( /**Event*/evt) {\n    var el = this.el,\n      options = this.options;\n\n    // Get the index of the dragged element within its parent\n    newIndex = index(dragEl);\n    newDraggableIndex = index(dragEl, options.draggable);\n    pluginEvent('drop', this, {\n      evt: evt\n    });\n    parentEl = dragEl && dragEl.parentNode;\n\n    // Get again after plugin event\n    newIndex = index(dragEl);\n    newDraggableIndex = index(dragEl, options.draggable);\n    if (Sortable.eventCanceled) {\n      this._nulling();\n      return;\n    }\n    awaitingDragStarted = false;\n    isCircumstantialInvert = false;\n    pastFirstInvertThresh = false;\n    clearInterval(this._loopId);\n    clearTimeout(this._dragStartTimer);\n    _cancelNextTick(this.cloneId);\n    _cancelNextTick(this._dragStartId);\n\n    // Unbind events\n    if (this.nativeDraggable) {\n      off(document, 'drop', this);\n      off(el, 'dragstart', this._onDragStart);\n    }\n    this._offMoveEvents();\n    this._offUpEvents();\n    if (Safari) {\n      css(document.body, 'user-select', '');\n    }\n    css(dragEl, 'transform', '');\n    if (evt) {\n      if (moved) {\n        evt.cancelable && evt.preventDefault();\n        !options.dropBubble && evt.stopPropagation();\n      }\n      ghostEl && ghostEl.parentNode && ghostEl.parentNode.removeChild(ghostEl);\n      if (rootEl === parentEl || putSortable && putSortable.lastPutMode !== 'clone') {\n        // Remove clone(s)\n        cloneEl && cloneEl.parentNode && cloneEl.parentNode.removeChild(cloneEl);\n      }\n      if (dragEl) {\n        if (this.nativeDraggable) {\n          off(dragEl, 'dragend', this);\n        }\n        _disableDraggable(dragEl);\n        dragEl.style['will-change'] = '';\n\n        // Remove classes\n        // ghostClass is added in dragStarted\n        if (moved && !awaitingDragStarted) {\n          toggleClass(dragEl, putSortable ? putSortable.options.ghostClass : this.options.ghostClass, false);\n        }\n        toggleClass(dragEl, this.options.chosenClass, false);\n\n        // Drag stop event\n        _dispatchEvent({\n          sortable: this,\n          name: 'unchoose',\n          toEl: parentEl,\n          newIndex: null,\n          newDraggableIndex: null,\n          originalEvent: evt\n        });\n        if (rootEl !== parentEl) {\n          if (newIndex >= 0) {\n            // Add event\n            _dispatchEvent({\n              rootEl: parentEl,\n              name: 'add',\n              toEl: parentEl,\n              fromEl: rootEl,\n              originalEvent: evt\n            });\n\n            // Remove event\n            _dispatchEvent({\n              sortable: this,\n              name: 'remove',\n              toEl: parentEl,\n              originalEvent: evt\n            });\n\n            // drag from one list and drop into another\n            _dispatchEvent({\n              rootEl: parentEl,\n              name: 'sort',\n              toEl: parentEl,\n              fromEl: rootEl,\n              originalEvent: evt\n            });\n            _dispatchEvent({\n              sortable: this,\n              name: 'sort',\n              toEl: parentEl,\n              originalEvent: evt\n            });\n          }\n          putSortable && putSortable.save();\n        } else {\n          if (newIndex !== oldIndex) {\n            if (newIndex >= 0) {\n              // drag & drop within the same list\n              _dispatchEvent({\n                sortable: this,\n                name: 'update',\n                toEl: parentEl,\n                originalEvent: evt\n              });\n              _dispatchEvent({\n                sortable: this,\n                name: 'sort',\n                toEl: parentEl,\n                originalEvent: evt\n              });\n            }\n          }\n        }\n        if (Sortable.active) {\n          /* jshint eqnull:true */\n          if (newIndex == null || newIndex === -1) {\n            newIndex = oldIndex;\n            newDraggableIndex = oldDraggableIndex;\n          }\n          _dispatchEvent({\n            sortable: this,\n            name: 'end',\n            toEl: parentEl,\n            originalEvent: evt\n          });\n\n          // Save sorting\n          this.save();\n        }\n      }\n    }\n    this._nulling();\n  },\n  _nulling: function _nulling() {\n    pluginEvent('nulling', this);\n    rootEl = dragEl = parentEl = ghostEl = nextEl = cloneEl = lastDownEl = cloneHidden = tapEvt = touchEvt = moved = newIndex = newDraggableIndex = oldIndex = oldDraggableIndex = lastTarget = lastDirection = putSortable = activeGroup = Sortable.dragged = Sortable.ghost = Sortable.clone = Sortable.active = null;\n    savedInputChecked.forEach(function (el) {\n      el.checked = true;\n    });\n    savedInputChecked.length = lastDx = lastDy = 0;\n  },\n  handleEvent: function handleEvent( /**Event*/evt) {\n    switch (evt.type) {\n      case 'drop':\n      case 'dragend':\n        this._onDrop(evt);\n        break;\n      case 'dragenter':\n      case 'dragover':\n        if (dragEl) {\n          this._onDragOver(evt);\n          _globalDragOver(evt);\n        }\n        break;\n      case 'selectstart':\n        evt.preventDefault();\n        break;\n    }\n  },\n  /**\r\n   * Serializes the item into an array of string.\r\n   * @returns {String[]}\r\n   */\n  toArray: function toArray() {\n    var order = [],\n      el,\n      children = this.el.children,\n      i = 0,\n      n = children.length,\n      options = this.options;\n    for (; i < n; i++) {\n      el = children[i];\n      if (closest(el, options.draggable, this.el, false)) {\n        order.push(el.getAttribute(options.dataIdAttr) || _generateId(el));\n      }\n    }\n    return order;\n  },\n  /**\r\n   * Sorts the elements according to the array.\r\n   * @param  {String[]}  order  order of the items\r\n   */\n  sort: function sort(order, useAnimation) {\n    var items = {},\n      rootEl = this.el;\n    this.toArray().forEach(function (id, i) {\n      var el = rootEl.children[i];\n      if (closest(el, this.options.draggable, rootEl, false)) {\n        items[id] = el;\n      }\n    }, this);\n    useAnimation && this.captureAnimationState();\n    order.forEach(function (id) {\n      if (items[id]) {\n        rootEl.removeChild(items[id]);\n        rootEl.appendChild(items[id]);\n      }\n    });\n    useAnimation && this.animateAll();\n  },\n  /**\r\n   * Save the current sorting\r\n   */\n  save: function save() {\n    var store = this.options.store;\n    store && store.set && store.set(this);\n  },\n  /**\r\n   * For each element in the set, get the first element that matches the selector by testing the element itself and traversing up through its ancestors in the DOM tree.\r\n   * @param   {HTMLElement}  el\r\n   * @param   {String}       [selector]  default: `options.draggable`\r\n   * @returns {HTMLElement|null}\r\n   */\n  closest: function closest$1(el, selector) {\n    return closest(el, selector || this.options.draggable, this.el, false);\n  },\n  /**\r\n   * Set/get option\r\n   * @param   {string} name\r\n   * @param   {*}      [value]\r\n   * @returns {*}\r\n   */\n  option: function option(name, value) {\n    var options = this.options;\n    if (value === void 0) {\n      return options[name];\n    } else {\n      var modifiedValue = PluginManager.modifyOption(this, name, value);\n      if (typeof modifiedValue !== 'undefined') {\n        options[name] = modifiedValue;\n      } else {\n        options[name] = value;\n      }\n      if (name === 'group') {\n        _prepareGroup(options);\n      }\n    }\n  },\n  /**\r\n   * Destroy\r\n   */\n  destroy: function destroy() {\n    pluginEvent('destroy', this);\n    var el = this.el;\n    el[expando] = null;\n    off(el, 'mousedown', this._onTapStart);\n    off(el, 'touchstart', this._onTapStart);\n    off(el, 'pointerdown', this._onTapStart);\n    if (this.nativeDraggable) {\n      off(el, 'dragover', this);\n      off(el, 'dragenter', this);\n    }\n    // Remove draggable attributes\n    Array.prototype.forEach.call(el.querySelectorAll('[draggable]'), function (el) {\n      el.removeAttribute('draggable');\n    });\n    this._onDrop();\n    this._disableDelayedDragEvents();\n    sortables.splice(sortables.indexOf(this.el), 1);\n    this.el = el = null;\n  },\n  _hideClone: function _hideClone() {\n    if (!cloneHidden) {\n      pluginEvent('hideClone', this);\n      if (Sortable.eventCanceled) return;\n      css(cloneEl, 'display', 'none');\n      if (this.options.removeCloneOnHide && cloneEl.parentNode) {\n        cloneEl.parentNode.removeChild(cloneEl);\n      }\n      cloneHidden = true;\n    }\n  },\n  _showClone: function _showClone(putSortable) {\n    if (putSortable.lastPutMode !== 'clone') {\n      this._hideClone();\n      return;\n    }\n    if (cloneHidden) {\n      pluginEvent('showClone', this);\n      if (Sortable.eventCanceled) return;\n\n      // show clone at dragEl or original position\n      if (dragEl.parentNode == rootEl && !this.options.group.revertClone) {\n        rootEl.insertBefore(cloneEl, dragEl);\n      } else if (nextEl) {\n        rootEl.insertBefore(cloneEl, nextEl);\n      } else {\n        rootEl.appendChild(cloneEl);\n      }\n      if (this.options.group.revertClone) {\n        this.animate(dragEl, cloneEl);\n      }\n      css(cloneEl, 'display', '');\n      cloneHidden = false;\n    }\n  }\n};\nfunction _globalDragOver( /**Event*/evt) {\n  if (evt.dataTransfer) {\n    evt.dataTransfer.dropEffect = 'move';\n  }\n  evt.cancelable && evt.preventDefault();\n}\nfunction _onMove(fromEl, toEl, dragEl, dragRect, targetEl, targetRect, originalEvent, willInsertAfter) {\n  var evt,\n    sortable = fromEl[expando],\n    onMoveFn = sortable.options.onMove,\n    retVal;\n  // Support for new CustomEvent feature\n  if (window.CustomEvent && !IE11OrLess && !Edge) {\n    evt = new CustomEvent('move', {\n      bubbles: true,\n      cancelable: true\n    });\n  } else {\n    evt = document.createEvent('Event');\n    evt.initEvent('move', true, true);\n  }\n  evt.to = toEl;\n  evt.from = fromEl;\n  evt.dragged = dragEl;\n  evt.draggedRect = dragRect;\n  evt.related = targetEl || toEl;\n  evt.relatedRect = targetRect || getRect(toEl);\n  evt.willInsertAfter = willInsertAfter;\n  evt.originalEvent = originalEvent;\n  fromEl.dispatchEvent(evt);\n  if (onMoveFn) {\n    retVal = onMoveFn.call(sortable, evt, originalEvent);\n  }\n  return retVal;\n}\nfunction _disableDraggable(el) {\n  el.draggable = false;\n}\nfunction _unsilent() {\n  _silent = false;\n}\nfunction _ghostIsFirst(evt, vertical, sortable) {\n  var firstElRect = getRect(getChild(sortable.el, 0, sortable.options, true));\n  var childContainingRect = getChildContainingRectFromElement(sortable.el, sortable.options, ghostEl);\n  var spacer = 10;\n  return vertical ? evt.clientX < childContainingRect.left - spacer || evt.clientY < firstElRect.top && evt.clientX < firstElRect.right : evt.clientY < childContainingRect.top - spacer || evt.clientY < firstElRect.bottom && evt.clientX < firstElRect.left;\n}\nfunction _ghostIsLast(evt, vertical, sortable) {\n  var lastElRect = getRect(lastChild(sortable.el, sortable.options.draggable));\n  var childContainingRect = getChildContainingRectFromElement(sortable.el, sortable.options, ghostEl);\n  var spacer = 10;\n  return vertical ? evt.clientX > childContainingRect.right + spacer || evt.clientY > lastElRect.bottom && evt.clientX > lastElRect.left : evt.clientY > childContainingRect.bottom + spacer || evt.clientX > lastElRect.right && evt.clientY > lastElRect.top;\n}\nfunction _getSwapDirection(evt, target, targetRect, vertical, swapThreshold, invertedSwapThreshold, invertSwap, isLastTarget) {\n  var mouseOnAxis = vertical ? evt.clientY : evt.clientX,\n    targetLength = vertical ? targetRect.height : targetRect.width,\n    targetS1 = vertical ? targetRect.top : targetRect.left,\n    targetS2 = vertical ? targetRect.bottom : targetRect.right,\n    invert = false;\n  if (!invertSwap) {\n    // Never invert or create dragEl shadow when target movemenet causes mouse to move past the end of regular swapThreshold\n    if (isLastTarget && targetMoveDistance < targetLength * swapThreshold) {\n      // multiplied only by swapThreshold because mouse will already be inside target by (1 - threshold) * targetLength / 2\n      // check if past first invert threshold on side opposite of lastDirection\n      if (!pastFirstInvertThresh && (lastDirection === 1 ? mouseOnAxis > targetS1 + targetLength * invertedSwapThreshold / 2 : mouseOnAxis < targetS2 - targetLength * invertedSwapThreshold / 2)) {\n        // past first invert threshold, do not restrict inverted threshold to dragEl shadow\n        pastFirstInvertThresh = true;\n      }\n      if (!pastFirstInvertThresh) {\n        // dragEl shadow (target move distance shadow)\n        if (lastDirection === 1 ? mouseOnAxis < targetS1 + targetMoveDistance // over dragEl shadow\n        : mouseOnAxis > targetS2 - targetMoveDistance) {\n          return -lastDirection;\n        }\n      } else {\n        invert = true;\n      }\n    } else {\n      // Regular\n      if (mouseOnAxis > targetS1 + targetLength * (1 - swapThreshold) / 2 && mouseOnAxis < targetS2 - targetLength * (1 - swapThreshold) / 2) {\n        return _getInsertDirection(target);\n      }\n    }\n  }\n  invert = invert || invertSwap;\n  if (invert) {\n    // Invert of regular\n    if (mouseOnAxis < targetS1 + targetLength * invertedSwapThreshold / 2 || mouseOnAxis > targetS2 - targetLength * invertedSwapThreshold / 2) {\n      return mouseOnAxis > targetS1 + targetLength / 2 ? 1 : -1;\n    }\n  }\n  return 0;\n}\n\n/**\r\n * Gets the direction dragEl must be swapped relative to target in order to make it\r\n * seem that dragEl has been \"inserted\" into that element's position\r\n * @param  {HTMLElement} target       The target whose position dragEl is being inserted at\r\n * @return {Number}                   Direction dragEl must be swapped\r\n */\nfunction _getInsertDirection(target) {\n  if (index(dragEl) < index(target)) {\n    return 1;\n  } else {\n    return -1;\n  }\n}\n\n/**\r\n * Generate id\r\n * @param   {HTMLElement} el\r\n * @returns {String}\r\n * @private\r\n */\nfunction _generateId(el) {\n  var str = el.tagName + el.className + el.src + el.href + el.textContent,\n    i = str.length,\n    sum = 0;\n  while (i--) {\n    sum += str.charCodeAt(i);\n  }\n  return sum.toString(36);\n}\nfunction _saveInputCheckedState(root) {\n  savedInputChecked.length = 0;\n  var inputs = root.getElementsByTagName('input');\n  var idx = inputs.length;\n  while (idx--) {\n    var el = inputs[idx];\n    el.checked && savedInputChecked.push(el);\n  }\n}\nfunction _nextTick(fn) {\n  return setTimeout(fn, 0);\n}\nfunction _cancelNextTick(id) {\n  return clearTimeout(id);\n}\n\n// Fixed #973:\nif (documentExists) {\n  on(document, 'touchmove', function (evt) {\n    if ((Sortable.active || awaitingDragStarted) && evt.cancelable) {\n      evt.preventDefault();\n    }\n  });\n}\n\n// Export utils\nSortable.utils = {\n  on: on,\n  off: off,\n  css: css,\n  find: find,\n  is: function is(el, selector) {\n    return !!closest(el, selector, el, false);\n  },\n  extend: extend,\n  throttle: throttle,\n  closest: closest,\n  toggleClass: toggleClass,\n  clone: clone,\n  index: index,\n  nextTick: _nextTick,\n  cancelNextTick: _cancelNextTick,\n  detectDirection: _detectDirection,\n  getChild: getChild,\n  expando: expando\n};\n\n/**\r\n * Get the Sortable instance of an element\r\n * @param  {HTMLElement} element The element\r\n * @return {Sortable|undefined}         The instance of Sortable\r\n */\nSortable.get = function (element) {\n  return element[expando];\n};\n\n/**\r\n * Mount a plugin to Sortable\r\n * @param  {...SortablePlugin|SortablePlugin[]} plugins       Plugins being mounted\r\n */\nSortable.mount = function () {\n  for (var _len = arguments.length, plugins = new Array(_len), _key = 0; _key < _len; _key++) {\n    plugins[_key] = arguments[_key];\n  }\n  if (plugins[0].constructor === Array) plugins = plugins[0];\n  plugins.forEach(function (plugin) {\n    if (!plugin.prototype || !plugin.prototype.constructor) {\n      throw \"Sortable: Mounted plugin must be a constructor function, not \".concat({}.toString.call(plugin));\n    }\n    if (plugin.utils) Sortable.utils = _objectSpread2(_objectSpread2({}, Sortable.utils), plugin.utils);\n    PluginManager.mount(plugin);\n  });\n};\n\n/**\r\n * Create sortable instance\r\n * @param {HTMLElement}  el\r\n * @param {Object}      [options]\r\n */\nSortable.create = function (el, options) {\n  return new Sortable(el, options);\n};\n\n// Export\nSortable.version = version;\n\nvar autoScrolls = [],\n  scrollEl,\n  scrollRootEl,\n  scrolling = false,\n  lastAutoScrollX,\n  lastAutoScrollY,\n  touchEvt$1,\n  pointerElemChangedInterval;\nfunction AutoScrollPlugin() {\n  function AutoScroll() {\n    this.defaults = {\n      scroll: true,\n      forceAutoScrollFallback: false,\n      scrollSensitivity: 30,\n      scrollSpeed: 10,\n      bubbleScroll: true\n    };\n\n    // Bind all private methods\n    for (var fn in this) {\n      if (fn.charAt(0) === '_' && typeof this[fn] === 'function') {\n        this[fn] = this[fn].bind(this);\n      }\n    }\n  }\n  AutoScroll.prototype = {\n    dragStarted: function dragStarted(_ref) {\n      var originalEvent = _ref.originalEvent;\n      if (this.sortable.nativeDraggable) {\n        on(document, 'dragover', this._handleAutoScroll);\n      } else {\n        if (this.options.supportPointer) {\n          on(document, 'pointermove', this._handleFallbackAutoScroll);\n        } else if (originalEvent.touches) {\n          on(document, 'touchmove', this._handleFallbackAutoScroll);\n        } else {\n          on(document, 'mousemove', this._handleFallbackAutoScroll);\n        }\n      }\n    },\n    dragOverCompleted: function dragOverCompleted(_ref2) {\n      var originalEvent = _ref2.originalEvent;\n      // For when bubbling is canceled and using fallback (fallback 'touchmove' always reached)\n      if (!this.options.dragOverBubble && !originalEvent.rootEl) {\n        this._handleAutoScroll(originalEvent);\n      }\n    },\n    drop: function drop() {\n      if (this.sortable.nativeDraggable) {\n        off(document, 'dragover', this._handleAutoScroll);\n      } else {\n        off(document, 'pointermove', this._handleFallbackAutoScroll);\n        off(document, 'touchmove', this._handleFallbackAutoScroll);\n        off(document, 'mousemove', this._handleFallbackAutoScroll);\n      }\n      clearPointerElemChangedInterval();\n      clearAutoScrolls();\n      cancelThrottle();\n    },\n    nulling: function nulling() {\n      touchEvt$1 = scrollRootEl = scrollEl = scrolling = pointerElemChangedInterval = lastAutoScrollX = lastAutoScrollY = null;\n      autoScrolls.length = 0;\n    },\n    _handleFallbackAutoScroll: function _handleFallbackAutoScroll(evt) {\n      this._handleAutoScroll(evt, true);\n    },\n    _handleAutoScroll: function _handleAutoScroll(evt, fallback) {\n      var _this = this;\n      var x = (evt.touches ? evt.touches[0] : evt).clientX,\n        y = (evt.touches ? evt.touches[0] : evt).clientY,\n        elem = document.elementFromPoint(x, y);\n      touchEvt$1 = evt;\n\n      // IE does not seem to have native autoscroll,\n      // Edge's autoscroll seems too conditional,\n      // MACOS Safari does not have autoscroll,\n      // Firefox and Chrome are good\n      if (fallback || this.options.forceAutoScrollFallback || Edge || IE11OrLess || Safari) {\n        autoScroll(evt, this.options, elem, fallback);\n\n        // Listener for pointer element change\n        var ogElemScroller = getParentAutoScrollElement(elem, true);\n        if (scrolling && (!pointerElemChangedInterval || x !== lastAutoScrollX || y !== lastAutoScrollY)) {\n          pointerElemChangedInterval && clearPointerElemChangedInterval();\n          // Detect for pointer elem change, emulating native DnD behaviour\n          pointerElemChangedInterval = setInterval(function () {\n            var newElem = getParentAutoScrollElement(document.elementFromPoint(x, y), true);\n            if (newElem !== ogElemScroller) {\n              ogElemScroller = newElem;\n              clearAutoScrolls();\n            }\n            autoScroll(evt, _this.options, newElem, fallback);\n          }, 10);\n          lastAutoScrollX = x;\n          lastAutoScrollY = y;\n        }\n      } else {\n        // if DnD is enabled (and browser has good autoscrolling), first autoscroll will already scroll, so get parent autoscroll of first autoscroll\n        if (!this.options.bubbleScroll || getParentAutoScrollElement(elem, true) === getWindowScrollingElement()) {\n          clearAutoScrolls();\n          return;\n        }\n        autoScroll(evt, this.options, getParentAutoScrollElement(elem, false), false);\n      }\n    }\n  };\n  return _extends(AutoScroll, {\n    pluginName: 'scroll',\n    initializeByDefault: true\n  });\n}\nfunction clearAutoScrolls() {\n  autoScrolls.forEach(function (autoScroll) {\n    clearInterval(autoScroll.pid);\n  });\n  autoScrolls = [];\n}\nfunction clearPointerElemChangedInterval() {\n  clearInterval(pointerElemChangedInterval);\n}\nvar autoScroll = throttle(function (evt, options, rootEl, isFallback) {\n  // Bug: https://bugzilla.mozilla.org/show_bug.cgi?id=505521\n  if (!options.scroll) return;\n  var x = (evt.touches ? evt.touches[0] : evt).clientX,\n    y = (evt.touches ? evt.touches[0] : evt).clientY,\n    sens = options.scrollSensitivity,\n    speed = options.scrollSpeed,\n    winScroller = getWindowScrollingElement();\n  var scrollThisInstance = false,\n    scrollCustomFn;\n\n  // New scroll root, set scrollEl\n  if (scrollRootEl !== rootEl) {\n    scrollRootEl = rootEl;\n    clearAutoScrolls();\n    scrollEl = options.scroll;\n    scrollCustomFn = options.scrollFn;\n    if (scrollEl === true) {\n      scrollEl = getParentAutoScrollElement(rootEl, true);\n    }\n  }\n  var layersOut = 0;\n  var currentParent = scrollEl;\n  do {\n    var el = currentParent,\n      rect = getRect(el),\n      top = rect.top,\n      bottom = rect.bottom,\n      left = rect.left,\n      right = rect.right,\n      width = rect.width,\n      height = rect.height,\n      canScrollX = void 0,\n      canScrollY = void 0,\n      scrollWidth = el.scrollWidth,\n      scrollHeight = el.scrollHeight,\n      elCSS = css(el),\n      scrollPosX = el.scrollLeft,\n      scrollPosY = el.scrollTop;\n    if (el === winScroller) {\n      canScrollX = width < scrollWidth && (elCSS.overflowX === 'auto' || elCSS.overflowX === 'scroll' || elCSS.overflowX === 'visible');\n      canScrollY = height < scrollHeight && (elCSS.overflowY === 'auto' || elCSS.overflowY === 'scroll' || elCSS.overflowY === 'visible');\n    } else {\n      canScrollX = width < scrollWidth && (elCSS.overflowX === 'auto' || elCSS.overflowX === 'scroll');\n      canScrollY = height < scrollHeight && (elCSS.overflowY === 'auto' || elCSS.overflowY === 'scroll');\n    }\n    var vx = canScrollX && (Math.abs(right - x) <= sens && scrollPosX + width < scrollWidth) - (Math.abs(left - x) <= sens && !!scrollPosX);\n    var vy = canScrollY && (Math.abs(bottom - y) <= sens && scrollPosY + height < scrollHeight) - (Math.abs(top - y) <= sens && !!scrollPosY);\n    if (!autoScrolls[layersOut]) {\n      for (var i = 0; i <= layersOut; i++) {\n        if (!autoScrolls[i]) {\n          autoScrolls[i] = {};\n        }\n      }\n    }\n    if (autoScrolls[layersOut].vx != vx || autoScrolls[layersOut].vy != vy || autoScrolls[layersOut].el !== el) {\n      autoScrolls[layersOut].el = el;\n      autoScrolls[layersOut].vx = vx;\n      autoScrolls[layersOut].vy = vy;\n      clearInterval(autoScrolls[layersOut].pid);\n      if (vx != 0 || vy != 0) {\n        scrollThisInstance = true;\n        /* jshint loopfunc:true */\n        autoScrolls[layersOut].pid = setInterval(function () {\n          // emulate drag over during autoscroll (fallback), emulating native DnD behaviour\n          if (isFallback && this.layer === 0) {\n            Sortable.active._onTouchMove(touchEvt$1); // To move ghost if it is positioned absolutely\n          }\n          var scrollOffsetY = autoScrolls[this.layer].vy ? autoScrolls[this.layer].vy * speed : 0;\n          var scrollOffsetX = autoScrolls[this.layer].vx ? autoScrolls[this.layer].vx * speed : 0;\n          if (typeof scrollCustomFn === 'function') {\n            if (scrollCustomFn.call(Sortable.dragged.parentNode[expando], scrollOffsetX, scrollOffsetY, evt, touchEvt$1, autoScrolls[this.layer].el) !== 'continue') {\n              return;\n            }\n          }\n          scrollBy(autoScrolls[this.layer].el, scrollOffsetX, scrollOffsetY);\n        }.bind({\n          layer: layersOut\n        }), 24);\n      }\n    }\n    layersOut++;\n  } while (options.bubbleScroll && currentParent !== winScroller && (currentParent = getParentAutoScrollElement(currentParent, false)));\n  scrolling = scrollThisInstance; // in case another function catches scrolling as false in between when it is not\n}, 30);\n\nvar drop = function drop(_ref) {\n  var originalEvent = _ref.originalEvent,\n    putSortable = _ref.putSortable,\n    dragEl = _ref.dragEl,\n    activeSortable = _ref.activeSortable,\n    dispatchSortableEvent = _ref.dispatchSortableEvent,\n    hideGhostForTarget = _ref.hideGhostForTarget,\n    unhideGhostForTarget = _ref.unhideGhostForTarget;\n  if (!originalEvent) return;\n  var toSortable = putSortable || activeSortable;\n  hideGhostForTarget();\n  var touch = originalEvent.changedTouches && originalEvent.changedTouches.length ? originalEvent.changedTouches[0] : originalEvent;\n  var target = document.elementFromPoint(touch.clientX, touch.clientY);\n  unhideGhostForTarget();\n  if (toSortable && !toSortable.el.contains(target)) {\n    dispatchSortableEvent('spill');\n    this.onSpill({\n      dragEl: dragEl,\n      putSortable: putSortable\n    });\n  }\n};\nfunction Revert() {}\nRevert.prototype = {\n  startIndex: null,\n  dragStart: function dragStart(_ref2) {\n    var oldDraggableIndex = _ref2.oldDraggableIndex;\n    this.startIndex = oldDraggableIndex;\n  },\n  onSpill: function onSpill(_ref3) {\n    var dragEl = _ref3.dragEl,\n      putSortable = _ref3.putSortable;\n    this.sortable.captureAnimationState();\n    if (putSortable) {\n      putSortable.captureAnimationState();\n    }\n    var nextSibling = getChild(this.sortable.el, this.startIndex, this.options);\n    if (nextSibling) {\n      this.sortable.el.insertBefore(dragEl, nextSibling);\n    } else {\n      this.sortable.el.appendChild(dragEl);\n    }\n    this.sortable.animateAll();\n    if (putSortable) {\n      putSortable.animateAll();\n    }\n  },\n  drop: drop\n};\n_extends(Revert, {\n  pluginName: 'revertOnSpill'\n});\nfunction Remove() {}\nRemove.prototype = {\n  onSpill: function onSpill(_ref4) {\n    var dragEl = _ref4.dragEl,\n      putSortable = _ref4.putSortable;\n    var parentSortable = putSortable || this.sortable;\n    parentSortable.captureAnimationState();\n    dragEl.parentNode && dragEl.parentNode.removeChild(dragEl);\n    parentSortable.animateAll();\n  },\n  drop: drop\n};\n_extends(Remove, {\n  pluginName: 'removeOnSpill'\n});\n\nvar lastSwapEl;\nfunction SwapPlugin() {\n  function Swap() {\n    this.defaults = {\n      swapClass: 'sortable-swap-highlight'\n    };\n  }\n  Swap.prototype = {\n    dragStart: function dragStart(_ref) {\n      var dragEl = _ref.dragEl;\n      lastSwapEl = dragEl;\n    },\n    dragOverValid: function dragOverValid(_ref2) {\n      var completed = _ref2.completed,\n        target = _ref2.target,\n        onMove = _ref2.onMove,\n        activeSortable = _ref2.activeSortable,\n        changed = _ref2.changed,\n        cancel = _ref2.cancel;\n      if (!activeSortable.options.swap) return;\n      var el = this.sortable.el,\n        options = this.options;\n      if (target && target !== el) {\n        var prevSwapEl = lastSwapEl;\n        if (onMove(target) !== false) {\n          toggleClass(target, options.swapClass, true);\n          lastSwapEl = target;\n        } else {\n          lastSwapEl = null;\n        }\n        if (prevSwapEl && prevSwapEl !== lastSwapEl) {\n          toggleClass(prevSwapEl, options.swapClass, false);\n        }\n      }\n      changed();\n      completed(true);\n      cancel();\n    },\n    drop: function drop(_ref3) {\n      var activeSortable = _ref3.activeSortable,\n        putSortable = _ref3.putSortable,\n        dragEl = _ref3.dragEl;\n      var toSortable = putSortable || this.sortable;\n      var options = this.options;\n      lastSwapEl && toggleClass(lastSwapEl, options.swapClass, false);\n      if (lastSwapEl && (options.swap || putSortable && putSortable.options.swap)) {\n        if (dragEl !== lastSwapEl) {\n          toSortable.captureAnimationState();\n          if (toSortable !== activeSortable) activeSortable.captureAnimationState();\n          swapNodes(dragEl, lastSwapEl);\n          toSortable.animateAll();\n          if (toSortable !== activeSortable) activeSortable.animateAll();\n        }\n      }\n    },\n    nulling: function nulling() {\n      lastSwapEl = null;\n    }\n  };\n  return _extends(Swap, {\n    pluginName: 'swap',\n    eventProperties: function eventProperties() {\n      return {\n        swapItem: lastSwapEl\n      };\n    }\n  });\n}\nfunction swapNodes(n1, n2) {\n  var p1 = n1.parentNode,\n    p2 = n2.parentNode,\n    i1,\n    i2;\n  if (!p1 || !p2 || p1.isEqualNode(n2) || p2.isEqualNode(n1)) return;\n  i1 = index(n1);\n  i2 = index(n2);\n  if (p1.isEqualNode(p2) && i1 < i2) {\n    i2++;\n  }\n  p1.insertBefore(n2, p1.children[i1]);\n  p2.insertBefore(n1, p2.children[i2]);\n}\n\nvar multiDragElements = [],\n  multiDragClones = [],\n  lastMultiDragSelect,\n  // for selection with modifier key down (SHIFT)\n  multiDragSortable,\n  initialFolding = false,\n  // Initial multi-drag fold when drag started\n  folding = false,\n  // Folding any other time\n  dragStarted = false,\n  dragEl$1,\n  clonesFromRect,\n  clonesHidden;\nfunction MultiDragPlugin() {\n  function MultiDrag(sortable) {\n    // Bind all private methods\n    for (var fn in this) {\n      if (fn.charAt(0) === '_' && typeof this[fn] === 'function') {\n        this[fn] = this[fn].bind(this);\n      }\n    }\n    if (!sortable.options.avoidImplicitDeselect) {\n      if (sortable.options.supportPointer) {\n        on(document, 'pointerup', this._deselectMultiDrag);\n      } else {\n        on(document, 'mouseup', this._deselectMultiDrag);\n        on(document, 'touchend', this._deselectMultiDrag);\n      }\n    }\n    on(document, 'keydown', this._checkKeyDown);\n    on(document, 'keyup', this._checkKeyUp);\n    this.defaults = {\n      selectedClass: 'sortable-selected',\n      multiDragKey: null,\n      avoidImplicitDeselect: false,\n      setData: function setData(dataTransfer, dragEl) {\n        var data = '';\n        if (multiDragElements.length && multiDragSortable === sortable) {\n          multiDragElements.forEach(function (multiDragElement, i) {\n            data += (!i ? '' : ', ') + multiDragElement.textContent;\n          });\n        } else {\n          data = dragEl.textContent;\n        }\n        dataTransfer.setData('Text', data);\n      }\n    };\n  }\n  MultiDrag.prototype = {\n    multiDragKeyDown: false,\n    isMultiDrag: false,\n    delayStartGlobal: function delayStartGlobal(_ref) {\n      var dragged = _ref.dragEl;\n      dragEl$1 = dragged;\n    },\n    delayEnded: function delayEnded() {\n      this.isMultiDrag = ~multiDragElements.indexOf(dragEl$1);\n    },\n    setupClone: function setupClone(_ref2) {\n      var sortable = _ref2.sortable,\n        cancel = _ref2.cancel;\n      if (!this.isMultiDrag) return;\n      for (var i = 0; i < multiDragElements.length; i++) {\n        multiDragClones.push(clone(multiDragElements[i]));\n        multiDragClones[i].sortableIndex = multiDragElements[i].sortableIndex;\n        multiDragClones[i].draggable = false;\n        multiDragClones[i].style['will-change'] = '';\n        toggleClass(multiDragClones[i], this.options.selectedClass, false);\n        multiDragElements[i] === dragEl$1 && toggleClass(multiDragClones[i], this.options.chosenClass, false);\n      }\n      sortable._hideClone();\n      cancel();\n    },\n    clone: function clone(_ref3) {\n      var sortable = _ref3.sortable,\n        rootEl = _ref3.rootEl,\n        dispatchSortableEvent = _ref3.dispatchSortableEvent,\n        cancel = _ref3.cancel;\n      if (!this.isMultiDrag) return;\n      if (!this.options.removeCloneOnHide) {\n        if (multiDragElements.length && multiDragSortable === sortable) {\n          insertMultiDragClones(true, rootEl);\n          dispatchSortableEvent('clone');\n          cancel();\n        }\n      }\n    },\n    showClone: function showClone(_ref4) {\n      var cloneNowShown = _ref4.cloneNowShown,\n        rootEl = _ref4.rootEl,\n        cancel = _ref4.cancel;\n      if (!this.isMultiDrag) return;\n      insertMultiDragClones(false, rootEl);\n      multiDragClones.forEach(function (clone) {\n        css(clone, 'display', '');\n      });\n      cloneNowShown();\n      clonesHidden = false;\n      cancel();\n    },\n    hideClone: function hideClone(_ref5) {\n      var _this = this;\n      var sortable = _ref5.sortable,\n        cloneNowHidden = _ref5.cloneNowHidden,\n        cancel = _ref5.cancel;\n      if (!this.isMultiDrag) return;\n      multiDragClones.forEach(function (clone) {\n        css(clone, 'display', 'none');\n        if (_this.options.removeCloneOnHide && clone.parentNode) {\n          clone.parentNode.removeChild(clone);\n        }\n      });\n      cloneNowHidden();\n      clonesHidden = true;\n      cancel();\n    },\n    dragStartGlobal: function dragStartGlobal(_ref6) {\n      var sortable = _ref6.sortable;\n      if (!this.isMultiDrag && multiDragSortable) {\n        multiDragSortable.multiDrag._deselectMultiDrag();\n      }\n      multiDragElements.forEach(function (multiDragElement) {\n        multiDragElement.sortableIndex = index(multiDragElement);\n      });\n\n      // Sort multi-drag elements\n      multiDragElements = multiDragElements.sort(function (a, b) {\n        return a.sortableIndex - b.sortableIndex;\n      });\n      dragStarted = true;\n    },\n    dragStarted: function dragStarted(_ref7) {\n      var _this2 = this;\n      var sortable = _ref7.sortable;\n      if (!this.isMultiDrag) return;\n      if (this.options.sort) {\n        // Capture rects,\n        // hide multi drag elements (by positioning them absolute),\n        // set multi drag elements rects to dragRect,\n        // show multi drag elements,\n        // animate to rects,\n        // unset rects & remove from DOM\n\n        sortable.captureAnimationState();\n        if (this.options.animation) {\n          multiDragElements.forEach(function (multiDragElement) {\n            if (multiDragElement === dragEl$1) return;\n            css(multiDragElement, 'position', 'absolute');\n          });\n          var dragRect = getRect(dragEl$1, false, true, true);\n          multiDragElements.forEach(function (multiDragElement) {\n            if (multiDragElement === dragEl$1) return;\n            setRect(multiDragElement, dragRect);\n          });\n          folding = true;\n          initialFolding = true;\n        }\n      }\n      sortable.animateAll(function () {\n        folding = false;\n        initialFolding = false;\n        if (_this2.options.animation) {\n          multiDragElements.forEach(function (multiDragElement) {\n            unsetRect(multiDragElement);\n          });\n        }\n\n        // Remove all auxiliary multidrag items from el, if sorting enabled\n        if (_this2.options.sort) {\n          removeMultiDragElements();\n        }\n      });\n    },\n    dragOver: function dragOver(_ref8) {\n      var target = _ref8.target,\n        completed = _ref8.completed,\n        cancel = _ref8.cancel;\n      if (folding && ~multiDragElements.indexOf(target)) {\n        completed(false);\n        cancel();\n      }\n    },\n    revert: function revert(_ref9) {\n      var fromSortable = _ref9.fromSortable,\n        rootEl = _ref9.rootEl,\n        sortable = _ref9.sortable,\n        dragRect = _ref9.dragRect;\n      if (multiDragElements.length > 1) {\n        // Setup unfold animation\n        multiDragElements.forEach(function (multiDragElement) {\n          sortable.addAnimationState({\n            target: multiDragElement,\n            rect: folding ? getRect(multiDragElement) : dragRect\n          });\n          unsetRect(multiDragElement);\n          multiDragElement.fromRect = dragRect;\n          fromSortable.removeAnimationState(multiDragElement);\n        });\n        folding = false;\n        insertMultiDragElements(!this.options.removeCloneOnHide, rootEl);\n      }\n    },\n    dragOverCompleted: function dragOverCompleted(_ref10) {\n      var sortable = _ref10.sortable,\n        isOwner = _ref10.isOwner,\n        insertion = _ref10.insertion,\n        activeSortable = _ref10.activeSortable,\n        parentEl = _ref10.parentEl,\n        putSortable = _ref10.putSortable;\n      var options = this.options;\n      if (insertion) {\n        // Clones must be hidden before folding animation to capture dragRectAbsolute properly\n        if (isOwner) {\n          activeSortable._hideClone();\n        }\n        initialFolding = false;\n        // If leaving sort:false root, or already folding - Fold to new location\n        if (options.animation && multiDragElements.length > 1 && (folding || !isOwner && !activeSortable.options.sort && !putSortable)) {\n          // Fold: Set all multi drag elements's rects to dragEl's rect when multi-drag elements are invisible\n          var dragRectAbsolute = getRect(dragEl$1, false, true, true);\n          multiDragElements.forEach(function (multiDragElement) {\n            if (multiDragElement === dragEl$1) return;\n            setRect(multiDragElement, dragRectAbsolute);\n\n            // Move element(s) to end of parentEl so that it does not interfere with multi-drag clones insertion if they are inserted\n            // while folding, and so that we can capture them again because old sortable will no longer be fromSortable\n            parentEl.appendChild(multiDragElement);\n          });\n          folding = true;\n        }\n\n        // Clones must be shown (and check to remove multi drags) after folding when interfering multiDragElements are moved out\n        if (!isOwner) {\n          // Only remove if not folding (folding will remove them anyways)\n          if (!folding) {\n            removeMultiDragElements();\n          }\n          if (multiDragElements.length > 1) {\n            var clonesHiddenBefore = clonesHidden;\n            activeSortable._showClone(sortable);\n\n            // Unfold animation for clones if showing from hidden\n            if (activeSortable.options.animation && !clonesHidden && clonesHiddenBefore) {\n              multiDragClones.forEach(function (clone) {\n                activeSortable.addAnimationState({\n                  target: clone,\n                  rect: clonesFromRect\n                });\n                clone.fromRect = clonesFromRect;\n                clone.thisAnimationDuration = null;\n              });\n            }\n          } else {\n            activeSortable._showClone(sortable);\n          }\n        }\n      }\n    },\n    dragOverAnimationCapture: function dragOverAnimationCapture(_ref11) {\n      var dragRect = _ref11.dragRect,\n        isOwner = _ref11.isOwner,\n        activeSortable = _ref11.activeSortable;\n      multiDragElements.forEach(function (multiDragElement) {\n        multiDragElement.thisAnimationDuration = null;\n      });\n      if (activeSortable.options.animation && !isOwner && activeSortable.multiDrag.isMultiDrag) {\n        clonesFromRect = _extends({}, dragRect);\n        var dragMatrix = matrix(dragEl$1, true);\n        clonesFromRect.top -= dragMatrix.f;\n        clonesFromRect.left -= dragMatrix.e;\n      }\n    },\n    dragOverAnimationComplete: function dragOverAnimationComplete() {\n      if (folding) {\n        folding = false;\n        removeMultiDragElements();\n      }\n    },\n    drop: function drop(_ref12) {\n      var evt = _ref12.originalEvent,\n        rootEl = _ref12.rootEl,\n        parentEl = _ref12.parentEl,\n        sortable = _ref12.sortable,\n        dispatchSortableEvent = _ref12.dispatchSortableEvent,\n        oldIndex = _ref12.oldIndex,\n        putSortable = _ref12.putSortable;\n      var toSortable = putSortable || this.sortable;\n      if (!evt) return;\n      var options = this.options,\n        children = parentEl.children;\n\n      // Multi-drag selection\n      if (!dragStarted) {\n        if (options.multiDragKey && !this.multiDragKeyDown) {\n          this._deselectMultiDrag();\n        }\n        toggleClass(dragEl$1, options.selectedClass, !~multiDragElements.indexOf(dragEl$1));\n        if (!~multiDragElements.indexOf(dragEl$1)) {\n          multiDragElements.push(dragEl$1);\n          dispatchEvent({\n            sortable: sortable,\n            rootEl: rootEl,\n            name: 'select',\n            targetEl: dragEl$1,\n            originalEvent: evt\n          });\n\n          // Modifier activated, select from last to dragEl\n          if (evt.shiftKey && lastMultiDragSelect && sortable.el.contains(lastMultiDragSelect)) {\n            var lastIndex = index(lastMultiDragSelect),\n              currentIndex = index(dragEl$1);\n            if (~lastIndex && ~currentIndex && lastIndex !== currentIndex) {\n              (function () {\n                // Must include lastMultiDragSelect (select it), in case modified selection from no selection\n                // (but previous selection existed)\n                var n, i;\n                if (currentIndex > lastIndex) {\n                  i = lastIndex;\n                  n = currentIndex;\n                } else {\n                  i = currentIndex;\n                  n = lastIndex + 1;\n                }\n                var filter = options.filter;\n                for (; i < n; i++) {\n                  if (~multiDragElements.indexOf(children[i])) continue;\n                  // Check if element is draggable\n                  if (!closest(children[i], options.draggable, parentEl, false)) continue;\n                  // Check if element is filtered\n                  var filtered = filter && (typeof filter === 'function' ? filter.call(sortable, evt, children[i], sortable) : filter.split(',').some(function (criteria) {\n                    return closest(children[i], criteria.trim(), parentEl, false);\n                  }));\n                  if (filtered) continue;\n                  toggleClass(children[i], options.selectedClass, true);\n                  multiDragElements.push(children[i]);\n                  dispatchEvent({\n                    sortable: sortable,\n                    rootEl: rootEl,\n                    name: 'select',\n                    targetEl: children[i],\n                    originalEvent: evt\n                  });\n                }\n              })();\n            }\n          } else {\n            lastMultiDragSelect = dragEl$1;\n          }\n          multiDragSortable = toSortable;\n        } else {\n          multiDragElements.splice(multiDragElements.indexOf(dragEl$1), 1);\n          lastMultiDragSelect = null;\n          dispatchEvent({\n            sortable: sortable,\n            rootEl: rootEl,\n            name: 'deselect',\n            targetEl: dragEl$1,\n            originalEvent: evt\n          });\n        }\n      }\n\n      // Multi-drag drop\n      if (dragStarted && this.isMultiDrag) {\n        folding = false;\n        // Do not \"unfold\" after around dragEl if reverted\n        if ((parentEl[expando].options.sort || parentEl !== rootEl) && multiDragElements.length > 1) {\n          var dragRect = getRect(dragEl$1),\n            multiDragIndex = index(dragEl$1, ':not(.' + this.options.selectedClass + ')');\n          if (!initialFolding && options.animation) dragEl$1.thisAnimationDuration = null;\n          toSortable.captureAnimationState();\n          if (!initialFolding) {\n            if (options.animation) {\n              dragEl$1.fromRect = dragRect;\n              multiDragElements.forEach(function (multiDragElement) {\n                multiDragElement.thisAnimationDuration = null;\n                if (multiDragElement !== dragEl$1) {\n                  var rect = folding ? getRect(multiDragElement) : dragRect;\n                  multiDragElement.fromRect = rect;\n\n                  // Prepare unfold animation\n                  toSortable.addAnimationState({\n                    target: multiDragElement,\n                    rect: rect\n                  });\n                }\n              });\n            }\n\n            // Multi drag elements are not necessarily removed from the DOM on drop, so to reinsert\n            // properly they must all be removed\n            removeMultiDragElements();\n            multiDragElements.forEach(function (multiDragElement) {\n              if (children[multiDragIndex]) {\n                parentEl.insertBefore(multiDragElement, children[multiDragIndex]);\n              } else {\n                parentEl.appendChild(multiDragElement);\n              }\n              multiDragIndex++;\n            });\n\n            // If initial folding is done, the elements may have changed position because they are now\n            // unfolding around dragEl, even though dragEl may not have his index changed, so update event\n            // must be fired here as Sortable will not.\n            if (oldIndex === index(dragEl$1)) {\n              var update = false;\n              multiDragElements.forEach(function (multiDragElement) {\n                if (multiDragElement.sortableIndex !== index(multiDragElement)) {\n                  update = true;\n                  return;\n                }\n              });\n              if (update) {\n                dispatchSortableEvent('update');\n                dispatchSortableEvent('sort');\n              }\n            }\n          }\n\n          // Must be done after capturing individual rects (scroll bar)\n          multiDragElements.forEach(function (multiDragElement) {\n            unsetRect(multiDragElement);\n          });\n          toSortable.animateAll();\n        }\n        multiDragSortable = toSortable;\n      }\n\n      // Remove clones if necessary\n      if (rootEl === parentEl || putSortable && putSortable.lastPutMode !== 'clone') {\n        multiDragClones.forEach(function (clone) {\n          clone.parentNode && clone.parentNode.removeChild(clone);\n        });\n      }\n    },\n    nullingGlobal: function nullingGlobal() {\n      this.isMultiDrag = dragStarted = false;\n      multiDragClones.length = 0;\n    },\n    destroyGlobal: function destroyGlobal() {\n      this._deselectMultiDrag();\n      off(document, 'pointerup', this._deselectMultiDrag);\n      off(document, 'mouseup', this._deselectMultiDrag);\n      off(document, 'touchend', this._deselectMultiDrag);\n      off(document, 'keydown', this._checkKeyDown);\n      off(document, 'keyup', this._checkKeyUp);\n    },\n    _deselectMultiDrag: function _deselectMultiDrag(evt) {\n      if (typeof dragStarted !== \"undefined\" && dragStarted) return;\n\n      // Only deselect if selection is in this sortable\n      if (multiDragSortable !== this.sortable) return;\n\n      // Only deselect if target is not item in this sortable\n      if (evt && closest(evt.target, this.options.draggable, this.sortable.el, false)) return;\n\n      // Only deselect if left click\n      if (evt && evt.button !== 0) return;\n      while (multiDragElements.length) {\n        var el = multiDragElements[0];\n        toggleClass(el, this.options.selectedClass, false);\n        multiDragElements.shift();\n        dispatchEvent({\n          sortable: this.sortable,\n          rootEl: this.sortable.el,\n          name: 'deselect',\n          targetEl: el,\n          originalEvent: evt\n        });\n      }\n    },\n    _checkKeyDown: function _checkKeyDown(evt) {\n      if (evt.key === this.options.multiDragKey) {\n        this.multiDragKeyDown = true;\n      }\n    },\n    _checkKeyUp: function _checkKeyUp(evt) {\n      if (evt.key === this.options.multiDragKey) {\n        this.multiDragKeyDown = false;\n      }\n    }\n  };\n  return _extends(MultiDrag, {\n    // Static methods & properties\n    pluginName: 'multiDrag',\n    utils: {\n      /**\r\n       * Selects the provided multi-drag item\r\n       * @param  {HTMLElement} el    The element to be selected\r\n       */\n      select: function select(el) {\n        var sortable = el.parentNode[expando];\n        if (!sortable || !sortable.options.multiDrag || ~multiDragElements.indexOf(el)) return;\n        if (multiDragSortable && multiDragSortable !== sortable) {\n          multiDragSortable.multiDrag._deselectMultiDrag();\n          multiDragSortable = sortable;\n        }\n        toggleClass(el, sortable.options.selectedClass, true);\n        multiDragElements.push(el);\n      },\n      /**\r\n       * Deselects the provided multi-drag item\r\n       * @param  {HTMLElement} el    The element to be deselected\r\n       */\n      deselect: function deselect(el) {\n        var sortable = el.parentNode[expando],\n          index = multiDragElements.indexOf(el);\n        if (!sortable || !sortable.options.multiDrag || !~index) return;\n        toggleClass(el, sortable.options.selectedClass, false);\n        multiDragElements.splice(index, 1);\n      }\n    },\n    eventProperties: function eventProperties() {\n      var _this3 = this;\n      var oldIndicies = [],\n        newIndicies = [];\n      multiDragElements.forEach(function (multiDragElement) {\n        oldIndicies.push({\n          multiDragElement: multiDragElement,\n          index: multiDragElement.sortableIndex\n        });\n\n        // multiDragElements will already be sorted if folding\n        var newIndex;\n        if (folding && multiDragElement !== dragEl$1) {\n          newIndex = -1;\n        } else if (folding) {\n          newIndex = index(multiDragElement, ':not(.' + _this3.options.selectedClass + ')');\n        } else {\n          newIndex = index(multiDragElement);\n        }\n        newIndicies.push({\n          multiDragElement: multiDragElement,\n          index: newIndex\n        });\n      });\n      return {\n        items: _toConsumableArray(multiDragElements),\n        clones: [].concat(multiDragClones),\n        oldIndicies: oldIndicies,\n        newIndicies: newIndicies\n      };\n    },\n    optionListeners: {\n      multiDragKey: function multiDragKey(key) {\n        key = key.toLowerCase();\n        if (key === 'ctrl') {\n          key = 'Control';\n        } else if (key.length > 1) {\n          key = key.charAt(0).toUpperCase() + key.substr(1);\n        }\n        return key;\n      }\n    }\n  });\n}\nfunction insertMultiDragElements(clonesInserted, rootEl) {\n  multiDragElements.forEach(function (multiDragElement, i) {\n    var target = rootEl.children[multiDragElement.sortableIndex + (clonesInserted ? Number(i) : 0)];\n    if (target) {\n      rootEl.insertBefore(multiDragElement, target);\n    } else {\n      rootEl.appendChild(multiDragElement);\n    }\n  });\n}\n\n/**\r\n * Insert multi-drag clones\r\n * @param  {[Boolean]} elementsInserted  Whether the multi-drag elements are inserted\r\n * @param  {HTMLElement} rootEl\r\n */\nfunction insertMultiDragClones(elementsInserted, rootEl) {\n  multiDragClones.forEach(function (clone, i) {\n    var target = rootEl.children[clone.sortableIndex + (elementsInserted ? Number(i) : 0)];\n    if (target) {\n      rootEl.insertBefore(clone, target);\n    } else {\n      rootEl.appendChild(clone);\n    }\n  });\n}\nfunction removeMultiDragElements() {\n  multiDragElements.forEach(function (multiDragElement) {\n    if (multiDragElement === dragEl$1) return;\n    multiDragElement.parentNode && multiDragElement.parentNode.removeChild(multiDragElement);\n  });\n}\n\nSortable.mount(new AutoScrollPlugin());\nSortable.mount(Remove, Revert);\n\nexport default Sortable;\nexport { MultiDragPlugin as MultiDrag, Sortable, SwapPlugin as Swap };\n", "import ExtensionPage from 'flarum/components/ExtensionPage';\nimport Button from 'flarum/components/Button';\nimport LinksQueueAddModal from './LinksQueueAddModal.jsx';\nimport LinksQueueListItem from './LinksQueueListItem.jsx';\n\nimport Sortable from 'sortablejs';\n\nexport default class SettingsPage extends ExtensionPage {\n  oninit(attrs) {\n    super.oninit(attrs);\n    this.loading = false;\n    this.linksQueueList = [];\n    this.loadResults();\n  }\n\n  initSort(){\n    let el = document.getElementById('linksQueueSortableItems');\n    Sortable.create(el,{\n          animation: 150,\n          swapThreshold: 0.65,\n          onEnd: (e) => this.updateSort(e),\n        });\n  }\n\n  content() {\n    return (\n      <div className=\"ExtensionPage-settings FlarumBadgesPage\">\n        <div className=\"container\">\n\n          <div style={{paddingBottom: '10px'}}>\n            <Button className={'Button'} onclick={() => app.modal.show(LinksQueueAddModal)}>\n              {app.translator.trans('wusong8899-links-queue.admin.link-add')}\n            </Button>\n          </div>\n\n          <ul id=\"linksQueueSortableItems\" style={{padding: '0px', listStyleType: 'none'}} oncreate={this.initSort.bind(this)}>\n            {this.linksQueueList.map((LinksQueueItemData) => {\n              return (\n                <li itemID={LinksQueueItemData.id()} style={{marginTop: '5px', background: 'var(--body-bg)'}}>\n                  {LinksQueueListItem.component({ LinksQueueItemData })}\n                </li>\n              );\n            })}\n          </ul>\n\n        </div>\n      </div>\n    );\n  }\n\n  updateSort(e){\n    const newIndex = e.newIndex;\n    const oldIndex = e.oldIndex;\n\n    if(newIndex!==oldIndex){\n      const children = e.from.children;\n      const linkQueueOrder = {};\n\n      for(let i=0;i<children.length;i++){\n        const child = children[i];\n        const itemID = $(child).attr(\"itemID\");\n\n        linkQueueOrder[itemID] = i;\n      }\n\n      app.request({\n        url: `${app.forum.attribute('apiUrl')}/linksQueueList/order`,\n        method: 'POST',\n        body: { linkQueueOrder },\n      });\n    }\n  }\n\n  parseResults(results) {\n    [].push.apply(this.linksQueueList, results);\n    m.redraw();\n    return results;\n  }\n\n  loadResults() {\n    return app.store\n      .find(\"linksQueueList\")\n      .catch(() => {})\n      .then(this.parseResults.bind(this));\n  }\n}\n", "import Model from \"flarum/Model\";\n\nexport default class LinksQueue extends Model {}\nObject.assign(LinksQueue.prototype, {\n  id: Model.attribute(\"id\"),\n  name: Model.attribute(\"name\"),\n  links: Model.attribute(\"links\"),\n  sort: Model.attribute(\"sort\"),\n});\n", "import SettingsPage from './components/SettingsPage.jsx';\nimport LinksQueue from \"../forum/model/LinksQueue\";\n\napp.initializers.add('wusong8899-client1-links-queue', () => {\n  app.store.models.linksQueueList = LinksQueue;\n  app.extensionData.for('wusong8899-client1-links-queue').registerPage(SettingsPage);\n});\n"], "names": ["LinksQueueAddModal", "Modal", "vnode", "Stream", "<PERSON><PERSON>", "e", "response", "LinksQueueDeleteModal", "LinksQueueListItem", "Component", "LinksQueueItemData", "linkID", "linkName", "linkUrl", "ownKeys", "object", "enumerableOnly", "keys", "symbols", "sym", "_objectSpread2", "target", "i", "source", "key", "_defineProperty", "_typeof", "obj", "value", "_extends", "_objectWithoutPropertiesLoose", "excluded", "sourceKeys", "_objectWithoutProperties", "sourceSymbolKeys", "version", "userAgent", "pattern", "IE11OrLess", "Edge", "FireFox", "Safari", "IOS", "ChromeForAndroid", "captureMode", "on", "el", "event", "fn", "off", "matches", "selector", "getParentOrHost", "closest", "ctx", "includeCTX", "R_SPACE", "toggleClass", "name", "state", "className", "css", "prop", "val", "style", "matrix", "selfOnly", "appliedTransforms", "transform", "matrixFn", "find", "tagName", "iterator", "list", "n", "getWindowScrollingElement", "scrollingElement", "getRect", "relativeToContainingBlock", "relativeToNonStaticParent", "undoScale", "container", "elRect", "top", "left", "bottom", "right", "height", "width", "containerRect", "elMatrix", "scaleX", "scaleY", "isScrolledPast", "elSide", "parentSide", "parent", "getParentAutoScrollElement", "elSideVal", "parentSideVal", "visible", "<PERSON><PERSON><PERSON><PERSON>", "childNum", "options", "includeDragEl", "<PERSON><PERSON><PERSON><PERSON>", "children", "Sortable", "<PERSON><PERSON><PERSON><PERSON>", "last", "index", "getRelativeScrollOffset", "offsetLeft", "offsetTop", "winScroller", "indexOfObject", "arr", "includeSelf", "elem", "gotSelf", "elemCSS", "extend", "dst", "src", "isRectEqual", "rect1", "rect2", "_throttleTimeout", "throttle", "callback", "ms", "args", "_this", "cancelThrottle", "scrollBy", "x", "y", "clone", "Polymer", "$", "getChildContainingRectFromElement", "ghostEl", "rect", "child", "_rect$left", "_rect$top", "_rect$right", "_rect$bottom", "childRect", "expando", "AnimationStateManager", "animationStates", "animationCallbackId", "fromRect", "childMatrix", "animating", "animationTime", "time", "toRect", "prevFromRect", "prevToRect", "animatingRect", "targetMatrix", "calculateRealTime", "currentRect", "duration", "translateX", "translateY", "repaint", "plugins", "defaults", "Plugin<PERSON>anager", "plugin", "option", "p", "eventName", "sortable", "evt", "eventNameGlobal", "pluginName", "initialized", "modified", "eventProperties", "modifiedValue", "dispatchEvent", "_ref", "rootEl", "targetEl", "cloneEl", "toEl", "fromEl", "oldIndex", "newIndex", "oldDraggableIndex", "newDraggableIndex", "originalEvent", "putSortable", "extraEventProperties", "onName", "allEventProperties", "_excluded", "pluginEvent", "data", "dragEl", "parentEl", "nextEl", "lastDownEl", "cloneHidden", "moved", "_hideGhostForTarget", "_unhideGhostForTarget", "_dispatchEvent", "info", "activeGroup", "awaitingDragStarted", "ignoreNextClick", "sortables", "tapEvt", "touchEvt", "lastDx", "lastDy", "tapDistanceLeft", "tapDistanceTop", "last<PERSON><PERSON><PERSON>", "lastDirection", "pastFirstInvertThresh", "isCircumstantialInvert", "targetMoveDistance", "ghostRelativeParent", "ghostRelativeParentInitialScroll", "_silent", "savedInputChecked", "documentExists", "PositionGhostAbsolutely", "CSSFloatProperty", "supportDraggable", "supportCssPointerEvents", "_detectDirection", "elCSS", "<PERSON><PERSON><PERSON><PERSON>", "child1", "child2", "firstChildCSS", "secondChildCSS", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touchingSideChild2", "_dragElInRowColumn", "dragRect", "targetRect", "vertical", "dragElS1Opp", "dragElS2Opp", "dragElOppLength", "targetS1Opp", "targetS2Opp", "targetOppLength", "_detectNearestEmptySortable", "ret", "threshold", "insideHorizontally", "insideVertically", "_prepareGroup", "toFn", "pull", "to", "from", "sameGroup", "otherGroup", "group", "originalGroup", "nearestEmptyInsertDetectEvent", "nearest", "_checkOutsideTargetEl", "dataTransfer", "preventOnFilter", "type", "touch", "originalTarget", "filter", "_saveInputCheckedState", "criteria", "ownerDocument", "dragStartFn", "_disableDraggable", "_nextTick", "fallback", "inserted", "fallbackTolerance", "fallbackOffset", "ghostMatrix", "relativeScrollOffset", "dx", "dy", "cssMatrix", "revert", "activeSortable", "isOwner", "canSort", "fromSortable", "completedFired", "dragOverEvent", "extra", "completed", "after", "_onMove", "changed", "capture", "insertion", "el<PERSON>ast<PERSON><PERSON><PERSON>", "_ghostIsLast", "_ghost<PERSON>sFirst", "<PERSON><PERSON><PERSON><PERSON>", "direction", "targetBeforeFirstSwap", "differentLevel", "differentRowCol", "side1", "scrolledPastTop", "scrollBefore", "_getSwapDirection", "sibling", "dragIndex", "nextS<PERSON>ling", "moveVector", "_unsilent", "_cancelNextTick", "_globalDragOver", "order", "_generateId", "useAnimation", "items", "id", "store", "willInsertAfter", "onMoveFn", "retVal", "firstElRect", "childContainingRect", "spacer", "lastElRect", "swapThreshold", "invertedSwapThreshold", "invertSwap", "isLastTarget", "mouseOnAxis", "targetLength", "targetS1", "targetS2", "invert", "_getInsertDirection", "str", "sum", "root", "inputs", "idx", "element", "_len", "_key", "autoScrolls", "scrollEl", "scrollRootEl", "scrolling", "lastAutoScrollX", "lastAutoScrollY", "touchEvt$1", "pointerElemChangedInterval", "AutoScrollPlugin", "AutoScroll", "_ref2", "clearPointerElemChangedInterval", "clearAutoScrolls", "autoScroll", "ogElemScroller", "newElem", "<PERSON><PERSON><PERSON><PERSON>", "sens", "speed", "scrollThisInstance", "scrollCustomFn", "layersOut", "currentParent", "canScrollX", "canScrollY", "scrollWidth", "scrollHeight", "scrollPosX", "scrollPosY", "vx", "vy", "scrollOffsetY", "scrollOffsetX", "drop", "dispatchSortableEvent", "hideGhostForTarget", "unhideGhostForTarget", "toSortable", "<PERSON><PERSON>", "_ref3", "Remove", "_ref4", "parentSortable", "SettingsPage", "ExtensionPage", "attrs", "linkQueueOrder", "itemID", "results", "LinksQueue", "Model"], "mappings": "0CAIA,MAAqBA,WAA2BC,EAAM,CACpD,OAAO,cAAgB,GAEvB,OAAOC,EAAO,CACZ,MAAM,OAAOA,CAAK,EAClB,KAAK,mBAAqB,KAAK,MAAM,mBACrC,KAAK,YAAc,MAEhB,KAAK,oBACN,KAAK,YAAc,OACnB,KAAK,SAAWC,GAAO,KAAK,mBAAmB,MAAM,EACrD,KAAK,QAAUA,GAAO,KAAK,mBAAmB,OAAO,IAErD,KAAK,SAAWA,GAAO,EAAE,EACzB,KAAK,QAAUA,GAAO,EAAE,EAE5B,CAEA,WAAY,CACV,MAAO,eACT,CAEA,OAAQ,CACN,OAAO,KAAK,cAAc,MAAM,IAAI,WAAW,MAAM,gDAAgD,EAAE,IAAI,WAAW,MAAM,iDAAiD,CAC/K,CAEA,SAAU,CACR,OACE,EAAC,MAAA,CAAI,UAAU,gBACZ,MAAA,CAAI,UAAU,MAAA,EACb,EAAC,MAAA,CAAI,UAAU,aAAa,MAAM,uBAChC,EAAC,MAAA,KACC,EAAC,MAAA,CAAI,MAAM,uBAAA,EAAyB,IAAI,WAAW,MAAM,iDAAiD,CAAE,EAC5G,EAAC,QAAA,CAAM,UAAU,MAAM,SAAQ,GAAC,UAAU,cAAc,KAAM,KAAK,QAAA,CAAU,EAC7E,EAAC,MAAA,CAAI,MAAM,uBAAA,EAAyB,IAAI,WAAW,MAAM,gDAAgD,CAAE,EAC3G,EAAC,SAAM,UAAU,MAAM,SAAQ,GAAC,UAAU,cAAc,KAAM,KAAK,QAAS,CAC9E,CACF,EAEA,EAAC,MAAA,CAAI,UAAU,aAAa,MAAM,uBAC/BC,GAAO,UACN,CACE,UAAW,yBACX,KAAM,SACN,QAAS,KAAK,OAAA,EAEhB,KAAK,cAAc,MAAM,IAAI,WAAW,MAAM,uCAAuC,EAAE,IAAI,WAAW,MAAM,wCAAwC,CAAA,EACpJ,IACDA,GAAO,UACN,CACE,UAAW,8BACX,QAAS,KAAK,QACd,QAAS,IAAM,CACb,KAAK,KAAA,CACP,CAAA,EAEF,IAAI,WAAW,MAAM,qCAAqC,CAAA,CAE9D,CAEF,CACF,CAEJ,CAEA,SAASC,EAAG,CACVA,EAAE,eAAA,EAEF,KAAK,QAAU,GAEZ,KAAK,cAAc,OACpB,KAAK,mBAAmB,KAAK,CACzB,KAAK,KAAK,SAAA,EACV,IAAI,KAAK,QAAA,CAAQ,CACpB,EACA,KACC,IAAM,KAAK,KAAA,EACVC,GAAa,CACZ,KAAK,QAAU,GACf,KAAK,aAAaA,CAAQ,CAC5B,CAAA,EAGF,IAAI,MACD,aAAa,gBAAgB,EAC7B,KAAK,CACJ,KAAK,KAAK,SAAA,EACV,IAAI,KAAK,QAAA,CAAQ,CAClB,EACA,KACC,IAAM,CACJ,SAAS,OAAA,CACX,CAAA,EAED,MAAM,IAAM,CACX,KAAK,QAAU,GACf,KAAK,aAAA,CACP,CAAC,CAEP,CACF,CCrGA,MAAqBC,WAA8BN,EAAM,CACvD,OAAO,cAAgB,GAEvB,OAAOC,EAAO,CACZ,MAAM,OAAOA,CAAK,EAClB,KAAK,mBAAqB,KAAK,MAAM,mBACrC,KAAK,QAAU,EACjB,CAEA,WAAY,CACV,MAAO,cACT,CAEA,OAAQ,CACN,OAAO,IAAI,WAAW,MAAM,gEAAgE,CAC9F,CAEA,SAAU,CAGR,OACE,EAAC,MAAA,CAAI,UAAU,YAAA,EACb,EAAC,OAAI,UAAU,aAAa,MAAM,qBAAA,EAC/BE,GAAO,UACN,CACE,UAAW,yBACX,KAAM,SACN,QAAS,KAAK,OAAA,EAEhB,IAAI,WAAW,MAAM,sCAAsC,CAAA,EAC3D,IACDA,GAAO,UACN,CACE,UAAW,8BACX,QAAS,KAAK,QACd,QAAS,IAAM,CACb,KAAK,KAAA,CACP,CAAA,EAEF,IAAI,WAAW,MAAM,qCAAqC,CAAA,CAE9D,CACF,CAEJ,CAEA,SAASC,EAAG,CACVA,EAAE,eAAA,EAEF,KAAK,QAAU,GAEf,KAAK,mBAAmB,SACvB,KACC,IAAM,CACJ,SAAS,OAAA,CACX,CAAA,CAEJ,CACF,CCxDA,MAAqBG,WAA2BC,EAAU,CACxD,MAAO,CACL,KAAM,CAAC,mBAAAC,GAAsB,KAAK,MAC5BC,EAASD,EAAmB,GAAA,EAC5BE,EAAWF,EAAmB,KAAA,EAC9BG,EAAUH,EAAmB,MAAA,EAEnC,SACG,MAAA,CAAI,MAAM,2EAAA,EACT,EAAC,WACC,EAAC,MAAA,CAAI,MAAM,uBACRN,GAAA,CAAO,UAAW,yBAA0B,QAAS,IAAM,KAAK,SAASM,CAAkB,CAAA,EACzF,IAAI,WAAW,MAAM,iDAAiD,CACzE,EAAS,IAET,EAACN,GAAA,CAAO,MAAM,+BAA+B,UAAW,wBAAyB,QAAS,IAAM,KAAK,WAAWM,CAAkB,CAAA,EAC/H,IAAI,WAAW,MAAM,mDAAmD,CAC3E,EAAS,KAET,EAAC,SAAG,IAAI,WAAW,MAAM,+CAA+C,EAAE,IAAE,EAC3EC,EAAO,MACR,EAAC,SAAG,IAAI,WAAW,MAAM,iDAAiD,EAAE,IAAE,EAC7EC,EAAS,QACT,IAAA,KAAG,IAAI,WAAW,MAAM,gDAAgD,EAAE,IAAE,EAC5EC,EAAQ,GACX,CACF,CACF,CAEJ,CAEA,SAASH,EAAoB,CAC3B,IAAI,MAAM,KAAKV,GAAoB,CAAC,mBAAAU,EAAmB,CACzD,CAEA,WAAWA,EAAoB,CAC7B,IAAI,MAAM,KAAKH,GAAuB,CAAC,mBAAAG,EAAmB,CAC5D,CACF,CC3CA;AAAA;AAAA;AAAA;AAAA;AAAA,GAMA,SAASI,GAAQC,EAAQC,EAAgB,CACvC,IAAIC,EAAO,OAAO,KAAKF,CAAM,EAC7B,GAAI,OAAO,sBAAuB,CAChC,IAAIG,EAAU,OAAO,sBAAsBH,CAAM,EAC7CC,IACFE,EAAUA,EAAQ,OAAO,SAAUC,EAAK,CACtC,OAAO,OAAO,yBAAyBJ,EAAQI,CAAG,EAAE,UACtD,CAAC,GAEHF,EAAK,KAAK,MAAMA,EAAMC,CAAO,CAC/B,CACA,OAAOD,CACT,CACA,SAASG,EAAeC,EAAQ,CAC9B,QAASC,EAAI,EAAGA,EAAI,UAAU,OAAQA,IAAK,CACzC,IAAIC,EAAS,UAAUD,CAAC,GAAK,KAAO,UAAUA,CAAC,EAAI,CAAA,EAC/CA,EAAI,EACNR,GAAQ,OAAOS,CAAM,EAAG,EAAI,EAAE,QAAQ,SAAUC,EAAK,CACnDC,GAAgBJ,EAAQG,EAAKD,EAAOC,CAAG,CAAC,CAC1C,CAAC,EACQ,OAAO,0BAChB,OAAO,iBAAiBH,EAAQ,OAAO,0BAA0BE,CAAM,CAAC,EAExET,GAAQ,OAAOS,CAAM,CAAC,EAAE,QAAQ,SAAUC,EAAK,CAC7C,OAAO,eAAeH,EAAQG,EAAK,OAAO,yBAAyBD,EAAQC,CAAG,CAAC,CACjF,CAAC,CAEL,CACA,OAAOH,CACT,CACA,SAASK,GAAQC,EAAK,CACpB,0BAEA,OAAI,OAAO,QAAW,YAAc,OAAO,OAAO,UAAa,SAC7DD,GAAU,SAAUC,EAAK,CACvB,OAAO,OAAOA,CAChB,EAEAD,GAAU,SAAUC,EAAK,CACvB,OAAOA,GAAO,OAAO,QAAW,YAAcA,EAAI,cAAgB,QAAUA,IAAQ,OAAO,UAAY,SAAW,OAAOA,CAC3H,EAEKD,GAAQC,CAAG,CACpB,CACA,SAASF,GAAgBE,EAAKH,EAAKI,EAAO,CACxC,OAAIJ,KAAOG,EACT,OAAO,eAAeA,EAAKH,EAAK,CAC9B,MAAOI,EACP,WAAY,GACZ,aAAc,GACd,SAAU,EAChB,CAAK,EAEDD,EAAIH,CAAG,EAAII,EAEND,CACT,CACA,SAASE,GAAW,CAClB,OAAAA,EAAW,OAAO,QAAU,SAAUR,EAAQ,CAC5C,QAASC,EAAI,EAAGA,EAAI,UAAU,OAAQA,IAAK,CACzC,IAAIC,EAAS,UAAUD,CAAC,EACxB,QAASE,KAAOD,EACV,OAAO,UAAU,eAAe,KAAKA,EAAQC,CAAG,IAClDH,EAAOG,CAAG,EAAID,EAAOC,CAAG,EAG9B,CACA,OAAOH,CACT,EACOQ,EAAS,MAAM,KAAM,SAAS,CACvC,CACA,SAASC,GAA8BP,EAAQQ,EAAU,CACvD,GAAIR,GAAU,KAAM,MAAO,CAAA,EAC3B,IAAIF,EAAS,CAAA,EACTW,EAAa,OAAO,KAAKT,CAAM,EAC/BC,EAAKF,EACT,IAAKA,EAAI,EAAGA,EAAIU,EAAW,OAAQV,IACjCE,EAAMQ,EAAWV,CAAC,EACd,EAAAS,EAAS,QAAQP,CAAG,GAAK,KAC7BH,EAAOG,CAAG,EAAID,EAAOC,CAAG,GAE1B,OAAOH,CACT,CACA,SAASY,GAAyBV,EAAQQ,EAAU,CAClD,GAAIR,GAAU,KAAM,MAAO,CAAA,EAC3B,IAAIF,EAASS,GAA8BP,EAAQQ,CAAQ,EACvDP,EAAKF,EACT,GAAI,OAAO,sBAAuB,CAChC,IAAIY,EAAmB,OAAO,sBAAsBX,CAAM,EAC1D,IAAKD,EAAI,EAAGA,EAAIY,EAAiB,OAAQZ,IACvCE,EAAMU,EAAiBZ,CAAC,EACpB,EAAAS,EAAS,QAAQP,CAAG,GAAK,IACxB,OAAO,UAAU,qBAAqB,KAAKD,EAAQC,CAAG,IAC3DH,EAAOG,CAAG,EAAID,EAAOC,CAAG,EAE5B,CACA,OAAOH,CACT,CA2BA,IAAIc,GAAU,SAEd,SAASC,EAAUC,EAAS,CAC1B,GAAI,OAAO,OAAW,KAAe,OAAO,UAC1C,MAAO,CAAC,CAAe,UAAU,UAAU,MAAMA,CAAO,CAE5D,CACA,IAAIC,EAAaF,EAAU,uDAAuD,EAC9EG,GAAOH,EAAU,OAAO,EACxBI,GAAUJ,EAAU,UAAU,EAC9BK,GAASL,EAAU,SAAS,GAAK,CAACA,EAAU,SAAS,GAAK,CAACA,EAAU,UAAU,EAC/EM,GAAMN,EAAU,iBAAiB,EACjCO,GAAmBP,EAAU,SAAS,GAAKA,EAAU,UAAU,EAE/DQ,GAAc,CAChB,QAAS,GACT,QAAS,EACX,EACA,SAASC,EAAGC,EAAIC,EAAOC,EAAI,CACzBF,EAAG,iBAAiBC,EAAOC,EAAI,CAACV,GAAcM,EAAW,CAC3D,CACA,SAASK,EAAIH,EAAIC,EAAOC,EAAI,CAC1BF,EAAG,oBAAoBC,EAAOC,EAAI,CAACV,GAAcM,EAAW,CAC9D,CACA,SAASM,GAAyBJ,EAAeK,EAAU,CACzD,GAAKA,EAEL,IADAA,EAAS,CAAC,IAAM,MAAQA,EAAWA,EAAS,UAAU,CAAC,GACnDL,EACF,GAAI,CACF,GAAIA,EAAG,QACL,OAAOA,EAAG,QAAQK,CAAQ,EACrB,GAAIL,EAAG,kBACZ,OAAOA,EAAG,kBAAkBK,CAAQ,EAC/B,GAAIL,EAAG,sBACZ,OAAOA,EAAG,sBAAsBK,CAAQ,CAE5C,MAAY,CACV,MAAO,EACT,CAEF,MAAO,GACT,CACA,SAASC,GAAgBN,EAAI,CAC3B,OAAOA,EAAG,MAAQA,IAAO,UAAYA,EAAG,KAAK,SAAWA,EAAG,KAAOA,EAAG,UACvE,CACA,SAASO,EAAyBP,EAAeK,EAA0BG,EAAKC,EAAY,CAC1F,GAAIT,EAAI,CACNQ,EAAMA,GAAO,SACb,EAAG,CACD,GAAIH,GAAY,OAASA,EAAS,CAAC,IAAM,IAAML,EAAG,aAAeQ,GAAOJ,GAAQJ,EAAIK,CAAQ,EAAID,GAAQJ,EAAIK,CAAQ,IAAMI,GAAcT,IAAOQ,EAC7I,OAAOR,EAET,GAAIA,IAAOQ,EAAK,KAElB,OAASR,EAAKM,GAAgBN,CAAE,EAClC,CACA,OAAO,IACT,CACA,IAAIU,GAAU,OACd,SAASC,EAAYX,EAAIY,EAAMC,EAAO,CACpC,GAAIb,GAAMY,EACR,GAAIZ,EAAG,UACLA,EAAG,UAAUa,EAAQ,MAAQ,QAAQ,EAAED,CAAI,MACtC,CACL,IAAIE,GAAa,IAAMd,EAAG,UAAY,KAAK,QAAQU,GAAS,GAAG,EAAE,QAAQ,IAAME,EAAO,IAAK,GAAG,EAC9FZ,EAAG,WAAac,GAAaD,EAAQ,IAAMD,EAAO,KAAK,QAAQF,GAAS,GAAG,CAC7E,CAEJ,CACA,SAASK,EAAIf,EAAIgB,EAAMC,EAAK,CAC1B,IAAIC,EAAQlB,GAAMA,EAAG,MACrB,GAAIkB,EAAO,CACT,GAAID,IAAQ,OACV,OAAI,SAAS,aAAe,SAAS,YAAY,iBAC/CA,EAAM,SAAS,YAAY,iBAAiBjB,EAAI,EAAE,EACzCA,EAAG,eACZiB,EAAMjB,EAAG,cAEJgB,IAAS,OAASC,EAAMA,EAAID,CAAI,EAEnC,EAAEA,KAAQE,IAAUF,EAAK,QAAQ,QAAQ,IAAM,KACjDA,EAAO,WAAaA,GAEtBE,EAAMF,CAAI,EAAIC,GAAO,OAAOA,GAAQ,SAAW,GAAK,KAExD,CACF,CACA,SAASE,GAAOnB,EAAIoB,EAAU,CAC5B,IAAIC,EAAoB,GACxB,GAAI,OAAOrB,GAAO,SAChBqB,EAAoBrB,MAEpB,GAAG,CACD,IAAIsB,EAAYP,EAAIf,EAAI,WAAW,EAC/BsB,GAAaA,IAAc,SAC7BD,EAAoBC,EAAY,IAAMD,EAG1C,OAAS,CAACD,IAAapB,EAAKA,EAAG,aAEjC,IAAIuB,EAAW,OAAO,WAAa,OAAO,iBAAmB,OAAO,WAAa,OAAO,YAExF,OAAOA,GAAY,IAAIA,EAASF,CAAiB,CACnD,CACA,SAASG,GAAKhB,EAAKiB,EAASC,EAAU,CACpC,GAAIlB,EAAK,CACP,IAAImB,EAAOnB,EAAI,qBAAqBiB,CAAO,EACzCjD,EAAI,EACJoD,EAAID,EAAK,OACX,GAAID,EACF,KAAOlD,EAAIoD,EAAGpD,IACZkD,EAASC,EAAKnD,CAAC,EAAGA,CAAC,EAGvB,OAAOmD,CACT,CACA,MAAO,CAAA,CACT,CACA,SAASE,GAA4B,CACnC,IAAIC,EAAmB,SAAS,iBAChC,OAAIA,GAGK,SAAS,eAEpB,CAWA,SAASC,EAAQ/B,EAAIgC,EAA2BC,EAA2BC,EAAWC,EAAW,CAC/F,GAAI,GAACnC,EAAG,uBAAyBA,IAAO,QACxC,KAAIoC,EAAQC,EAAKC,EAAMC,EAAQC,EAAOC,EAAQC,EAiB9C,GAhBI1C,IAAO,QAAUA,EAAG,YAAcA,IAAO6B,KAC3CO,EAASpC,EAAG,sBAAqB,EACjCqC,EAAMD,EAAO,IACbE,EAAOF,EAAO,KACdG,EAASH,EAAO,OAChBI,EAAQJ,EAAO,MACfK,EAASL,EAAO,OAChBM,EAAQN,EAAO,QAEfC,EAAM,EACNC,EAAO,EACPC,EAAS,OAAO,YAChBC,EAAQ,OAAO,WACfC,EAAS,OAAO,YAChBC,EAAQ,OAAO,aAEZV,GAA6BC,IAA8BjC,IAAO,SAErEmC,EAAYA,GAAanC,EAAG,WAIxB,CAACR,GACH,EACE,IAAI2C,GAAaA,EAAU,wBAA0BpB,EAAIoB,EAAW,WAAW,IAAM,QAAUF,GAA6BlB,EAAIoB,EAAW,UAAU,IAAM,UAAW,CACpK,IAAIQ,EAAgBR,EAAU,sBAAqB,EAGnDE,GAAOM,EAAc,IAAM,SAAS5B,EAAIoB,EAAW,kBAAkB,CAAC,EACtEG,GAAQK,EAAc,KAAO,SAAS5B,EAAIoB,EAAW,mBAAmB,CAAC,EACzEI,EAASF,EAAMD,EAAO,OACtBI,EAAQF,EAAOF,EAAO,MACtB,KACF,OAEOD,EAAYA,EAAU,YAGnC,GAAID,GAAalC,IAAO,OAAQ,CAE9B,IAAI4C,EAAWzB,GAAOgB,GAAanC,CAAE,EACnC6C,EAASD,GAAYA,EAAS,EAC9BE,EAASF,GAAYA,EAAS,EAC5BA,IACFP,GAAOS,EACPR,GAAQO,EACRH,GAASG,EACTJ,GAAUK,EACVP,EAASF,EAAMI,EACfD,EAAQF,EAAOI,EAEnB,CACA,MAAO,CACL,IAAKL,EACL,KAAMC,EACN,OAAQC,EACR,MAAOC,EACP,MAAOE,EACP,OAAQD,CACZ,EACA,CASA,SAASM,GAAe/C,EAAIgD,EAAQC,EAAY,CAK9C,QAJIC,EAASC,EAA2BnD,EAAI,EAAI,EAC9CoD,EAAYrB,EAAQ/B,CAAE,EAAEgD,CAAM,EAGzBE,GAAQ,CACb,IAAIG,EAAgBtB,EAAQmB,CAAM,EAAED,CAAU,EAC5CK,EAAU,OAMZ,GAJEA,EAAUF,GAAaC,EAIrB,CAACC,EAAS,OAAOJ,EACrB,GAAIA,IAAWrB,IAA6B,MAC5CqB,EAASC,EAA2BD,EAAQ,EAAK,CACnD,CACA,MAAO,EACT,CAUA,SAASK,GAASvD,EAAIwD,EAAUC,EAASC,EAAe,CAItD,QAHIC,EAAe,EACjBnF,EAAI,EACJoF,EAAW5D,EAAG,SACTxB,EAAIoF,EAAS,QAAQ,CAC1B,GAAIA,EAASpF,CAAC,EAAE,MAAM,UAAY,QAAUoF,EAASpF,CAAC,IAAMqF,EAAS,QAAUH,GAAiBE,EAASpF,CAAC,IAAMqF,EAAS,UAAYtD,EAAQqD,EAASpF,CAAC,EAAGiF,EAAQ,UAAWzD,EAAI,EAAK,EAAG,CACvL,GAAI2D,IAAiBH,EACnB,OAAOI,EAASpF,CAAC,EAEnBmF,GACF,CACAnF,GACF,CACA,OAAO,IACT,CAQA,SAASsF,GAAU9D,EAAIK,EAAU,CAE/B,QADI0D,EAAO/D,EAAG,iBACP+D,IAASA,IAASF,EAAS,OAAS9C,EAAIgD,EAAM,SAAS,IAAM,QAAU1D,GAAY,CAACD,GAAQ2D,EAAM1D,CAAQ,IAC/G0D,EAAOA,EAAK,uBAEd,OAAOA,GAAQ,IACjB,CASA,SAASC,EAAMhE,EAAIK,EAAU,CAC3B,IAAI2D,EAAQ,EACZ,GAAI,CAAChE,GAAM,CAACA,EAAG,WACb,MAAO,GAIT,KAAOA,EAAKA,EAAG,wBACTA,EAAG,SAAS,YAAW,IAAO,YAAcA,IAAO6D,EAAS,QAAU,CAACxD,GAAYD,GAAQJ,EAAIK,CAAQ,IACzG2D,IAGJ,OAAOA,CACT,CAQA,SAASC,GAAwBjE,EAAI,CACnC,IAAIkE,EAAa,EACfC,EAAY,EACZC,EAAcvC,EAAyB,EACzC,GAAI7B,EACF,EAAG,CACD,IAAI4C,EAAWzB,GAAOnB,CAAE,EACtB6C,EAASD,EAAS,EAClBE,EAASF,EAAS,EACpBsB,GAAclE,EAAG,WAAa6C,EAC9BsB,GAAanE,EAAG,UAAY8C,CAC9B,OAAS9C,IAAOoE,IAAgBpE,EAAKA,EAAG,aAE1C,MAAO,CAACkE,EAAYC,CAAS,CAC/B,CAQA,SAASE,GAAcC,EAAKzF,EAAK,CAC/B,QAASL,KAAK8F,EACZ,GAAKA,EAAI,eAAe9F,CAAC,GACzB,QAASE,KAAOG,EACd,GAAIA,EAAI,eAAeH,CAAG,GAAKG,EAAIH,CAAG,IAAM4F,EAAI9F,CAAC,EAAEE,CAAG,EAAG,OAAO,OAAOF,CAAC,EAG5E,MAAO,EACT,CACA,SAAS2E,EAA2BnD,EAAIuE,EAAa,CAEnD,GAAI,CAACvE,GAAM,CAACA,EAAG,sBAAuB,OAAO6B,EAAyB,EACtE,IAAI2C,EAAOxE,EACPyE,EAAU,GACd,EAEE,IAAID,EAAK,YAAcA,EAAK,aAAeA,EAAK,aAAeA,EAAK,aAAc,CAChF,IAAIE,EAAU3D,EAAIyD,CAAI,EACtB,GAAIA,EAAK,YAAcA,EAAK,cAAgBE,EAAQ,WAAa,QAAUA,EAAQ,WAAa,WAAaF,EAAK,aAAeA,EAAK,eAAiBE,EAAQ,WAAa,QAAUA,EAAQ,WAAa,UAAW,CACpN,GAAI,CAACF,EAAK,uBAAyBA,IAAS,SAAS,KAAM,OAAO3C,EAAyB,EAC3F,GAAI4C,GAAWF,EAAa,OAAOC,EACnCC,EAAU,EACZ,CACF,OAEOD,EAAOA,EAAK,YACrB,OAAO3C,EAAyB,CAClC,CACA,SAAS8C,GAAOC,EAAKC,EAAK,CACxB,GAAID,GAAOC,EACT,QAASnG,KAAOmG,EACVA,EAAI,eAAenG,CAAG,IACxBkG,EAAIlG,CAAG,EAAImG,EAAInG,CAAG,GAIxB,OAAOkG,CACT,CACA,SAASE,GAAYC,EAAOC,EAAO,CACjC,OAAO,KAAK,MAAMD,EAAM,GAAG,IAAM,KAAK,MAAMC,EAAM,GAAG,GAAK,KAAK,MAAMD,EAAM,IAAI,IAAM,KAAK,MAAMC,EAAM,IAAI,GAAK,KAAK,MAAMD,EAAM,MAAM,IAAM,KAAK,MAAMC,EAAM,MAAM,GAAK,KAAK,MAAMD,EAAM,KAAK,IAAM,KAAK,MAAMC,EAAM,KAAK,CAC5N,CACA,IAAIC,GACJ,SAASC,GAASC,EAAUC,EAAI,CAC9B,OAAO,UAAY,CACjB,GAAI,CAACH,GAAkB,CACrB,IAAII,EAAO,UACTC,EAAQ,KACND,EAAK,SAAW,EAClBF,EAAS,KAAKG,EAAOD,EAAK,CAAC,CAAC,EAE5BF,EAAS,MAAMG,EAAOD,CAAI,EAE5BJ,GAAmB,WAAW,UAAY,CACxCA,GAAmB,MACrB,EAAGG,CAAE,CACP,CACF,CACF,CACA,SAASG,IAAiB,CACxB,aAAaN,EAAgB,EAC7BA,GAAmB,MACrB,CACA,SAASO,GAASxF,EAAIyF,EAAGC,EAAG,CAC1B1F,EAAG,YAAcyF,EACjBzF,EAAG,WAAa0F,CAClB,CACA,SAASC,GAAM3F,EAAI,CACjB,IAAI4F,EAAU,OAAO,QACjBC,EAAI,OAAO,QAAU,OAAO,MAChC,OAAID,GAAWA,EAAQ,IACdA,EAAQ,IAAI5F,CAAE,EAAE,UAAU,EAAI,EAC5B6F,EACFA,EAAE7F,CAAE,EAAE,MAAM,EAAI,EAAE,CAAC,EAEnBA,EAAG,UAAU,EAAI,CAE5B,CAeA,SAAS8F,GAAkC3D,EAAWsB,EAASsC,EAAS,CACtE,IAAIC,EAAO,CAAA,EACX,aAAM,KAAK7D,EAAU,QAAQ,EAAE,QAAQ,SAAU8D,EAAO,CACtD,IAAIC,EAAYC,EAAWC,EAAaC,EACxC,GAAI,GAAC9F,EAAQ0F,EAAOxC,EAAQ,UAAWtB,EAAW,EAAK,GAAK8D,EAAM,UAAYA,IAAUF,GACxF,KAAIO,EAAYvE,EAAQkE,CAAK,EAC7BD,EAAK,KAAO,KAAK,KAAKE,EAAaF,EAAK,QAAU,MAAQE,IAAe,OAASA,EAAa,IAAUI,EAAU,IAAI,EACvHN,EAAK,IAAM,KAAK,KAAKG,EAAYH,EAAK,OAAS,MAAQG,IAAc,OAASA,EAAY,IAAUG,EAAU,GAAG,EACjHN,EAAK,MAAQ,KAAK,KAAKI,EAAcJ,EAAK,SAAW,MAAQI,IAAgB,OAASA,EAAc,KAAWE,EAAU,KAAK,EAC9HN,EAAK,OAAS,KAAK,KAAKK,EAAeL,EAAK,UAAY,MAAQK,IAAiB,OAASA,EAAe,KAAWC,EAAU,MAAM,EACtI,CAAC,EACDN,EAAK,MAAQA,EAAK,MAAQA,EAAK,KAC/BA,EAAK,OAASA,EAAK,OAASA,EAAK,IACjCA,EAAK,EAAIA,EAAK,KACdA,EAAK,EAAIA,EAAK,IACPA,CACT,CACA,IAAIO,EAAU,WAAa,IAAI,KAAI,EAAG,QAAO,EAE7C,SAASC,IAAwB,CAC/B,IAAIC,EAAkB,CAAA,EACpBC,EACF,MAAO,CACL,sBAAuB,UAAiC,CAEtD,GADAD,EAAkB,CAAA,EACd,EAAC,KAAK,QAAQ,UAClB,KAAI7C,EAAW,CAAA,EAAG,MAAM,KAAK,KAAK,GAAG,QAAQ,EAC7CA,EAAS,QAAQ,SAAUqC,EAAO,CAChC,GAAI,EAAAlF,EAAIkF,EAAO,SAAS,IAAM,QAAUA,IAAUpC,EAAS,OAC3D,CAAA4C,EAAgB,KAAK,CACnB,OAAQR,EACR,KAAMlE,EAAQkE,CAAK,CAC7B,CAAS,EACD,IAAIU,EAAWrI,EAAe,GAAImI,EAAgBA,EAAgB,OAAS,CAAC,EAAE,IAAI,EAGlF,GAAIR,EAAM,sBAAuB,CAC/B,IAAIW,EAAczF,GAAO8E,EAAO,EAAI,EAChCW,IACFD,EAAS,KAAOC,EAAY,EAC5BD,EAAS,MAAQC,EAAY,EAEjC,CACAX,EAAM,SAAWU,EACnB,CAAC,EACH,EACA,kBAAmB,SAA2B9F,EAAO,CACnD4F,EAAgB,KAAK5F,CAAK,CAC5B,EACA,qBAAsB,SAA8BtC,EAAQ,CAC1DkI,EAAgB,OAAOpC,GAAcoC,EAAiB,CACpD,OAAQlI,CAChB,CAAO,EAAG,CAAC,CACP,EACA,WAAY,SAAoB4G,EAAU,CACxC,IAAIG,EAAQ,KACZ,GAAI,CAAC,KAAK,QAAQ,UAAW,CAC3B,aAAaoB,CAAmB,EAC5B,OAAOvB,GAAa,YAAYA,EAAQ,EAC5C,MACF,CACA,IAAI0B,EAAY,GACdC,EAAgB,EAClBL,EAAgB,QAAQ,SAAU5F,EAAO,CACvC,IAAIkG,EAAO,EACTxI,EAASsC,EAAM,OACf8F,EAAWpI,EAAO,SAClByI,EAASjF,EAAQxD,CAAM,EACvB0I,EAAe1I,EAAO,aACtB2I,EAAa3I,EAAO,WACpB4I,EAAgBtG,EAAM,KACtBuG,EAAejG,GAAO5C,EAAQ,EAAI,EAChC6I,IAEFJ,EAAO,KAAOI,EAAa,EAC3BJ,EAAO,MAAQI,EAAa,GAE9B7I,EAAO,OAASyI,EACZzI,EAAO,uBAELuG,GAAYmC,EAAcD,CAAM,GAAK,CAAClC,GAAY6B,EAAUK,CAAM,IAErEG,EAAc,IAAMH,EAAO,MAAQG,EAAc,KAAOH,EAAO,SAAWL,EAAS,IAAMK,EAAO,MAAQL,EAAS,KAAOK,EAAO,QAE9HD,EAAOM,GAAkBF,EAAeF,EAAcC,EAAY5B,EAAM,OAAO,GAK9ER,GAAYkC,EAAQL,CAAQ,IAC/BpI,EAAO,aAAeoI,EACtBpI,EAAO,WAAayI,EACfD,IACHA,EAAOzB,EAAM,QAAQ,WAEvBA,EAAM,QAAQ/G,EAAQ4I,EAAeH,EAAQD,CAAI,GAE/CA,IACFF,EAAY,GACZC,EAAgB,KAAK,IAAIA,EAAeC,CAAI,EAC5C,aAAaxI,EAAO,mBAAmB,EACvCA,EAAO,oBAAsB,WAAW,UAAY,CAClDA,EAAO,cAAgB,EACvBA,EAAO,aAAe,KACtBA,EAAO,SAAW,KAClBA,EAAO,WAAa,KACpBA,EAAO,sBAAwB,IACjC,EAAGwI,CAAI,EACPxI,EAAO,sBAAwBwI,EAEnC,CAAC,EACD,aAAaL,CAAmB,EAC3BG,EAGHH,EAAsB,WAAW,UAAY,CACvC,OAAOvB,GAAa,YAAYA,EAAQ,CAC9C,EAAG2B,CAAa,EAJZ,OAAO3B,GAAa,YAAYA,EAAQ,EAM9CsB,EAAkB,CAAA,CACpB,EACA,QAAS,SAAiBlI,EAAQ+I,EAAaN,EAAQO,EAAU,CAC/D,GAAIA,EAAU,CACZxG,EAAIxC,EAAQ,aAAc,EAAE,EAC5BwC,EAAIxC,EAAQ,YAAa,EAAE,EAC3B,IAAIqE,EAAWzB,GAAO,KAAK,EAAE,EAC3B0B,EAASD,GAAYA,EAAS,EAC9BE,EAASF,GAAYA,EAAS,EAC9B4E,GAAcF,EAAY,KAAON,EAAO,OAASnE,GAAU,GAC3D4E,GAAcH,EAAY,IAAMN,EAAO,MAAQlE,GAAU,GAC3DvE,EAAO,WAAa,CAAC,CAACiJ,EACtBjJ,EAAO,WAAa,CAAC,CAACkJ,EACtB1G,EAAIxC,EAAQ,YAAa,eAAiBiJ,EAAa,MAAQC,EAAa,OAAO,EACnF,KAAK,gBAAkBC,GAAQnJ,CAAM,EAErCwC,EAAIxC,EAAQ,aAAc,aAAegJ,EAAW,MAAQ,KAAK,QAAQ,OAAS,IAAM,KAAK,QAAQ,OAAS,GAAG,EACjHxG,EAAIxC,EAAQ,YAAa,oBAAoB,EAC7C,OAAOA,EAAO,UAAa,UAAY,aAAaA,EAAO,QAAQ,EACnEA,EAAO,SAAW,WAAW,UAAY,CACvCwC,EAAIxC,EAAQ,aAAc,EAAE,EAC5BwC,EAAIxC,EAAQ,YAAa,EAAE,EAC3BA,EAAO,SAAW,GAClBA,EAAO,WAAa,GACpBA,EAAO,WAAa,EACtB,EAAGgJ,CAAQ,CACb,CACF,CACJ,CACA,CACA,SAASG,GAAQnJ,EAAQ,CACvB,OAAOA,EAAO,WAChB,CACA,SAAS8I,GAAkBF,EAAeR,EAAUK,EAAQvD,EAAS,CACnE,OAAO,KAAK,KAAK,KAAK,IAAIkD,EAAS,IAAMQ,EAAc,IAAK,CAAC,EAAI,KAAK,IAAIR,EAAS,KAAOQ,EAAc,KAAM,CAAC,CAAC,EAAI,KAAK,KAAK,KAAK,IAAIR,EAAS,IAAMK,EAAO,IAAK,CAAC,EAAI,KAAK,IAAIL,EAAS,KAAOK,EAAO,KAAM,CAAC,CAAC,EAAIvD,EAAQ,SAC7N,CAEA,IAAIkE,GAAU,CAAA,EACVC,GAAW,CACb,oBAAqB,EACvB,EACIC,GAAgB,CAClB,MAAO,SAAeC,EAAQ,CAE5B,QAASC,KAAUH,GACbA,GAAS,eAAeG,CAAM,GAAK,EAAEA,KAAUD,KACjDA,EAAOC,CAAM,EAAIH,GAASG,CAAM,GAGpCJ,GAAQ,QAAQ,SAAUK,EAAG,CAC3B,GAAIA,EAAE,aAAeF,EAAO,WAC1B,KAAM,iCAAiC,OAAOA,EAAO,WAAY,iBAAiB,CAEtF,CAAC,EACDH,GAAQ,KAAKG,CAAM,CACrB,EACA,YAAa,SAAqBG,EAAWC,EAAUC,EAAK,CAC1D,IAAI7C,EAAQ,KACZ,KAAK,cAAgB,GACrB6C,EAAI,OAAS,UAAY,CACvB7C,EAAM,cAAgB,EACxB,EACA,IAAI8C,EAAkBH,EAAY,SAClCN,GAAQ,QAAQ,SAAUG,EAAQ,CAC3BI,EAASJ,EAAO,UAAU,IAE3BI,EAASJ,EAAO,UAAU,EAAEM,CAAe,GAC7CF,EAASJ,EAAO,UAAU,EAAEM,CAAe,EAAE9J,EAAe,CAC1D,SAAU4J,CACpB,EAAWC,CAAG,CAAC,EAKLD,EAAS,QAAQJ,EAAO,UAAU,GAAKI,EAASJ,EAAO,UAAU,EAAEG,CAAS,GAC9EC,EAASJ,EAAO,UAAU,EAAEG,CAAS,EAAE3J,EAAe,CACpD,SAAU4J,CACpB,EAAWC,CAAG,CAAC,EAEX,CAAC,CACH,EACA,kBAAmB,SAA2BD,EAAUlI,EAAI4H,EAAUnE,EAAS,CAC7EkE,GAAQ,QAAQ,SAAUG,EAAQ,CAChC,IAAIO,EAAaP,EAAO,WACxB,GAAI,GAACI,EAAS,QAAQG,CAAU,GAAK,CAACP,EAAO,qBAC7C,KAAIQ,EAAc,IAAIR,EAAOI,EAAUlI,EAAIkI,EAAS,OAAO,EAC3DI,EAAY,SAAWJ,EACvBI,EAAY,QAAUJ,EAAS,QAC/BA,EAASG,CAAU,EAAIC,EAGvBvJ,EAAS6I,EAAUU,EAAY,QAAQ,EACzC,CAAC,EACD,QAASP,KAAUG,EAAS,QAC1B,GAAKA,EAAS,QAAQ,eAAeH,CAAM,EAC3C,KAAIQ,EAAW,KAAK,aAAaL,EAAUH,EAAQG,EAAS,QAAQH,CAAM,CAAC,EACvE,OAAOQ,EAAa,MACtBL,EAAS,QAAQH,CAAM,EAAIQ,GAGjC,EACA,mBAAoB,SAA4B3H,EAAMsH,EAAU,CAC9D,IAAIM,EAAkB,CAAA,EACtB,OAAAb,GAAQ,QAAQ,SAAUG,EAAQ,CAC5B,OAAOA,EAAO,iBAAoB,YACtC/I,EAASyJ,EAAiBV,EAAO,gBAAgB,KAAKI,EAASJ,EAAO,UAAU,EAAGlH,CAAI,CAAC,CAC1F,CAAC,EACM4H,CACT,EACA,aAAc,SAAsBN,EAAUtH,EAAM9B,EAAO,CACzD,IAAI2J,EACJ,OAAAd,GAAQ,QAAQ,SAAUG,EAAQ,CAE3BI,EAASJ,EAAO,UAAU,GAG3BA,EAAO,iBAAmB,OAAOA,EAAO,gBAAgBlH,CAAI,GAAM,aACpE6H,EAAgBX,EAAO,gBAAgBlH,CAAI,EAAE,KAAKsH,EAASJ,EAAO,UAAU,EAAGhJ,CAAK,EAExF,CAAC,EACM2J,CACT,CACF,EAEA,SAASC,GAAcC,EAAM,CAC3B,IAAIT,EAAWS,EAAK,SAClBC,EAASD,EAAK,OACd/H,EAAO+H,EAAK,KACZE,EAAWF,EAAK,SAChBG,EAAUH,EAAK,QACfI,EAAOJ,EAAK,KACZK,EAASL,EAAK,OACdM,EAAWN,EAAK,SAChBO,EAAWP,EAAK,SAChBQ,EAAoBR,EAAK,kBACzBS,EAAoBT,EAAK,kBACzBU,EAAgBV,EAAK,cACrBW,EAAcX,EAAK,YACnBY,EAAuBZ,EAAK,qBAE9B,GADAT,EAAWA,GAAYU,GAAUA,EAAOrC,CAAO,EAC3C,EAAC2B,EACL,KAAIC,EACF1E,EAAUyE,EAAS,QACnBsB,EAAS,KAAO5I,EAAK,OAAO,CAAC,EAAE,cAAgBA,EAAK,OAAO,CAAC,EAE1D,OAAO,aAAe,CAACpB,GAAc,CAACC,GACxC0I,EAAM,IAAI,YAAYvH,EAAM,CAC1B,QAAS,GACT,WAAY,EAClB,CAAK,GAEDuH,EAAM,SAAS,YAAY,OAAO,EAClCA,EAAI,UAAUvH,EAAM,GAAM,EAAI,GAEhCuH,EAAI,GAAKY,GAAQH,EACjBT,EAAI,KAAOa,GAAUJ,EACrBT,EAAI,KAAOU,GAAYD,EACvBT,EAAI,MAAQW,EACZX,EAAI,SAAWc,EACfd,EAAI,SAAWe,EACff,EAAI,kBAAoBgB,EACxBhB,EAAI,kBAAoBiB,EACxBjB,EAAI,cAAgBkB,EACpBlB,EAAI,SAAWmB,EAAcA,EAAY,YAAc,OACvD,IAAIG,EAAqBnL,EAAeA,EAAe,GAAIiL,CAAoB,EAAG1B,GAAc,mBAAmBjH,EAAMsH,CAAQ,CAAC,EAClI,QAASH,KAAU0B,EACjBtB,EAAIJ,CAAM,EAAI0B,EAAmB1B,CAAM,EAErCa,GACFA,EAAO,cAAcT,CAAG,EAEtB1E,EAAQ+F,CAAM,GAChB/F,EAAQ+F,CAAM,EAAE,KAAKtB,EAAUC,CAAG,EAEtC,CAEA,IAAIuB,GAAY,CAAC,KAAK,EAClBC,EAAc,SAAqB1B,EAAWC,EAAU,CAC1D,IAAIS,EAAO,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAA,EAC7EU,EAAgBV,EAAK,IACrBiB,EAAOzK,GAAyBwJ,EAAMe,EAAS,EACjD7B,GAAc,YAAY,KAAKhE,CAAQ,EAAEoE,EAAWC,EAAU5J,EAAe,CAC3E,OAAQuL,EACR,SAAUC,EACV,QAAS/D,EACT,OAAQ6C,EACR,OAAQmB,GACR,WAAYC,GACZ,QAASlB,EACT,YAAamB,EACb,YAAaC,GACb,YAAaZ,EACb,eAAgBzF,EAAS,OACzB,cAAewF,EACf,SAAUJ,GACV,kBAAmBE,GACnB,SAAUD,EACV,kBAAmBE,GACnB,mBAAoBe,GACpB,qBAAsBC,GACtB,eAAgB,UAA0B,CACxCH,EAAc,EAChB,EACA,cAAe,UAAyB,CACtCA,EAAc,EAChB,EACA,sBAAuB,SAA+BrJ,EAAM,CAC1DyJ,EAAe,CACb,SAAUnC,EACV,KAAMtH,EACN,cAAeyI,CACvB,CAAO,CACH,CACJ,EAAKO,CAAI,CAAC,CACV,EACA,SAASS,EAAeC,EAAM,CAC5B5B,GAAcpK,EAAe,CAC3B,YAAagL,EACb,QAASR,EACT,SAAUe,EACV,OAAQjB,EACR,SAAUK,GACV,kBAAmBE,GACnB,SAAUD,EACV,kBAAmBE,EACvB,EAAKkB,CAAI,CAAC,CACV,CACA,IAAIT,EACFC,EACA/D,EACA6C,EACAmB,GACAC,GACAlB,EACAmB,EACAhB,GACAC,EACAC,GACAC,GACAmB,GACAjB,EACAkB,GAAsB,GACtBC,GAAkB,GAClBC,GAAY,CAAA,EACZC,GACAC,EACAC,GACAC,GACAC,GACAC,GACAd,GACAe,GACAC,GACAC,GAAwB,GACxBC,GAAyB,GACzBC,GAEAC,EACAC,GAAmC,CAAA,EAGnCC,GAAU,GACVC,GAAoB,CAAA,EAGlBC,GAAiB,OAAO,SAAa,IACvCC,GAA0B/L,GAC1BgM,GAAmBnM,IAAQD,EAAa,WAAa,QAErDqM,GAAmBH,IAAkB,CAAC7L,IAAoB,CAACD,IAAO,cAAe,SAAS,cAAc,KAAK,EAC7GkM,IAA0B,UAAY,CACpC,GAAKJ,GAEL,IAAIlM,EACF,MAAO,GAET,IAAIQ,EAAK,SAAS,cAAc,GAAG,EACnC,OAAAA,EAAG,MAAM,QAAU,sBACZA,EAAG,MAAM,gBAAkB,OACpC,GAAC,EACD+L,GAAmB,SAA0B/L,EAAIyD,EAAS,CACxD,IAAIuI,EAAQjL,EAAIf,CAAE,EAChBiM,EAAU,SAASD,EAAM,KAAK,EAAI,SAASA,EAAM,WAAW,EAAI,SAASA,EAAM,YAAY,EAAI,SAASA,EAAM,eAAe,EAAI,SAASA,EAAM,gBAAgB,EAChKE,EAAS3I,GAASvD,EAAI,EAAGyD,CAAO,EAChC0I,EAAS5I,GAASvD,EAAI,EAAGyD,CAAO,EAChC2I,EAAgBF,GAAUnL,EAAImL,CAAM,EACpCG,EAAiBF,GAAUpL,EAAIoL,CAAM,EACrCG,EAAkBF,GAAiB,SAASA,EAAc,UAAU,EAAI,SAASA,EAAc,WAAW,EAAIrK,EAAQmK,CAAM,EAAE,MAC9HK,EAAmBF,GAAkB,SAASA,EAAe,UAAU,EAAI,SAASA,EAAe,WAAW,EAAItK,EAAQoK,CAAM,EAAE,MACpI,GAAIH,EAAM,UAAY,OACpB,OAAOA,EAAM,gBAAkB,UAAYA,EAAM,gBAAkB,iBAAmB,WAAa,aAErG,GAAIA,EAAM,UAAY,OACpB,OAAOA,EAAM,oBAAoB,MAAM,GAAG,EAAE,QAAU,EAAI,WAAa,aAEzE,GAAIE,GAAUE,EAAc,OAAYA,EAAc,QAAa,OAAQ,CACzE,IAAII,EAAqBJ,EAAc,QAAa,OAAS,OAAS,QACtE,OAAOD,IAAWE,EAAe,QAAU,QAAUA,EAAe,QAAUG,GAAsB,WAAa,YACnH,CACA,OAAON,IAAWE,EAAc,UAAY,SAAWA,EAAc,UAAY,QAAUA,EAAc,UAAY,SAAWA,EAAc,UAAY,QAAUE,GAAmBL,GAAWD,EAAMJ,EAAgB,IAAM,QAAUO,GAAUH,EAAMJ,EAAgB,IAAM,QAAUU,EAAkBC,EAAmBN,GAAW,WAAa,YACvV,EACAQ,GAAqB,SAA4BC,EAAUC,EAAYC,EAAU,CAC/E,IAAIC,EAAcD,EAAWF,EAAS,KAAOA,EAAS,IACpDI,EAAcF,EAAWF,EAAS,MAAQA,EAAS,OACnDK,EAAkBH,EAAWF,EAAS,MAAQA,EAAS,OACvDM,EAAcJ,EAAWD,EAAW,KAAOA,EAAW,IACtDM,EAAcL,EAAWD,EAAW,MAAQA,EAAW,OACvDO,EAAkBN,EAAWD,EAAW,MAAQA,EAAW,OAC7D,OAAOE,IAAgBG,GAAeF,IAAgBG,GAAeJ,EAAcE,EAAkB,IAAMC,EAAcE,EAAkB,CAC7I,EAOAC,GAA8B,SAAqC1H,EAAGC,EAAG,CACvE,IAAI0H,EACJ,OAAA1C,GAAU,KAAK,SAAUxC,EAAU,CACjC,IAAImF,EAAYnF,EAAS3B,CAAO,EAAE,QAAQ,qBAC1C,GAAI,GAAC8G,GAAavJ,GAAUoE,CAAQ,GACpC,KAAIlC,EAAOjE,EAAQmG,CAAQ,EACzBoF,EAAqB7H,GAAKO,EAAK,KAAOqH,GAAa5H,GAAKO,EAAK,MAAQqH,EACrEE,EAAmB7H,GAAKM,EAAK,IAAMqH,GAAa3H,GAAKM,EAAK,OAASqH,EACrE,GAAIC,GAAsBC,EACxB,OAAOH,EAAMlF,EAEjB,CAAC,EACMkF,CACT,EACAI,GAAgB,SAAuB/J,EAAS,CAC9C,SAASgK,EAAK3O,EAAO4O,EAAM,CACzB,OAAO,SAAUC,EAAIC,EAAM/D,EAAQ1B,EAAK,CACtC,IAAI0F,EAAYF,EAAG,QAAQ,MAAM,MAAQC,EAAK,QAAQ,MAAM,MAAQD,EAAG,QAAQ,MAAM,OAASC,EAAK,QAAQ,MAAM,KACjH,GAAI9O,GAAS,OAAS4O,GAAQG,GAG5B,MAAO,GACF,GAAI/O,GAAS,MAAQA,IAAU,GACpC,MAAO,GACF,GAAI4O,GAAQ5O,IAAU,QAC3B,OAAOA,EACF,GAAI,OAAOA,GAAU,WAC1B,OAAO2O,EAAK3O,EAAM6O,EAAIC,EAAM/D,EAAQ1B,CAAG,EAAGuF,CAAI,EAAEC,EAAIC,EAAM/D,EAAQ1B,CAAG,EAErE,IAAI2F,GAAcJ,EAAOC,EAAKC,GAAM,QAAQ,MAAM,KAClD,OAAO9O,IAAU,IAAQ,OAAOA,GAAU,UAAYA,IAAUgP,GAAchP,EAAM,MAAQA,EAAM,QAAQgP,CAAU,EAAI,EAE5H,CACF,CACA,IAAIC,EAAQ,CAAA,EACRC,EAAgBvK,EAAQ,OACxB,CAACuK,GAAiBpP,GAAQoP,CAAa,GAAK,YAC9CA,EAAgB,CACd,KAAMA,CACd,GAEID,EAAM,KAAOC,EAAc,KAC3BD,EAAM,UAAYN,EAAKO,EAAc,KAAM,EAAI,EAC/CD,EAAM,SAAWN,EAAKO,EAAc,GAAG,EACvCD,EAAM,YAAcC,EAAc,YAClCvK,EAAQ,MAAQsK,CAClB,EACA5D,GAAsB,UAA+B,CAC/C,CAAC2B,IAA2B/F,GAC9BhF,EAAIgF,EAAS,UAAW,MAAM,CAElC,EACAqE,GAAwB,UAAiC,CACnD,CAAC0B,IAA2B/F,GAC9BhF,EAAIgF,EAAS,UAAW,EAAE,CAE9B,EAGE2F,IAAkB,CAAC7L,IACrB,SAAS,iBAAiB,QAAS,SAAUsI,EAAK,CAChD,GAAIsC,GACF,OAAAtC,EAAI,eAAc,EAClBA,EAAI,iBAAmBA,EAAI,gBAAe,EAC1CA,EAAI,0BAA4BA,EAAI,yBAAwB,EAC5DsC,GAAkB,GACX,EAEX,EAAG,EAAI,EAET,IAAIwD,GAAgC,SAAuC9F,EAAK,CAC9E,GAAI0B,EAAQ,CACV1B,EAAMA,EAAI,QAAUA,EAAI,QAAQ,CAAC,EAAIA,EACrC,IAAI+F,EAAUf,GAA4BhF,EAAI,QAASA,EAAI,OAAO,EAClE,GAAI+F,EAAS,CAEX,IAAIjO,EAAQ,CAAA,EACZ,QAASzB,KAAK2J,EACRA,EAAI,eAAe3J,CAAC,IACtByB,EAAMzB,CAAC,EAAI2J,EAAI3J,CAAC,GAGpByB,EAAM,OAASA,EAAM,OAASiO,EAC9BjO,EAAM,eAAiB,OACvBA,EAAM,gBAAkB,OACxBiO,EAAQ3H,CAAO,EAAE,YAAYtG,CAAK,CACpC,CACF,CACF,EACIkO,GAAwB,SAA+BhG,EAAK,CAC1D0B,GACFA,EAAO,WAAWtD,CAAO,EAAE,iBAAiB4B,EAAI,MAAM,CAE1D,EAOA,SAAStE,EAAS7D,EAAIyD,EAAS,CAC7B,GAAI,EAAEzD,GAAMA,EAAG,UAAYA,EAAG,WAAa,GACzC,KAAM,8CAA8C,OAAO,CAAA,EAAG,SAAS,KAAKA,CAAE,CAAC,EAEjF,KAAK,GAAKA,EACV,KAAK,QAAUyD,EAAU1E,EAAS,CAAA,EAAI0E,CAAO,EAG7CzD,EAAGuG,CAAO,EAAI,KACd,IAAIqB,EAAW,CACb,MAAO,KACP,KAAM,GACN,SAAU,GACV,MAAO,KACP,OAAQ,KACR,UAAW,WAAW,KAAK5H,EAAG,QAAQ,EAAI,MAAQ,KAClD,cAAe,EAEf,WAAY,GAEZ,sBAAuB,KAEvB,kBAAmB,GACnB,UAAW,UAAqB,CAC9B,OAAO+L,GAAiB/L,EAAI,KAAK,OAAO,CAC1C,EACA,WAAY,iBACZ,YAAa,kBACb,UAAW,gBACX,OAAQ,SACR,OAAQ,KACR,gBAAiB,GACjB,UAAW,EACX,OAAQ,KACR,QAAS,SAAiBoO,EAAcvE,EAAQ,CAC9CuE,EAAa,QAAQ,OAAQvE,EAAO,WAAW,CACjD,EACA,WAAY,GACZ,eAAgB,GAChB,WAAY,UACZ,MAAO,EACP,iBAAkB,GAClB,qBAAsB,OAAO,SAAW,OAAS,QAAQ,SAAS,OAAO,iBAAkB,EAAE,GAAK,EAClG,cAAe,GACf,cAAe,oBACf,eAAgB,GAChB,kBAAmB,EACnB,eAAgB,CACd,EAAG,EACH,EAAG,CACT,EAEI,eAAgBhG,EAAS,iBAAmB,IAAS,iBAAkB,SAAW,CAAClE,IAAUC,IAC7F,qBAAsB,CAC1B,EACEiI,GAAc,kBAAkB,KAAM7H,EAAI4H,CAAQ,EAGlD,QAAShH,KAAQgH,EACf,EAAEhH,KAAQ6C,KAAaA,EAAQ7C,CAAI,EAAIgH,EAAShH,CAAI,GAEtD4M,GAAc/J,CAAO,EAGrB,QAASvD,KAAM,KACTA,EAAG,OAAO,CAAC,IAAM,KAAO,OAAO,KAAKA,CAAE,GAAM,aAC9C,KAAKA,CAAE,EAAI,KAAKA,CAAE,EAAE,KAAK,IAAI,GAKjC,KAAK,gBAAkBuD,EAAQ,cAAgB,GAAQoI,GACnD,KAAK,kBAEP,KAAK,QAAQ,oBAAsB,GAIjCpI,EAAQ,eACV1D,EAAGC,EAAI,cAAe,KAAK,WAAW,GAEtCD,EAAGC,EAAI,YAAa,KAAK,WAAW,EACpCD,EAAGC,EAAI,aAAc,KAAK,WAAW,GAEnC,KAAK,kBACPD,EAAGC,EAAI,WAAY,IAAI,EACvBD,EAAGC,EAAI,YAAa,IAAI,GAE1B0K,GAAU,KAAK,KAAK,EAAE,EAGtBjH,EAAQ,OAASA,EAAQ,MAAM,KAAO,KAAK,KAAKA,EAAQ,MAAM,IAAI,IAAI,GAAK,CAAA,CAAE,EAG7E1E,EAAS,KAAMyH,IAAuB,CACxC,CACA3C,EAAS,UAA4C,CACnD,YAAaA,EACb,iBAAkB,SAA0BtF,EAAQ,CAC9C,CAAC,KAAK,GAAG,SAASA,CAAM,GAAKA,IAAW,KAAK,KAC/C0M,GAAa,KAEjB,EACA,cAAe,SAAuB9C,EAAK5J,EAAQ,CACjD,OAAO,OAAO,KAAK,QAAQ,WAAc,WAAa,KAAK,QAAQ,UAAU,KAAK,KAAM4J,EAAK5J,EAAQsL,CAAM,EAAI,KAAK,QAAQ,SAC9H,EACA,YAAa,SAA6C1B,EAAK,CAC7D,GAAKA,EAAI,WACT,KAAI7C,EAAQ,KACVtF,EAAK,KAAK,GACVyD,EAAU,KAAK,QACf4K,EAAkB5K,EAAQ,gBAC1B6K,EAAOnG,EAAI,KACXoG,EAAQpG,EAAI,SAAWA,EAAI,QAAQ,CAAC,GAAKA,EAAI,aAAeA,EAAI,cAAgB,SAAWA,EAC3F5J,GAAUgQ,GAASpG,GAAK,OACxBqG,EAAiBrG,EAAI,OAAO,aAAeA,EAAI,MAAQA,EAAI,KAAK,CAAC,GAAKA,EAAI,cAAgBA,EAAI,aAAY,EAAG,CAAC,IAAM5J,EACpHkQ,EAAShL,EAAQ,OAInB,GAHAiL,GAAuB1O,CAAE,EAGrB,CAAA6J,GAGA,0BAAwB,KAAKyE,CAAI,GAAKnG,EAAI,SAAW,GAAK1E,EAAQ,WAKlE,CAAA+K,EAAe,mBAKf,GAAC,KAAK,iBAAmB7O,IAAUpB,GAAUA,EAAO,QAAQ,YAAW,IAAO,YAGlFA,EAASgC,EAAQhC,EAAQkF,EAAQ,UAAWzD,EAAI,EAAK,EACjD,EAAAzB,GAAUA,EAAO,WAGjByL,KAAezL,GAUnB,IAJA0K,GAAWjF,EAAMzF,CAAM,EACvB4K,GAAoBnF,EAAMzF,EAAQkF,EAAQ,SAAS,EAG/C,OAAOgL,GAAW,YACpB,GAAIA,EAAO,KAAK,KAAMtG,EAAK5J,EAAQ,IAAI,EAAG,CACxC8L,EAAe,CACb,SAAU/E,EACV,OAAQkJ,EACR,KAAM,SACN,SAAUjQ,EACV,KAAMyB,EACN,OAAQA,CAClB,CAAS,EACD2J,EAAY,SAAUrE,EAAO,CAC3B,IAAK6C,CACf,CAAS,EACDkG,GAAmBlG,EAAI,eAAc,EACrC,MACF,UACSsG,IACTA,EAASA,EAAO,MAAM,GAAG,EAAE,KAAK,SAAUE,EAAU,CAElD,GADAA,EAAWpO,EAAQiO,EAAgBG,EAAS,KAAI,EAAI3O,EAAI,EAAK,EACzD2O,EACF,OAAAtE,EAAe,CACb,SAAU/E,EACV,OAAQqJ,EACR,KAAM,SACN,SAAUpQ,EACV,OAAQyB,EACR,KAAMA,CAClB,CAAW,EACD2J,EAAY,SAAUrE,EAAO,CAC3B,IAAK6C,CACjB,CAAW,EACM,EAEX,CAAC,EACGsG,GAAQ,CACVJ,GAAmBlG,EAAI,eAAc,EACrC,MACF,CAEE1E,EAAQ,QAAU,CAAClD,EAAQiO,EAAgB/K,EAAQ,OAAQzD,EAAI,EAAK,GAKxE,KAAK,kBAAkBmI,EAAKoG,EAAOhQ,CAAM,GAC3C,EACA,kBAAmB,SAAwC4J,EAAiBoG,EAAyBhQ,EAAQ,CAC3G,IAAI+G,EAAQ,KACVtF,EAAKsF,EAAM,GACX7B,EAAU6B,EAAM,QAChBsJ,EAAgB5O,EAAG,cACnB6O,EACF,GAAItQ,GAAU,CAACsL,GAAUtL,EAAO,aAAeyB,EAAI,CACjD,IAAI0M,EAAW3K,EAAQxD,CAAM,EA0E7B,GAzEAqK,EAAS5I,EACT6J,EAAStL,EACTuL,EAAWD,EAAO,WAClBE,GAASF,EAAO,YAChBG,GAAazL,EACbgM,GAAc9G,EAAQ,MACtBI,EAAS,QAAUgG,EACnBc,GAAS,CACP,OAAQd,EACR,SAAU0E,GAASpG,GAAK,QACxB,SAAUoG,GAASpG,GAAK,OAChC,EACM4C,GAAkBJ,GAAO,QAAU+B,EAAS,KAC5C1B,GAAiBL,GAAO,QAAU+B,EAAS,IAC3C,KAAK,QAAU6B,GAASpG,GAAK,QAC7B,KAAK,QAAUoG,GAASpG,GAAK,QAC7B0B,EAAO,MAAM,aAAa,EAAI,MAC9BgF,EAAc,UAAuB,CAInC,GAHAlF,EAAY,aAAcrE,EAAO,CAC/B,IAAK6C,CACf,CAAS,EACGtE,EAAS,cAAe,CAC1ByB,EAAM,QAAO,EACb,MACF,CAGAA,EAAM,0BAAyB,EAC3B,CAAC5F,IAAW4F,EAAM,kBACpBuE,EAAO,UAAY,IAIrBvE,EAAM,kBAAkB6C,EAAKoG,CAAK,EAGlClE,EAAe,CACb,SAAU/E,EACV,KAAM,SACN,cAAe6C,CACzB,CAAS,EAGDxH,EAAYkJ,EAAQpG,EAAQ,YAAa,EAAI,CAC/C,EAGAA,EAAQ,OAAO,MAAM,GAAG,EAAE,QAAQ,SAAUkL,EAAU,CACpDnN,GAAKqI,EAAQ8E,EAAS,KAAI,EAAIG,EAAiB,CACjD,CAAC,EACD/O,EAAG6O,EAAe,WAAYX,EAA6B,EAC3DlO,EAAG6O,EAAe,YAAaX,EAA6B,EAC5DlO,EAAG6O,EAAe,YAAaX,EAA6B,EACxDxK,EAAQ,gBACV1D,EAAG6O,EAAe,YAAatJ,EAAM,OAAO,EAE5C,CAAC,KAAK,iBAAmBvF,EAAG6O,EAAe,gBAAiBtJ,EAAM,OAAO,IAEzEvF,EAAG6O,EAAe,UAAWtJ,EAAM,OAAO,EAC1CvF,EAAG6O,EAAe,WAAYtJ,EAAM,OAAO,EAC3CvF,EAAG6O,EAAe,cAAetJ,EAAM,OAAO,GAI5C5F,IAAW,KAAK,kBAClB,KAAK,QAAQ,oBAAsB,EACnCmK,EAAO,UAAY,IAErBF,EAAY,aAAc,KAAM,CAC9B,IAAKxB,CACb,CAAO,EAGG1E,EAAQ,QAAU,CAACA,EAAQ,kBAAoB8K,KAAW,CAAC,KAAK,iBAAmB,EAAE9O,IAAQD,IAAc,CAC7G,GAAIqE,EAAS,cAAe,CAC1B,KAAK,QAAO,EACZ,MACF,CAIIJ,EAAQ,gBACV1D,EAAG6O,EAAe,YAAatJ,EAAM,mBAAmB,EACxDvF,EAAG6O,EAAe,gBAAiBtJ,EAAM,mBAAmB,IAE5DvF,EAAG6O,EAAe,UAAWtJ,EAAM,mBAAmB,EACtDvF,EAAG6O,EAAe,WAAYtJ,EAAM,mBAAmB,EACvDvF,EAAG6O,EAAe,cAAetJ,EAAM,mBAAmB,GAE5DvF,EAAG6O,EAAe,YAAatJ,EAAM,4BAA4B,EACjEvF,EAAG6O,EAAe,YAAatJ,EAAM,4BAA4B,EACjE7B,EAAQ,gBAAkB1D,EAAG6O,EAAe,cAAetJ,EAAM,4BAA4B,EAC7FA,EAAM,gBAAkB,WAAWuJ,EAAapL,EAAQ,KAAK,CAC/D,MACEoL,EAAW,CAEf,CACF,EACA,6BAA8B,SAAsEtR,EAAG,CACrG,IAAIgR,EAAQhR,EAAE,QAAUA,EAAE,QAAQ,CAAC,EAAIA,EACnC,KAAK,IAAI,KAAK,IAAIgR,EAAM,QAAU,KAAK,MAAM,EAAG,KAAK,IAAIA,EAAM,QAAU,KAAK,MAAM,CAAC,GAAK,KAAK,MAAM,KAAK,QAAQ,qBAAuB,KAAK,iBAAmB,OAAO,kBAAoB,EAAE,GAChM,KAAK,oBAAmB,CAE5B,EACA,oBAAqB,UAA+B,CAClD1E,GAAUiF,GAAkBjF,CAAM,EAClC,aAAa,KAAK,eAAe,EACjC,KAAK,0BAAyB,CAChC,EACA,0BAA2B,UAAqC,CAC9D,IAAI+E,EAAgB,KAAK,GAAG,cAC5BzO,EAAIyO,EAAe,UAAW,KAAK,mBAAmB,EACtDzO,EAAIyO,EAAe,WAAY,KAAK,mBAAmB,EACvDzO,EAAIyO,EAAe,cAAe,KAAK,mBAAmB,EAC1DzO,EAAIyO,EAAe,YAAa,KAAK,mBAAmB,EACxDzO,EAAIyO,EAAe,gBAAiB,KAAK,mBAAmB,EAC5DzO,EAAIyO,EAAe,YAAa,KAAK,4BAA4B,EACjEzO,EAAIyO,EAAe,YAAa,KAAK,4BAA4B,EACjEzO,EAAIyO,EAAe,cAAe,KAAK,4BAA4B,CACrE,EACA,kBAAmB,SAAwCzG,EAAiBoG,EAAO,CACjFA,EAAQA,GAASpG,EAAI,aAAe,SAAWA,EAC3C,CAAC,KAAK,iBAAmBoG,EACvB,KAAK,QAAQ,eACfxO,EAAG,SAAU,cAAe,KAAK,YAAY,EACpCwO,EACTxO,EAAG,SAAU,YAAa,KAAK,YAAY,EAE3CA,EAAG,SAAU,YAAa,KAAK,YAAY,GAG7CA,EAAG8J,EAAQ,UAAW,IAAI,EAC1B9J,EAAG6I,EAAQ,YAAa,KAAK,YAAY,GAE3C,GAAI,CACE,SAAS,UACXmG,GAAU,UAAY,CACpB,SAAS,UAAU,MAAK,CAC1B,CAAC,EAED,OAAO,aAAY,EAAG,gBAAe,CAEzC,MAAc,CAAC,CACjB,EACA,aAAc,SAAsBC,EAAU7G,EAAK,CAEjD,GADAqC,GAAsB,GAClB5B,GAAUiB,EAAQ,CACpBF,EAAY,cAAe,KAAM,CAC/B,IAAKxB,CACb,CAAO,EACG,KAAK,iBACPpI,EAAG,SAAU,WAAYoO,EAAqB,EAEhD,IAAI1K,EAAU,KAAK,QAGnB,CAACuL,GAAYrO,EAAYkJ,EAAQpG,EAAQ,UAAW,EAAK,EACzD9C,EAAYkJ,EAAQpG,EAAQ,WAAY,EAAI,EAC5CI,EAAS,OAAS,KAClBmL,GAAY,KAAK,aAAY,EAG7B3E,EAAe,CACb,SAAU,KACV,KAAM,QACN,cAAelC,CACvB,CAAO,CACH,MACE,KAAK,SAAQ,CAEjB,EACA,iBAAkB,UAA4B,CAC5C,GAAIyC,EAAU,CACZ,KAAK,OAASA,EAAS,QACvB,KAAK,OAASA,EAAS,QACvBT,GAAmB,EAGnB,QAFI5L,EAAS,SAAS,iBAAiBqM,EAAS,QAASA,EAAS,OAAO,EACrE1H,EAAS3E,EACNA,GAAUA,EAAO,aACtBA,EAASA,EAAO,WAAW,iBAAiBqM,EAAS,QAASA,EAAS,OAAO,EAC1ErM,IAAW2E,IACfA,EAAS3E,EAGX,GADAsL,EAAO,WAAWtD,CAAO,EAAE,iBAAiBhI,CAAM,EAC9C2E,EACF,EAAG,CACD,GAAIA,EAAOqD,CAAO,EAAG,CACnB,IAAI0I,EAAW,OAOf,GANAA,EAAW/L,EAAOqD,CAAO,EAAE,YAAY,CACrC,QAASqE,EAAS,QAClB,QAASA,EAAS,QAClB,OAAQrM,EACR,OAAQ2E,CACtB,CAAa,EACG+L,GAAY,CAAC,KAAK,QAAQ,eAC5B,KAEJ,CACA1Q,EAAS2E,CACX,OAC8BA,EAAS5C,GAAgB4C,CAAM,GAE/DkH,GAAqB,CACvB,CACF,EACA,aAAc,SAAsCjC,EAAK,CACvD,GAAIwC,GAAQ,CACV,IAAIlH,EAAU,KAAK,QACjByL,EAAoBzL,EAAQ,kBAC5B0L,EAAiB1L,EAAQ,eACzB8K,EAAQpG,EAAI,QAAUA,EAAI,QAAQ,CAAC,EAAIA,EACvCiH,EAAcrJ,GAAW5E,GAAO4E,EAAS,EAAI,EAC7ClD,EAASkD,GAAWqJ,GAAeA,EAAY,EAC/CtM,EAASiD,GAAWqJ,GAAeA,EAAY,EAC/CC,EAAuB1D,IAA2BL,GAAuBrH,GAAwBqH,CAAmB,EACpHgE,GAAMf,EAAM,QAAU5D,GAAO,QAAUwE,EAAe,IAAMtM,GAAU,IAAMwM,EAAuBA,EAAqB,CAAC,EAAI9D,GAAiC,CAAC,EAAI,IAAM1I,GAAU,GACnL0M,GAAMhB,EAAM,QAAU5D,GAAO,QAAUwE,EAAe,IAAMrM,GAAU,IAAMuM,EAAuBA,EAAqB,CAAC,EAAI9D,GAAiC,CAAC,EAAI,IAAMzI,GAAU,GAGrL,GAAI,CAACe,EAAS,QAAU,CAAC2G,GAAqB,CAC5C,GAAI0E,GAAqB,KAAK,IAAI,KAAK,IAAIX,EAAM,QAAU,KAAK,MAAM,EAAG,KAAK,IAAIA,EAAM,QAAU,KAAK,MAAM,CAAC,EAAIW,EAChH,OAEF,KAAK,aAAa/G,EAAK,EAAI,CAC7B,CACA,GAAIpC,EAAS,CACPqJ,GACFA,EAAY,GAAKE,GAAMzE,IAAU,GACjCuE,EAAY,GAAKG,GAAMzE,IAAU,IAEjCsE,EAAc,CACZ,EAAG,EACH,EAAG,EACH,EAAG,EACH,EAAG,EACH,EAAGE,EACH,EAAGC,CACf,EAEQ,IAAIC,EAAY,UAAU,OAAOJ,EAAY,EAAG,GAAG,EAAE,OAAOA,EAAY,EAAG,GAAG,EAAE,OAAOA,EAAY,EAAG,GAAG,EAAE,OAAOA,EAAY,EAAG,GAAG,EAAE,OAAOA,EAAY,EAAG,GAAG,EAAE,OAAOA,EAAY,EAAG,GAAG,EAC1LrO,EAAIgF,EAAS,kBAAmByJ,CAAS,EACzCzO,EAAIgF,EAAS,eAAgByJ,CAAS,EACtCzO,EAAIgF,EAAS,cAAeyJ,CAAS,EACrCzO,EAAIgF,EAAS,YAAayJ,CAAS,EACnC3E,GAASyE,EACTxE,GAASyE,EACT3E,EAAW2D,CACb,CACApG,EAAI,YAAcA,EAAI,eAAc,CACtC,CACF,EACA,aAAc,UAAwB,CAGpC,GAAI,CAACpC,EAAS,CACZ,IAAI5D,EAAY,KAAK,QAAQ,eAAiB,SAAS,KAAOyG,EAC5D5C,EAAOjE,EAAQ8H,EAAQ,GAAM8B,GAAyB,GAAMxJ,CAAS,EACrEsB,EAAU,KAAK,QAGjB,GAAIkI,GAAyB,CAG3B,IADAL,EAAsBnJ,EACfpB,EAAIuK,EAAqB,UAAU,IAAM,UAAYvK,EAAIuK,EAAqB,WAAW,IAAM,QAAUA,IAAwB,UACtIA,EAAsBA,EAAoB,WAExCA,IAAwB,SAAS,MAAQA,IAAwB,SAAS,iBACxEA,IAAwB,WAAUA,EAAsBzJ,EAAyB,GACrFmE,EAAK,KAAOsF,EAAoB,UAChCtF,EAAK,MAAQsF,EAAoB,YAEjCA,EAAsBzJ,EAAyB,EAEjD0J,GAAmCtH,GAAwBqH,CAAmB,CAChF,CACAvF,EAAU8D,EAAO,UAAU,EAAI,EAC/BlJ,EAAYoF,EAAStC,EAAQ,WAAY,EAAK,EAC9C9C,EAAYoF,EAAStC,EAAQ,cAAe,EAAI,EAChD9C,EAAYoF,EAAStC,EAAQ,UAAW,EAAI,EAC5C1C,EAAIgF,EAAS,aAAc,EAAE,EAC7BhF,EAAIgF,EAAS,YAAa,EAAE,EAC5BhF,EAAIgF,EAAS,aAAc,YAAY,EACvChF,EAAIgF,EAAS,SAAU,CAAC,EACxBhF,EAAIgF,EAAS,MAAOC,EAAK,GAAG,EAC5BjF,EAAIgF,EAAS,OAAQC,EAAK,IAAI,EAC9BjF,EAAIgF,EAAS,QAASC,EAAK,KAAK,EAChCjF,EAAIgF,EAAS,SAAUC,EAAK,MAAM,EAClCjF,EAAIgF,EAAS,UAAW,KAAK,EAC7BhF,EAAIgF,EAAS,WAAY4F,GAA0B,WAAa,OAAO,EACvE5K,EAAIgF,EAAS,SAAU,QAAQ,EAC/BhF,EAAIgF,EAAS,gBAAiB,MAAM,EACpClC,EAAS,MAAQkC,EACjB5D,EAAU,YAAY4D,CAAO,EAG7BhF,EAAIgF,EAAS,mBAAoBgF,GAAkB,SAAShF,EAAQ,MAAM,KAAK,EAAI,IAAM,KAAOiF,GAAiB,SAASjF,EAAQ,MAAM,MAAM,EAAI,IAAM,GAAG,CAC7J,CACF,EACA,aAAc,SAAiCoC,EAAiB6G,EAAU,CACxE,IAAI1J,EAAQ,KACR8I,EAAejG,EAAI,aACnB1E,EAAU6B,EAAM,QAIpB,GAHAqE,EAAY,YAAa,KAAM,CAC7B,IAAKxB,CACX,CAAK,EACGtE,EAAS,cAAe,CAC1B,KAAK,QAAO,EACZ,MACF,CACA8F,EAAY,aAAc,IAAI,EACzB9F,EAAS,gBACZiF,EAAUnD,GAAMkE,CAAM,EACtBf,EAAQ,gBAAgB,IAAI,EAC5BA,EAAQ,UAAY,GACpBA,EAAQ,MAAM,aAAa,EAAI,GAC/B,KAAK,WAAU,EACfnI,EAAYmI,EAAS,KAAK,QAAQ,YAAa,EAAK,EACpDjF,EAAS,MAAQiF,GAInBxD,EAAM,QAAUyJ,GAAU,UAAY,CACpCpF,EAAY,QAASrE,CAAK,EACtB,CAAAzB,EAAS,gBACRyB,EAAM,QAAQ,mBACjBsD,EAAO,aAAaE,EAASe,CAAM,EAErCvE,EAAM,WAAU,EAChB+E,EAAe,CACb,SAAU/E,EACV,KAAM,OACd,CAAO,EACH,CAAC,EACD,CAAC0J,GAAYrO,EAAYkJ,EAAQpG,EAAQ,UAAW,EAAI,EAGpDuL,GACFvE,GAAkB,GAClBnF,EAAM,QAAU,YAAYA,EAAM,iBAAkB,EAAE,IAGtDnF,EAAI,SAAU,UAAWmF,EAAM,OAAO,EACtCnF,EAAI,SAAU,WAAYmF,EAAM,OAAO,EACvCnF,EAAI,SAAU,cAAemF,EAAM,OAAO,EACtC8I,IACFA,EAAa,cAAgB,OAC7B3K,EAAQ,SAAWA,EAAQ,QAAQ,KAAK6B,EAAO8I,EAAcvE,CAAM,GAErE9J,EAAG,SAAU,OAAQuF,CAAK,EAG1BvE,EAAI8I,EAAQ,YAAa,eAAe,GAE1CW,GAAsB,GACtBlF,EAAM,aAAeyJ,GAAUzJ,EAAM,aAAa,KAAKA,EAAO0J,EAAU7G,CAAG,CAAC,EAC5EpI,EAAG,SAAU,cAAeuF,CAAK,EACjC4E,GAAQ,GACR,OAAO,aAAY,EAAG,gBAAe,EACjCvK,IACFoB,EAAI,SAAS,KAAM,cAAe,MAAM,CAE5C,EAEA,YAAa,SAAgCoH,EAAK,CAChD,IAAInI,EAAK,KAAK,GACZzB,EAAS4J,EAAI,OACbuE,EACAC,EACA8C,EACAhM,EAAU,KAAK,QACfsK,EAAQtK,EAAQ,MAChBiM,EAAiB7L,EAAS,OAC1B8L,EAAUpF,KAAgBwD,EAC1B6B,EAAUnM,EAAQ,KAClBoM,EAAevG,GAAeoG,EAC9B9C,EACAtH,EAAQ,KACRwK,EAAiB,GACnB,GAAItE,GAAS,OACb,SAASuE,EAAcnP,GAAMoP,GAAO,CAClCrG,EAAY/I,GAAM0E,EAAOhH,EAAe,CACtC,IAAK6J,EACL,QAASwH,EACT,KAAM/C,EAAW,WAAa,aAC9B,OAAQ6C,EACR,SAAU/C,EACV,WAAYC,EACZ,QAASiD,EACT,aAAcC,EACd,OAAQtR,EACR,UAAW0R,EACX,OAAQ,SAAgB1R,GAAQ2R,GAAO,CACrC,OAAOC,GAAQvH,EAAQ5I,EAAI6J,EAAQ6C,EAAUnO,GAAQwD,EAAQxD,EAAM,EAAG4J,EAAK+H,EAAK,CAClF,EACA,QAASE,CACjB,EAASJ,EAAK,CAAC,CACX,CAGA,SAASK,GAAU,CACjBN,EAAc,0BAA0B,EACxCzK,EAAM,sBAAqB,EACvBA,IAAUuK,GACZA,EAAa,sBAAqB,CAEtC,CAGA,SAASI,EAAUK,GAAW,CAC5B,OAAAP,EAAc,oBAAqB,CACjC,UAAWO,EACnB,CAAO,EACGA,KAEEX,EACFD,EAAe,WAAU,EAEzBA,EAAe,WAAWpK,CAAK,EAE7BA,IAAUuK,IAEZlP,EAAYkJ,EAAQP,EAAcA,EAAY,QAAQ,WAAaoG,EAAe,QAAQ,WAAY,EAAK,EAC3G/O,EAAYkJ,EAAQpG,EAAQ,WAAY,EAAI,GAE1C6F,IAAgBhE,GAASA,IAAUzB,EAAS,OAC9CyF,EAAchE,EACLA,IAAUzB,EAAS,QAAUyF,IACtCA,EAAc,MAIZuG,IAAiBvK,IACnBA,EAAM,sBAAwB/G,GAEhC+G,EAAM,WAAW,UAAY,CAC3ByK,EAAc,2BAA2B,EACzCzK,EAAM,sBAAwB,IAChC,CAAC,EACGA,IAAUuK,IACZA,EAAa,WAAU,EACvBA,EAAa,sBAAwB,QAKrCtR,IAAWsL,GAAU,CAACA,EAAO,UAAYtL,IAAWyB,GAAM,CAACzB,EAAO,YACpE0M,GAAa,MAIX,CAACxH,EAAQ,gBAAkB,CAAC0E,EAAI,QAAU5J,IAAW,WACvDsL,EAAO,WAAWtD,CAAO,EAAE,iBAAiB4B,EAAI,MAAM,EAGtD,CAACmI,IAAarC,GAA8B9F,CAAG,GAEjD,CAAC1E,EAAQ,gBAAkB0E,EAAI,iBAAmBA,EAAI,gBAAe,EAC9D2H,EAAiB,EAC1B,CAGA,SAASM,GAAU,CACjBlH,EAAWlF,EAAM6F,CAAM,EACvBT,GAAoBpF,EAAM6F,EAAQpG,EAAQ,SAAS,EACnD4G,EAAe,CACb,SAAU/E,EACV,KAAM,SACN,KAAMtF,EACN,SAAUkJ,EACV,kBAAmBE,GACnB,cAAejB,CACvB,CAAO,CACH,CAMA,GALIA,EAAI,iBAAmB,QACzBA,EAAI,YAAcA,EAAI,eAAc,EAEtC5J,EAASgC,EAAQhC,EAAQkF,EAAQ,UAAWzD,EAAI,EAAI,EACpD+P,EAAc,UAAU,EACpBlM,EAAS,cAAe,OAAOiM,EACnC,GAAIjG,EAAO,SAAS1B,EAAI,MAAM,GAAK5J,EAAO,UAAYA,EAAO,YAAcA,EAAO,YAAc+G,EAAM,wBAA0B/G,EAC9H,OAAO0R,EAAU,EAAK,EAGxB,GADAxF,GAAkB,GACdiF,GAAkB,CAACjM,EAAQ,WAAakM,EAAUC,IAAYH,EAAS3F,IAAalB,GACtFU,IAAgB,OAAS,KAAK,YAAciB,GAAY,UAAU,KAAMmF,EAAgB7F,EAAQ1B,CAAG,IAAM4F,EAAM,SAAS,KAAM2B,EAAgB7F,EAAQ1B,CAAG,GAAI,CAI7J,GAHAyE,EAAW,KAAK,cAAczE,EAAK5J,CAAM,IAAM,WAC/CmO,EAAW3K,EAAQ8H,CAAM,EACzBkG,EAAc,eAAe,EACzBlM,EAAS,cAAe,OAAOiM,EACnC,GAAIL,EACF,OAAA3F,EAAWlB,EACXyH,EAAO,EACP,KAAK,WAAU,EACfN,EAAc,QAAQ,EACjBlM,EAAS,gBACRkG,GACFnB,EAAO,aAAaiB,EAAQE,EAAM,EAElCnB,EAAO,YAAYiB,CAAM,GAGtBoG,EAAU,EAAI,EAEvB,IAAIM,EAAczM,GAAU9D,EAAIyD,EAAQ,SAAS,EACjD,GAAI,CAAC8M,GAAeC,GAAarI,EAAKyE,EAAU,IAAI,GAAK,CAAC2D,EAAY,SAAU,CAI9E,GAAIA,IAAgB1G,EAClB,OAAOoG,EAAU,EAAK,EAUxB,GANIM,GAAevQ,IAAOmI,EAAI,SAC5B5J,EAASgS,GAEPhS,IACFoO,EAAa5K,EAAQxD,CAAM,GAEzB4R,GAAQvH,EAAQ5I,EAAI6J,EAAQ6C,EAAUnO,EAAQoO,EAAYxE,EAAK,CAAC,CAAC5J,CAAM,IAAM,GAC/E,OAAA8R,EAAO,EACHE,GAAeA,EAAY,YAE7BvQ,EAAG,aAAa6J,EAAQ0G,EAAY,WAAW,EAE/CvQ,EAAG,YAAY6J,CAAM,EAEvBC,EAAW9J,EAEXoQ,EAAO,EACAH,EAAU,EAAI,CAEzB,SAAWM,GAAeE,GAActI,EAAKyE,EAAU,IAAI,EAAG,CAE5D,IAAI8D,GAAanN,GAASvD,EAAI,EAAGyD,EAAS,EAAI,EAC9C,GAAIiN,KAAe7G,EACjB,OAAOoG,EAAU,EAAK,EAIxB,GAFA1R,EAASmS,GACT/D,EAAa5K,EAAQxD,CAAM,EACvB4R,GAAQvH,EAAQ5I,EAAI6J,EAAQ6C,EAAUnO,EAAQoO,EAAYxE,EAAK,EAAK,IAAM,GAC5E,OAAAkI,EAAO,EACPrQ,EAAG,aAAa6J,EAAQ6G,EAAU,EAClC5G,EAAW9J,EAEXoQ,EAAO,EACAH,EAAU,EAAI,CAEzB,SAAW1R,EAAO,aAAeyB,EAAI,CACnC2M,EAAa5K,EAAQxD,CAAM,EAC3B,IAAIoS,EAAY,EACdC,GACAC,GAAiBhH,EAAO,aAAe7J,EACvC8Q,EAAkB,CAACrE,GAAmB5C,EAAO,UAAYA,EAAO,QAAU6C,EAAUnO,EAAO,UAAYA,EAAO,QAAUoO,EAAYC,CAAQ,EAC5ImE,GAAQnE,EAAW,MAAQ,OAC3BoE,GAAkBjO,GAAexE,EAAQ,MAAO,KAAK,GAAKwE,GAAe8G,EAAQ,MAAO,KAAK,EAC7FoH,GAAeD,GAAkBA,GAAgB,UAAY,OAC3D/F,KAAe1M,IACjBqS,GAAwBjE,EAAWoE,EAAK,EACxC5F,GAAwB,GACxBC,GAAyB,CAAC0F,GAAmBrN,EAAQ,YAAcoN,IAErEF,EAAYO,GAAkB/I,EAAK5J,EAAQoO,EAAYC,EAAUkE,EAAkB,EAAIrN,EAAQ,cAAeA,EAAQ,uBAAyB,KAAOA,EAAQ,cAAgBA,EAAQ,sBAAuB2H,GAAwBH,KAAe1M,CAAM,EAC1P,IAAI4S,EACJ,GAAIR,IAAc,EAAG,CAEnB,IAAIS,GAAYpN,EAAM6F,CAAM,EAC5B,GACEuH,IAAaT,EACbQ,EAAUrH,EAAS,SAASsH,EAAS,QAC9BD,IAAYpQ,EAAIoQ,EAAS,SAAS,IAAM,QAAUA,IAAYpL,GACzE,CAEA,GAAI4K,IAAc,GAAKQ,IAAY5S,EACjC,OAAO0R,EAAU,EAAK,EAExBhF,GAAa1M,EACb2M,GAAgByF,EAChB,IAAIU,GAAc9S,EAAO,mBACvB2R,GAAQ,GACVA,GAAQS,IAAc,EACtB,IAAIW,GAAanB,GAAQvH,EAAQ5I,EAAI6J,EAAQ6C,EAAUnO,EAAQoO,EAAYxE,EAAK+H,EAAK,EACrF,GAAIoB,KAAe,GACjB,OAAIA,KAAe,GAAKA,KAAe,MACrCpB,GAAQoB,KAAe,GAEzB9F,GAAU,GACV,WAAW+F,GAAW,EAAE,EACxBlB,EAAO,EACHH,IAAS,CAACmB,GACZrR,EAAG,YAAY6J,CAAM,EAErBtL,EAAO,WAAW,aAAasL,EAAQqG,GAAQmB,GAAc9S,CAAM,EAIjEyS,IACFxL,GAASwL,GAAiB,EAAGC,GAAeD,GAAgB,SAAS,EAEvElH,EAAWD,EAAO,WAGd+G,KAA0B,QAAa,CAACxF,KAC1CC,GAAqB,KAAK,IAAIuF,GAAwB7O,EAAQxD,CAAM,EAAEwS,EAAK,CAAC,GAE9EX,EAAO,EACAH,EAAU,EAAI,CAEzB,CACA,GAAIjQ,EAAG,SAAS6J,CAAM,EACpB,OAAOoG,EAAU,EAAK,CAE1B,CACA,MAAO,EACT,EACA,sBAAuB,KACvB,eAAgB,UAA0B,CACxC9P,EAAI,SAAU,YAAa,KAAK,YAAY,EAC5CA,EAAI,SAAU,YAAa,KAAK,YAAY,EAC5CA,EAAI,SAAU,cAAe,KAAK,YAAY,EAC9CA,EAAI,SAAU,WAAY8N,EAA6B,EACvD9N,EAAI,SAAU,YAAa8N,EAA6B,EACxD9N,EAAI,SAAU,YAAa8N,EAA6B,CAC1D,EACA,aAAc,UAAwB,CACpC,IAAIW,EAAgB,KAAK,GAAG,cAC5BzO,EAAIyO,EAAe,UAAW,KAAK,OAAO,EAC1CzO,EAAIyO,EAAe,WAAY,KAAK,OAAO,EAC3CzO,EAAIyO,EAAe,YAAa,KAAK,OAAO,EAC5CzO,EAAIyO,EAAe,gBAAiB,KAAK,OAAO,EAChDzO,EAAIyO,EAAe,cAAe,KAAK,OAAO,EAC9CzO,EAAI,SAAU,cAAe,IAAI,CACnC,EACA,QAAS,SAA4BgI,EAAK,CACxC,IAAInI,EAAK,KAAK,GACZyD,EAAU,KAAK,QAajB,GAVAyF,EAAWlF,EAAM6F,CAAM,EACvBT,GAAoBpF,EAAM6F,EAAQpG,EAAQ,SAAS,EACnDkG,EAAY,OAAQ,KAAM,CACxB,IAAKxB,CACX,CAAK,EACD2B,EAAWD,GAAUA,EAAO,WAG5BX,EAAWlF,EAAM6F,CAAM,EACvBT,GAAoBpF,EAAM6F,EAAQpG,EAAQ,SAAS,EAC/CI,EAAS,cAAe,CAC1B,KAAK,SAAQ,EACb,MACF,CACA2G,GAAsB,GACtBY,GAAyB,GACzBD,GAAwB,GACxB,cAAc,KAAK,OAAO,EAC1B,aAAa,KAAK,eAAe,EACjCqG,GAAgB,KAAK,OAAO,EAC5BA,GAAgB,KAAK,YAAY,EAG7B,KAAK,kBACPrR,EAAI,SAAU,OAAQ,IAAI,EAC1BA,EAAIH,EAAI,YAAa,KAAK,YAAY,GAExC,KAAK,eAAc,EACnB,KAAK,aAAY,EACbL,IACFoB,EAAI,SAAS,KAAM,cAAe,EAAE,EAEtCA,EAAI8I,EAAQ,YAAa,EAAE,EACvB1B,IACE+B,KACF/B,EAAI,YAAcA,EAAI,eAAc,EACpC,CAAC1E,EAAQ,YAAc0E,EAAI,gBAAe,GAE5CpC,GAAWA,EAAQ,YAAcA,EAAQ,WAAW,YAAYA,CAAO,GACnE6C,IAAWkB,GAAYR,GAAeA,EAAY,cAAgB,UAEpER,GAAWA,EAAQ,YAAcA,EAAQ,WAAW,YAAYA,CAAO,EAErEe,IACE,KAAK,iBACP1J,EAAI0J,EAAQ,UAAW,IAAI,EAE7BiF,GAAkBjF,CAAM,EACxBA,EAAO,MAAM,aAAa,EAAI,GAI1BK,IAAS,CAACM,IACZ7J,EAAYkJ,EAAQP,EAAcA,EAAY,QAAQ,WAAa,KAAK,QAAQ,WAAY,EAAK,EAEnG3I,EAAYkJ,EAAQ,KAAK,QAAQ,YAAa,EAAK,EAGnDQ,EAAe,CACb,SAAU,KACV,KAAM,WACN,KAAMP,EACN,SAAU,KACV,kBAAmB,KACnB,cAAe3B,CACzB,CAAS,EACGS,IAAWkB,GACTZ,GAAY,IAEdmB,EAAe,CACb,OAAQP,EACR,KAAM,MACN,KAAMA,EACN,OAAQlB,EACR,cAAeT,CAC7B,CAAa,EAGDkC,EAAe,CACb,SAAU,KACV,KAAM,SACN,KAAMP,EACN,cAAe3B,CAC7B,CAAa,EAGDkC,EAAe,CACb,OAAQP,EACR,KAAM,OACN,KAAMA,EACN,OAAQlB,EACR,cAAeT,CAC7B,CAAa,EACDkC,EAAe,CACb,SAAU,KACV,KAAM,OACN,KAAMP,EACN,cAAe3B,CAC7B,CAAa,GAEHmB,GAAeA,EAAY,KAAI,GAE3BJ,IAAaD,IACXC,GAAY,IAEdmB,EAAe,CACb,SAAU,KACV,KAAM,SACN,KAAMP,EACN,cAAe3B,CAC/B,CAAe,EACDkC,EAAe,CACb,SAAU,KACV,KAAM,OACN,KAAMP,EACN,cAAe3B,CAC/B,CAAe,GAIHtE,EAAS,UAEPqF,GAAY,MAAQA,IAAa,MACnCA,EAAWD,GACXG,GAAoBD,IAEtBkB,EAAe,CACb,SAAU,KACV,KAAM,MACN,KAAMP,EACN,cAAe3B,CAC3B,CAAW,EAGD,KAAK,KAAI,KAIf,KAAK,SAAQ,CACf,EACA,SAAU,UAAoB,CAC5BwB,EAAY,UAAW,IAAI,EAC3Bf,EAASiB,EAASC,EAAW/D,EAAUgE,GAASjB,EAAUkB,GAAaC,EAAcU,GAASC,EAAWV,GAAQhB,EAAWE,GAAoBH,GAAWE,GAAoB8B,GAAaC,GAAgB5B,EAAciB,GAAc1G,EAAS,QAAUA,EAAS,MAAQA,EAAS,MAAQA,EAAS,OAAS,KAC/S4H,GAAkB,QAAQ,SAAUzL,EAAI,CACtCA,EAAG,QAAU,EACf,CAAC,EACDyL,GAAkB,OAASZ,GAASC,GAAS,CAC/C,EACA,YAAa,SAAgC3C,EAAK,CAChD,OAAQA,EAAI,KAAI,CACd,IAAK,OACL,IAAK,UACH,KAAK,QAAQA,CAAG,EAChB,MACF,IAAK,YACL,IAAK,WACC0B,IACF,KAAK,YAAY1B,CAAG,EACpBsJ,GAAgBtJ,CAAG,GAErB,MACF,IAAK,cACHA,EAAI,eAAc,EAClB,KACR,CACE,EAKA,QAAS,UAAmB,CAO1B,QANIuJ,EAAQ,CAAA,EACV1R,EACA4D,EAAW,KAAK,GAAG,SACnBpF,EAAI,EACJoD,EAAIgC,EAAS,OACbH,EAAU,KAAK,QACVjF,EAAIoD,EAAGpD,IACZwB,EAAK4D,EAASpF,CAAC,EACX+B,EAAQP,EAAIyD,EAAQ,UAAW,KAAK,GAAI,EAAK,GAC/CiO,EAAM,KAAK1R,EAAG,aAAayD,EAAQ,UAAU,GAAKkO,GAAY3R,CAAE,CAAC,EAGrE,OAAO0R,CACT,EAKA,KAAM,SAAcA,EAAOE,EAAc,CACvC,IAAIC,EAAQ,CAAA,EACVjJ,EAAS,KAAK,GAChB,KAAK,QAAO,EAAG,QAAQ,SAAUkJ,EAAItT,EAAG,CACtC,IAAIwB,EAAK4I,EAAO,SAASpK,CAAC,EACtB+B,EAAQP,EAAI,KAAK,QAAQ,UAAW4I,EAAQ,EAAK,IACnDiJ,EAAMC,CAAE,EAAI9R,EAEhB,EAAG,IAAI,EACP4R,GAAgB,KAAK,sBAAqB,EAC1CF,EAAM,QAAQ,SAAUI,EAAI,CACtBD,EAAMC,CAAE,IACVlJ,EAAO,YAAYiJ,EAAMC,CAAE,CAAC,EAC5BlJ,EAAO,YAAYiJ,EAAMC,CAAE,CAAC,EAEhC,CAAC,EACDF,GAAgB,KAAK,WAAU,CACjC,EAIA,KAAM,UAAgB,CACpB,IAAIG,EAAQ,KAAK,QAAQ,MACzBA,GAASA,EAAM,KAAOA,EAAM,IAAI,IAAI,CACtC,EAOA,QAAS,SAAmB/R,EAAIK,EAAU,CACxC,OAAOE,EAAQP,EAAIK,GAAY,KAAK,QAAQ,UAAW,KAAK,GAAI,EAAK,CACvE,EAOA,OAAQ,SAAgBO,EAAM9B,EAAO,CACnC,IAAI2E,EAAU,KAAK,QACnB,GAAI3E,IAAU,OACZ,OAAO2E,EAAQ7C,CAAI,EAEnB,IAAI6H,EAAgBZ,GAAc,aAAa,KAAMjH,EAAM9B,CAAK,EAC5D,OAAO2J,EAAkB,IAC3BhF,EAAQ7C,CAAI,EAAI6H,EAEhBhF,EAAQ7C,CAAI,EAAI9B,EAEd8B,IAAS,SACX4M,GAAc/J,CAAO,CAG3B,EAIA,QAAS,UAAmB,CAC1BkG,EAAY,UAAW,IAAI,EAC3B,IAAI3J,EAAK,KAAK,GACdA,EAAGuG,CAAO,EAAI,KACdpG,EAAIH,EAAI,YAAa,KAAK,WAAW,EACrCG,EAAIH,EAAI,aAAc,KAAK,WAAW,EACtCG,EAAIH,EAAI,cAAe,KAAK,WAAW,EACnC,KAAK,kBACPG,EAAIH,EAAI,WAAY,IAAI,EACxBG,EAAIH,EAAI,YAAa,IAAI,GAG3B,MAAM,UAAU,QAAQ,KAAKA,EAAG,iBAAiB,aAAa,EAAG,SAAUA,EAAI,CAC7EA,EAAG,gBAAgB,WAAW,CAChC,CAAC,EACD,KAAK,QAAO,EACZ,KAAK,0BAAyB,EAC9B0K,GAAU,OAAOA,GAAU,QAAQ,KAAK,EAAE,EAAG,CAAC,EAC9C,KAAK,GAAK1K,EAAK,IACjB,EACA,WAAY,UAAsB,CAChC,GAAI,CAACiK,EAAa,CAEhB,GADAN,EAAY,YAAa,IAAI,EACzB9F,EAAS,cAAe,OAC5B9C,EAAI+H,EAAS,UAAW,MAAM,EAC1B,KAAK,QAAQ,mBAAqBA,EAAQ,YAC5CA,EAAQ,WAAW,YAAYA,CAAO,EAExCmB,EAAc,EAChB,CACF,EACA,WAAY,SAAoBX,EAAa,CAC3C,GAAIA,EAAY,cAAgB,QAAS,CACvC,KAAK,WAAU,EACf,MACF,CACA,GAAIW,EAAa,CAEf,GADAN,EAAY,YAAa,IAAI,EACzB9F,EAAS,cAAe,OAGxBgG,EAAO,YAAcjB,GAAU,CAAC,KAAK,QAAQ,MAAM,YACrDA,EAAO,aAAaE,EAASe,CAAM,EAC1BE,GACTnB,EAAO,aAAaE,EAASiB,EAAM,EAEnCnB,EAAO,YAAYE,CAAO,EAExB,KAAK,QAAQ,MAAM,aACrB,KAAK,QAAQe,EAAQf,CAAO,EAE9B/H,EAAI+H,EAAS,UAAW,EAAE,EAC1BmB,EAAc,EAChB,CACF,CACF,EACA,SAASwH,GAA2BtJ,EAAK,CACnCA,EAAI,eACNA,EAAI,aAAa,WAAa,QAEhCA,EAAI,YAAcA,EAAI,eAAc,CACtC,CACA,SAASgI,GAAQnH,EAAQD,EAAMc,EAAQ6C,EAAU7D,EAAU8D,EAAYtD,EAAe2I,EAAiB,CACrG,IAAI7J,EACFD,EAAWc,EAAOzC,CAAO,EACzB0L,EAAW/J,EAAS,QAAQ,OAC5BgK,EAEF,OAAI,OAAO,aAAe,CAAC1S,GAAc,CAACC,GACxC0I,EAAM,IAAI,YAAY,OAAQ,CAC5B,QAAS,GACT,WAAY,EAClB,CAAK,GAEDA,EAAM,SAAS,YAAY,OAAO,EAClCA,EAAI,UAAU,OAAQ,GAAM,EAAI,GAElCA,EAAI,GAAKY,EACTZ,EAAI,KAAOa,EACXb,EAAI,QAAU0B,EACd1B,EAAI,YAAcuE,EAClBvE,EAAI,QAAUU,GAAYE,EAC1BZ,EAAI,YAAcwE,GAAc5K,EAAQgH,CAAI,EAC5CZ,EAAI,gBAAkB6J,EACtB7J,EAAI,cAAgBkB,EACpBL,EAAO,cAAcb,CAAG,EACpB8J,IACFC,EAASD,EAAS,KAAK/J,EAAUC,EAAKkB,CAAa,GAE9C6I,CACT,CACA,SAASpD,GAAkB9O,EAAI,CAC7BA,EAAG,UAAY,EACjB,CACA,SAASuR,IAAY,CACnB/F,GAAU,EACZ,CACA,SAASiF,GAActI,EAAKyE,EAAU1E,EAAU,CAC9C,IAAIiK,EAAcpQ,EAAQwB,GAAS2E,EAAS,GAAI,EAAGA,EAAS,QAAS,EAAI,CAAC,EACtEkK,EAAsBtM,GAAkCoC,EAAS,GAAIA,EAAS,QAASnC,CAAO,EAC9FsM,EAAS,GACb,OAAOzF,EAAWzE,EAAI,QAAUiK,EAAoB,KAAOC,GAAUlK,EAAI,QAAUgK,EAAY,KAAOhK,EAAI,QAAUgK,EAAY,MAAQhK,EAAI,QAAUiK,EAAoB,IAAMC,GAAUlK,EAAI,QAAUgK,EAAY,QAAUhK,EAAI,QAAUgK,EAAY,IAC1P,CACA,SAAS3B,GAAarI,EAAKyE,EAAU1E,EAAU,CAC7C,IAAIoK,EAAavQ,EAAQ+B,GAAUoE,EAAS,GAAIA,EAAS,QAAQ,SAAS,CAAC,EACvEkK,EAAsBtM,GAAkCoC,EAAS,GAAIA,EAAS,QAASnC,CAAO,EAC9FsM,EAAS,GACb,OAAOzF,EAAWzE,EAAI,QAAUiK,EAAoB,MAAQC,GAAUlK,EAAI,QAAUmK,EAAW,QAAUnK,EAAI,QAAUmK,EAAW,KAAOnK,EAAI,QAAUiK,EAAoB,OAASC,GAAUlK,EAAI,QAAUmK,EAAW,OAASnK,EAAI,QAAUmK,EAAW,GAC3P,CACA,SAASpB,GAAkB/I,EAAK5J,EAAQoO,EAAYC,EAAU2F,EAAeC,EAAuBC,EAAYC,EAAc,CAC5H,IAAIC,EAAc/F,EAAWzE,EAAI,QAAUA,EAAI,QAC7CyK,EAAehG,EAAWD,EAAW,OAASA,EAAW,MACzDkG,EAAWjG,EAAWD,EAAW,IAAMA,EAAW,KAClDmG,EAAWlG,EAAWD,EAAW,OAASA,EAAW,MACrDoG,EAAS,GACX,GAAI,CAACN,GAEH,GAAIC,GAAgBrH,GAAqBuH,EAAeL,GAOtD,GAJI,CAACpH,KAA0BD,KAAkB,EAAIyH,EAAcE,EAAWD,EAAeJ,EAAwB,EAAIG,EAAcG,EAAWF,EAAeJ,EAAwB,KAEvLrH,GAAwB,IAErBA,GAOH4H,EAAS,WALL7H,KAAkB,EAAIyH,EAAcE,EAAWxH,GACjDsH,EAAcG,EAAWzH,GACzB,MAAO,CAACH,WAORyH,EAAcE,EAAWD,GAAgB,EAAIL,GAAiB,GAAKI,EAAcG,EAAWF,GAAgB,EAAIL,GAAiB,EACnI,OAAOS,GAAoBzU,CAAM,EAKvC,OADAwU,EAASA,GAAUN,EACfM,IAEEJ,EAAcE,EAAWD,EAAeJ,EAAwB,GAAKG,EAAcG,EAAWF,EAAeJ,EAAwB,GAChIG,EAAcE,EAAWD,EAAe,EAAI,EAAI,GAGpD,CACT,CAQA,SAASI,GAAoBzU,EAAQ,CACnC,OAAIyF,EAAM6F,CAAM,EAAI7F,EAAMzF,CAAM,EACvB,EAEA,EAEX,CAQA,SAASoT,GAAY3R,EAAI,CAIvB,QAHIiT,EAAMjT,EAAG,QAAUA,EAAG,UAAYA,EAAG,IAAMA,EAAG,KAAOA,EAAG,YAC1DxB,EAAIyU,EAAI,OACRC,EAAM,EACD1U,KACL0U,GAAOD,EAAI,WAAWzU,CAAC,EAEzB,OAAO0U,EAAI,SAAS,EAAE,CACxB,CACA,SAASxE,GAAuByE,EAAM,CACpC1H,GAAkB,OAAS,EAG3B,QAFI2H,EAASD,EAAK,qBAAqB,OAAO,EAC1CE,EAAMD,EAAO,OACVC,KAAO,CACZ,IAAIrT,EAAKoT,EAAOC,CAAG,EACnBrT,EAAG,SAAWyL,GAAkB,KAAKzL,CAAE,CACzC,CACF,CACA,SAAS+O,GAAU7O,EAAI,CACrB,OAAO,WAAWA,EAAI,CAAC,CACzB,CACA,SAASsR,GAAgBM,EAAI,CAC3B,OAAO,aAAaA,CAAE,CACxB,CAGIpG,IACF3L,EAAG,SAAU,YAAa,SAAUoI,EAAK,EAClCtE,EAAS,QAAU2G,KAAwBrC,EAAI,YAClDA,EAAI,eAAc,CAEtB,CAAC,EAIHtE,EAAS,MAAQ,CACf,GAAI9D,EACJ,IAAKI,EACL,IAAKY,EACL,KAAMS,GACN,GAAI,SAAYxB,EAAIK,EAAU,CAC5B,MAAO,CAAC,CAACE,EAAQP,EAAIK,EAAUL,EAAI,EAAK,CAC1C,EACA,OAAQ2E,GACR,SAAUO,GACV,QAAS3E,EACT,YAAaI,EACb,MAAOgF,GACP,MAAO3B,EACP,SAAU+K,GACV,eAAgByC,GAChB,gBAAiBzF,GACjB,SAAUxI,GACV,QAASgD,CACX,EAOA1C,EAAS,IAAM,SAAUyP,EAAS,CAChC,OAAOA,EAAQ/M,CAAO,CACxB,EAMA1C,EAAS,MAAQ,UAAY,CAC3B,QAAS0P,EAAO,UAAU,OAAQ5L,EAAU,IAAI,MAAM4L,CAAI,EAAGC,EAAO,EAAGA,EAAOD,EAAMC,IAClF7L,EAAQ6L,CAAI,EAAI,UAAUA,CAAI,EAE5B7L,EAAQ,CAAC,EAAE,cAAgB,QAAOA,EAAUA,EAAQ,CAAC,GACzDA,EAAQ,QAAQ,SAAUG,EAAQ,CAChC,GAAI,CAACA,EAAO,WAAa,CAACA,EAAO,UAAU,YACzC,KAAM,gEAAgE,OAAO,CAAA,EAAG,SAAS,KAAKA,CAAM,CAAC,EAEnGA,EAAO,QAAOjE,EAAS,MAAQvF,EAAeA,EAAe,CAAA,EAAIuF,EAAS,KAAK,EAAGiE,EAAO,KAAK,GAClGD,GAAc,MAAMC,CAAM,CAC5B,CAAC,CACH,EAOAjE,EAAS,OAAS,SAAU7D,EAAIyD,EAAS,CACvC,OAAO,IAAII,EAAS7D,EAAIyD,CAAO,CACjC,EAGAI,EAAS,QAAUxE,GAEnB,IAAIoU,EAAc,CAAA,EAChBC,GACAC,GACAC,GAAY,GACZC,GACAC,GACAC,GACAC,GACF,SAASC,IAAmB,CAC1B,SAASC,GAAa,CACpB,KAAK,SAAW,CACd,OAAQ,GACR,wBAAyB,GACzB,kBAAmB,GACnB,YAAa,GACb,aAAc,EACpB,EAGI,QAAShU,KAAM,KACTA,EAAG,OAAO,CAAC,IAAM,KAAO,OAAO,KAAKA,CAAE,GAAM,aAC9C,KAAKA,CAAE,EAAI,KAAKA,CAAE,EAAE,KAAK,IAAI,EAGnC,CACA,OAAAgU,EAAW,UAAY,CACrB,YAAa,SAAqBvL,EAAM,CACtC,IAAIU,EAAgBV,EAAK,cACrB,KAAK,SAAS,gBAChB5I,EAAG,SAAU,WAAY,KAAK,iBAAiB,EAE3C,KAAK,QAAQ,eACfA,EAAG,SAAU,cAAe,KAAK,yBAAyB,EACjDsJ,EAAc,QACvBtJ,EAAG,SAAU,YAAa,KAAK,yBAAyB,EAExDA,EAAG,SAAU,YAAa,KAAK,yBAAyB,CAG9D,EACA,kBAAmB,SAA2BoU,EAAO,CACnD,IAAI9K,EAAgB8K,EAAM,cAEtB,CAAC,KAAK,QAAQ,gBAAkB,CAAC9K,EAAc,QACjD,KAAK,kBAAkBA,CAAa,CAExC,EACA,KAAM,UAAgB,CAChB,KAAK,SAAS,gBAChBlJ,EAAI,SAAU,WAAY,KAAK,iBAAiB,GAEhDA,EAAI,SAAU,cAAe,KAAK,yBAAyB,EAC3DA,EAAI,SAAU,YAAa,KAAK,yBAAyB,EACzDA,EAAI,SAAU,YAAa,KAAK,yBAAyB,GAE3DiU,GAA+B,EAC/BC,GAAgB,EAChB9O,GAAc,CAChB,EACA,QAAS,UAAmB,CAC1BwO,GAAaJ,GAAeD,GAAWE,GAAYI,GAA6BH,GAAkBC,GAAkB,KACpHL,EAAY,OAAS,CACvB,EACA,0BAA2B,SAAmCtL,EAAK,CACjE,KAAK,kBAAkBA,EAAK,EAAI,CAClC,EACA,kBAAmB,SAA2BA,EAAK6G,EAAU,CAC3D,IAAI1J,EAAQ,KACRG,GAAK0C,EAAI,QAAUA,EAAI,QAAQ,CAAC,EAAIA,GAAK,QAC3CzC,GAAKyC,EAAI,QAAUA,EAAI,QAAQ,CAAC,EAAIA,GAAK,QACzC3D,EAAO,SAAS,iBAAiBiB,EAAGC,CAAC,EAOvC,GANAqO,GAAa5L,EAMT6G,GAAY,KAAK,QAAQ,yBAA2BvP,IAAQD,GAAcG,GAAQ,CACpF2U,GAAWnM,EAAK,KAAK,QAAS3D,EAAMwK,CAAQ,EAG5C,IAAIuF,EAAiBpR,EAA2BqB,EAAM,EAAI,EACtDoP,KAAc,CAACI,IAA8BvO,IAAMoO,IAAmBnO,IAAMoO,MAC9EE,IAA8BI,GAA+B,EAE7DJ,GAA6B,YAAY,UAAY,CACnD,IAAIQ,EAAUrR,EAA2B,SAAS,iBAAiBsC,EAAGC,CAAC,EAAG,EAAI,EAC1E8O,IAAYD,IACdA,EAAiBC,EACjBH,GAAgB,GAElBC,GAAWnM,EAAK7C,EAAM,QAASkP,EAASxF,CAAQ,CAClD,EAAG,EAAE,EACL6E,GAAkBpO,EAClBqO,GAAkBpO,EAEtB,KAAO,CAEL,GAAI,CAAC,KAAK,QAAQ,cAAgBvC,EAA2BqB,EAAM,EAAI,IAAM3C,IAA6B,CACxGwS,GAAgB,EAChB,MACF,CACAC,GAAWnM,EAAK,KAAK,QAAShF,EAA2BqB,EAAM,EAAK,EAAG,EAAK,CAC9E,CACF,CACJ,EACSzF,EAASmV,EAAY,CAC1B,WAAY,SACZ,oBAAqB,EACzB,CAAG,CACH,CACA,SAASG,IAAmB,CAC1BZ,EAAY,QAAQ,SAAUa,EAAY,CACxC,cAAcA,EAAW,GAAG,CAC9B,CAAC,EACDb,EAAc,CAAA,CAChB,CACA,SAASW,IAAkC,CACzC,cAAcJ,EAA0B,CAC1C,CACA,IAAIM,GAAapP,GAAS,SAAUiD,EAAK1E,EAASmF,EAAQ6L,EAAY,CAEpE,GAAKhR,EAAQ,OACb,KAAIgC,GAAK0C,EAAI,QAAUA,EAAI,QAAQ,CAAC,EAAIA,GAAK,QAC3CzC,GAAKyC,EAAI,QAAUA,EAAI,QAAQ,CAAC,EAAIA,GAAK,QACzCuM,EAAOjR,EAAQ,kBACfkR,EAAQlR,EAAQ,YAChBW,EAAcvC,EAAyB,EACrC+S,EAAqB,GACvBC,EAGElB,KAAiB/K,IACnB+K,GAAe/K,EACfyL,GAAgB,EAChBX,GAAWjQ,EAAQ,OACnBoR,EAAiBpR,EAAQ,SACrBiQ,KAAa,KACfA,GAAWvQ,EAA2ByF,EAAQ,EAAI,IAGtD,IAAIkM,EAAY,EACZC,EAAgBrB,GACpB,EAAG,CACD,IAAI1T,EAAK+U,EACP/O,EAAOjE,EAAQ/B,CAAE,EACjBqC,EAAM2D,EAAK,IACXzD,EAASyD,EAAK,OACd1D,EAAO0D,EAAK,KACZxD,EAAQwD,EAAK,MACbtD,EAAQsD,EAAK,MACbvD,EAASuD,EAAK,OACdgP,GAAa,OACbC,EAAa,OACbC,GAAclV,EAAG,YACjBmV,GAAenV,EAAG,aAClBgM,EAAQjL,EAAIf,CAAE,EACdoV,GAAapV,EAAG,WAChBqV,GAAarV,EAAG,UACdA,IAAOoE,GACT4Q,GAAatS,EAAQwS,KAAgBlJ,EAAM,YAAc,QAAUA,EAAM,YAAc,UAAYA,EAAM,YAAc,WACvHiJ,EAAaxS,EAAS0S,KAAiBnJ,EAAM,YAAc,QAAUA,EAAM,YAAc,UAAYA,EAAM,YAAc,aAEzHgJ,GAAatS,EAAQwS,KAAgBlJ,EAAM,YAAc,QAAUA,EAAM,YAAc,UACvFiJ,EAAaxS,EAAS0S,KAAiBnJ,EAAM,YAAc,QAAUA,EAAM,YAAc,WAE3F,IAAIsJ,GAAKN,KAAe,KAAK,IAAIxS,EAAQiD,CAAC,GAAKiP,GAAQU,GAAa1S,EAAQwS,KAAgB,KAAK,IAAI5S,EAAOmD,CAAC,GAAKiP,GAAQ,CAAC,CAACU,IACxHG,EAAKN,IAAe,KAAK,IAAI1S,EAASmD,CAAC,GAAKgP,GAAQW,GAAa5S,EAAS0S,KAAiB,KAAK,IAAI9S,EAAMqD,CAAC,GAAKgP,GAAQ,CAAC,CAACW,IAC9H,GAAI,CAAC5B,EAAYqB,CAAS,EACxB,QAAStW,GAAI,EAAGA,IAAKsW,EAAWtW,KACzBiV,EAAYjV,EAAC,IAChBiV,EAAYjV,EAAC,EAAI,CAAA,IAInBiV,EAAYqB,CAAS,EAAE,IAAMQ,IAAM7B,EAAYqB,CAAS,EAAE,IAAMS,GAAM9B,EAAYqB,CAAS,EAAE,KAAO9U,KACtGyT,EAAYqB,CAAS,EAAE,GAAK9U,EAC5ByT,EAAYqB,CAAS,EAAE,GAAKQ,GAC5B7B,EAAYqB,CAAS,EAAE,GAAKS,EAC5B,cAAc9B,EAAYqB,CAAS,EAAE,GAAG,GACpCQ,IAAM,GAAKC,GAAM,KACnBX,EAAqB,GAErBnB,EAAYqB,CAAS,EAAE,IAAM,aAAY,UAAY,CAE/CL,GAAc,KAAK,QAAU,GAC/B5Q,EAAS,OAAO,aAAakQ,EAAU,EAEzC,IAAIyB,GAAgB/B,EAAY,KAAK,KAAK,EAAE,GAAKA,EAAY,KAAK,KAAK,EAAE,GAAKkB,EAAQ,EAClFc,GAAgBhC,EAAY,KAAK,KAAK,EAAE,GAAKA,EAAY,KAAK,KAAK,EAAE,GAAKkB,EAAQ,EAClF,OAAOE,GAAmB,YACxBA,EAAe,KAAKhR,EAAS,QAAQ,WAAW0C,CAAO,EAAGkP,GAAeD,GAAerN,EAAK4L,GAAYN,EAAY,KAAK,KAAK,EAAE,EAAE,IAAM,YAI/IjO,GAASiO,EAAY,KAAK,KAAK,EAAE,GAAIgC,GAAeD,EAAa,CACnE,GAAE,KAAK,CACL,MAAOV,CACjB,CAAS,EAAG,EAAE,IAGVA,GACF,OAASrR,EAAQ,cAAgBsR,IAAkB3Q,IAAgB2Q,EAAgB5R,EAA2B4R,EAAe,EAAK,IAClInB,GAAYgB,EACd,EAAG,EAAE,EAEDc,GAAO,SAAc/M,EAAM,CAC7B,IAAIU,EAAgBV,EAAK,cACvBW,EAAcX,EAAK,YACnBkB,EAASlB,EAAK,OACd+G,EAAiB/G,EAAK,eACtBgN,EAAwBhN,EAAK,sBAC7BiN,EAAqBjN,EAAK,mBAC1BkN,EAAuBlN,EAAK,qBAC9B,GAAKU,EACL,KAAIyM,EAAaxM,GAAeoG,EAChCkG,EAAkB,EAClB,IAAIrH,EAAQlF,EAAc,gBAAkBA,EAAc,eAAe,OAASA,EAAc,eAAe,CAAC,EAAIA,EAChH9K,EAAS,SAAS,iBAAiBgQ,EAAM,QAASA,EAAM,OAAO,EACnEsH,EAAoB,EAChBC,GAAc,CAACA,EAAW,GAAG,SAASvX,CAAM,IAC9CoX,EAAsB,OAAO,EAC7B,KAAK,QAAQ,CACX,OAAQ9L,EACR,YAAaP,CACnB,CAAK,GAEL,EACA,SAASyM,IAAS,CAAC,CACnBA,GAAO,UAAY,CACjB,WAAY,KACZ,UAAW,SAAmB5B,EAAO,CACnC,IAAIhL,EAAoBgL,EAAM,kBAC9B,KAAK,WAAahL,CACpB,EACA,QAAS,SAAiB6M,EAAO,CAC/B,IAAInM,EAASmM,EAAM,OACjB1M,EAAc0M,EAAM,YACtB,KAAK,SAAS,sBAAqB,EAC/B1M,GACFA,EAAY,sBAAqB,EAEnC,IAAI+H,EAAc9N,GAAS,KAAK,SAAS,GAAI,KAAK,WAAY,KAAK,OAAO,EACtE8N,EACF,KAAK,SAAS,GAAG,aAAaxH,EAAQwH,CAAW,EAEjD,KAAK,SAAS,GAAG,YAAYxH,CAAM,EAErC,KAAK,SAAS,WAAU,EACpBP,GACFA,EAAY,WAAU,CAE1B,EACA,KAAMoM,EACR,EACA3W,EAASgX,GAAQ,CACf,WAAY,eACd,CAAC,EACD,SAASE,IAAS,CAAC,CACnBA,GAAO,UAAY,CACjB,QAAS,SAAiBC,EAAO,CAC/B,IAAIrM,EAASqM,EAAM,OACjB5M,EAAc4M,EAAM,YAClBC,EAAiB7M,GAAe,KAAK,SACzC6M,EAAe,sBAAqB,EACpCtM,EAAO,YAAcA,EAAO,WAAW,YAAYA,CAAM,EACzDsM,EAAe,WAAU,CAC3B,EACA,KAAMT,EACR,EACA3W,EAASkX,GAAQ,CACf,WAAY,eACd,CAAC,EAkqBDpS,EAAS,MAAM,IAAIoQ,EAAkB,EACrCpQ,EAAS,MAAMoS,GAAQF,EAAM,ECvyG7B,MAAqBK,WAAqBC,EAAc,CACtD,OAAOC,EAAO,CACZ,MAAM,OAAOA,CAAK,EAClB,KAAK,QAAU,GACf,KAAK,eAAiB,CAAA,EACtB,KAAK,YAAA,CACP,CAEA,UAAU,CACR,IAAItW,EAAK,SAAS,eAAe,yBAAyB,EAC1D6D,EAAS,OAAO7D,EAAG,CACb,UAAW,IACX,cAAe,IACf,MAAQ,GAAM,KAAK,WAAW,CAAC,CAAA,CAChC,CACP,CAEA,SAAU,CACR,OACE,EAAC,MAAA,CAAI,UAAU,yCAAA,EACb,EAAC,OAAI,UAAU,aAEb,EAAC,MAAA,CAAI,MAAO,CAAC,cAAe,MAAA,GAC1B,EAAC1C,GAAA,CAAO,UAAW,SAAU,QAAS,IAAM,IAAI,MAAM,KAAKJ,EAAkB,CAAA,EAC1E,IAAI,WAAW,MAAM,uCAAuC,CAC/D,CACF,EAEA,EAAC,MAAG,GAAG,0BAA0B,MAAO,CAAC,QAAS,MAAO,cAAe,MAAA,EAAS,SAAU,KAAK,SAAS,KAAK,IAAI,CAAA,EAC/G,KAAK,eAAe,IAAKU,KAErB,KAAA,CAAG,OAAQA,EAAmB,GAAA,EAAM,MAAO,CAAC,UAAW,MAAO,WAAY,iBAAgB,EACxFF,GAAmB,UAAU,CAAE,mBAAAE,CAAA,CAAoB,CACtD,CAEH,CACH,CAEF,CACF,CAEJ,CAEA,WAAWL,EAAE,CACX,MAAM2L,EAAW3L,EAAE,SACb0L,EAAW1L,EAAE,SAEnB,GAAG2L,IAAWD,EAAS,CACrB,MAAMrF,EAAWrG,EAAE,KAAK,SAClBgZ,EAAiB,CAAA,EAEvB,QAAQ/X,EAAE,EAAEA,EAAEoF,EAAS,OAAOpF,IAAI,CAChC,MAAMyH,EAAQrC,EAASpF,CAAC,EAClBgY,EAAS,EAAEvQ,CAAK,EAAE,KAAK,QAAQ,EAErCsQ,EAAeC,CAAM,EAAIhY,CAC3B,CAEA,IAAI,QAAQ,CACV,IAAK,GAAG,IAAI,MAAM,UAAU,QAAQ,CAAC,wBACrC,OAAQ,OACR,KAAM,CAAE,eAAA+X,CAAA,CAAe,CACxB,CACH,CACF,CAEA,aAAaE,EAAS,CACpB,OAAA,EAAG,KAAK,MAAM,KAAK,eAAgBA,CAAO,EAC1C,EAAE,OAAA,EACKA,CACT,CAEA,aAAc,CACZ,OAAO,IAAI,MACR,KAAK,gBAAgB,EACrB,MAAM,IAAM,CAAC,CAAC,EACd,KAAK,KAAK,aAAa,KAAK,IAAI,CAAC,CACtC,CACF,CCnFe,MAAMC,WAAmBC,EAAM,CAAA,CAC9C,OAAO,OAAOD,GAAW,UAAW,CAClC,GAAIC,GAAM,UAAU,IAAI,EACxB,KAAMA,GAAM,UAAU,MAAM,EAC5B,MAAOA,GAAM,UAAU,OAAO,EAC9B,KAAMA,GAAM,UAAU,MAAM,CAC9B,CAAC,ECLD,IAAI,aAAa,IAAI,iCAAkC,IAAM,CAC3D,IAAI,MAAM,OAAO,eAAiBD,GAClC,IAAI,cAAc,IAAI,gCAAgC,EAAE,aAAaN,EAAY,CACnF,CAAC", "x_google_ignoreList": [3]}