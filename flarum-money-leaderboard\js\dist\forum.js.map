{"version": 3, "file": "forum.js", "mappings": "kCAAAA,EAAOC,QAAUC,OAAOC,WAAW,6BCC/BC,EAA2B,GAG/B,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaN,QAGrB,IAAID,EAASI,EAAyBE,GAAY,CAGjDL,QAAS,IAOV,OAHAQ,EAAoBH,GAAUN,EAAQA,EAAOC,QAASI,GAG/CL,EAAOC,QCpBfI,EAAoBK,EAAKV,IACxB,IAAIW,EAASX,GAAUA,EAAOY,WAC7B,IAAOZ,EAAiB,QACxB,IAAM,EAEP,OADAK,EAAoBQ,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GCLRN,EAAoBQ,EAAI,CAACZ,EAASc,KACjC,IAAI,IAAIC,KAAOD,EACXV,EAAoBY,EAAEF,EAAYC,KAASX,EAAoBY,EAAEhB,EAASe,IAC5EE,OAAOC,eAAelB,EAASe,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3EX,EAAoBY,EAAI,CAACK,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFlB,EAAoBsB,EAAK1B,IACH,oBAAX2B,QAA0BA,OAAOC,aAC1CX,OAAOC,eAAelB,EAAS2B,OAAOC,YAAa,CAAEC,MAAO,WAE7DZ,OAAOC,eAAelB,EAAS,aAAc,CAAE6B,OAAO,K,mCCLvD,MAAM,EAA+B5B,OAAO6B,KAAKC,OAAO,a,aCAxD,MAAM,EAA+B9B,OAAO6B,KAAKC,OAAe,OCA1D,EAA+B9B,OAAO6B,KAAKC,OAAO,iBCAlD,EAA+B9B,OAAO6B,KAAKC,OAAO,wB,aCAxD,MAAM,EAA+B9B,OAAO6B,KAAKC,OAAO,yB,aCAxD,MAAM,EAA+B9B,OAAO6B,KAAKC,OAAO,oC,aCAxD,MAAM,EAA+B9B,OAAO6B,KAAKC,OAAO,4B,aCAxD,MAAM,EAA+B9B,OAAO6B,KAAKC,OAAO,oC,aCAzC,SAASC,EAAgBhB,EAAGiB,GAMzC,OALAD,EAAkBf,OAAOiB,gBAAkB,SAAyBlB,EAAGiB,GAErE,OADAjB,EAAEmB,UAAYF,EACPjB,GAGFgB,EAAgBhB,EAAGiB,GCLb,SAASG,EAAeC,EAAUC,GAC/CD,EAASd,UAAYN,OAAOsB,OAAOD,EAAWf,WAC9Cc,EAASd,UAAUiB,YAAcH,EACjCH,EAAeG,EAAUC,GCJ3B,MAAM,EAA+BrC,OAAO6B,KAAKC,OAAO,mB,aCAxD,MAAM,EAA+B9B,OAAO6B,KAAKC,OAAO,4B,aCAxD,MAAM,EAA+B9B,OAAO6B,KAAKC,OAAO,+B,aCAxD,MAAM,EAA+B9B,OAAO6B,KAAKC,OAAO,qB,aCAxD,MAAM,EAA+B9B,OAAO6B,KAAKC,OAAkB,U,aCAnE,MAAM,EAA+B9B,OAAO6B,KAAKC,OAAO,mB,aCAxD,MAAM,EAA+B9B,OAAO6B,KAAKC,OAAO,kB,aCAxD,MAAM,EAA+B9B,OAAO6B,KAAKC,OAAO,oB,aCKnCU,EAAAA,SAAAA,G,4EACnBC,KAAA,WACE,IAiBIC,EAAgBC,EAjBpB,EAAqCC,KAAKC,MAAnCC,EAAP,EAAOA,oBAAoBC,EAA3B,EAA2BA,OACrBC,EAAYC,IAAIC,MAAMC,UAAU,8BAAgC,UAChEC,EAAaN,EAAoBK,UAAU,SAC3CE,EAAYL,EAAUM,QAAQ,UAAWF,GAC3CG,EAAc,qCACdC,EAAY,kCAahB,GAXY,IAATT,EACDQ,EAAc,qCACE,IAATR,EACPQ,EAAc,uCACE,IAATR,EACPQ,EAAc,uCAEdC,EAAY,oCAIX,4BAA6BxD,OAAOC,WAAW,CAChD,IAAQwD,EAAeC,EAAQ,KAAvBD,WACRf,EAAkBe,EAAWf,gBAC7BC,EAAoBc,EAAWd,kBAGjC,OACE,SAAKgB,UAAU,qCACb,SAAKC,MAAM,kCACT,SAAKA,MAAOJ,GAAYT,GACxB,SAAKa,MAAOL,GACV,OAAGK,MAAM,oBAGb,SAAKA,MAAM,kCACT,EAAC,IAAD,CAAMC,KAAMZ,IAAIa,MAAMC,KAAKjB,GAAsBa,UAAU,sBAAsBK,MAAM,8BACpFtB,EAAgBA,EAAgBI,GAAqBmB,GAAAA,CAAOnB,GAD/D,IACsFH,EAAkBA,EAAkBG,GAAqBoB,GAAAA,CAASpB,KAG1J,SAAKc,MAAM,mCACRP,K,EAxCUb,CAAiC2B,KCGjCC,EAAAA,SAAAA,G,oFACnBC,OAAA,SAAOC,GACL,YAAMD,OAAN,UAAaC,GACb1B,KAAK2B,SAAU,EACf3B,KAAK4B,aAAc,EACnB5B,KAAK6B,qBAAuB,GAC5B7B,KAAK8B,eAAiB,EAEtB9B,KAAK+B,aAAe1B,IAAIC,MAAMC,UAAU,4BAA8B,GACtEP,KAAKgC,cAAgB3B,IAAIC,MAAMC,UAAU,6BAA+B,GACxEP,KAAKiC,cAAgB5B,IAAIC,MAAMC,UAAU,6BAA+B,GACxEP,KAAKkC,YAAY,EAAElC,KAAKgC,gB,EAG1BG,SAAA,SAAST,GACP,YAAMS,SAAN,UAAeT,GACf,IAAMU,EAAY/B,IAAIgC,WAAWC,MAAM,wDACvCjC,IAAIkC,SAASH,GACb/B,IAAImC,cAAc,GAClBC,EAAE,kCAAkCC,KAAKN,I,EAG3CvC,KAAA,WAAO,IACD8B,EADC,OAEDxB,EAAS,EAMb,OAJGH,KAAK2B,UACNA,EAAUgB,IAAAA,UAA2B,CAACC,KAAM,WAI5C,SAAK7B,UAAU,wBACZ8B,IAAAA,UAAAA,OAED,SAAK9B,UAAU,aACb,SAAKA,UAAU,oBACb,SAAKA,UAAU,yBACb,YAAK+B,GAAAA,CAAUD,IAAAA,UAAAA,eAAmCE,aAGpD,SAAK/B,MAAM,6BACT,SAAKA,MAAM,6BACRX,IAAIgC,WAAWC,MAAM,yDAGxB,SAAKtB,MAAM,8BACT,SAAKA,MAAM,kCAAkCX,IAAIgC,WAAWC,MAAM,wDAClE,SAAKtB,MAAM,kCAAkCX,IAAIgC,WAAWC,MAAM,wDAClE,SAAKtB,MAAM,mCAAmCX,IAAIgC,WAAWC,MAAM,0DAGrE,QAAItB,MAAM,wBACPhB,KAAK6B,qBAAqBmB,KAAI,SAAC9C,GAG9B,OAFAC,IAGE,QAAIa,MAAM,6BACPpB,EAAyBqD,UAAU,CAAE/C,oBAAAA,EAAqBC,OAAAA,UAMjEH,KAAK2B,SAA8C,IAAnC3B,KAAK6B,qBAAqBqB,QAC1C,aACE,SAAK9B,MAAM,uGAAuGf,IAAIgC,WAAWC,MAAM,qDAIzIX,GAAW3B,KAAKmD,kBAChB,SAAK/B,MAAM,kCACT,EAAC,IAAD,CAAQL,UAAW,yBAA0BqC,SAAUpD,KAAK2B,QAASA,QAAS3B,KAAK2B,QAAS0B,QAAS,kBAAM,EAAKC,aAC7GjD,IAAIgC,WAAWC,MAAM,yDAK3BX,GAAW,SAAKZ,UAAU,6BAA6BY,Q,EAQpEwB,eAAA,WACE,OAAOnD,KAAK4B,aAAe5B,KAAK+B,aAAa/B,KAAK8B,gB,EAGpDwB,SAAA,WACEtD,KAAK2B,SAAU,EACf3B,KAAKkC,YAAYlC,KAAK6B,qBAAqBqB,OAAOlD,KAAKiC,gB,EAGzDsB,aAAA,SAAaC,GAMX,OALAxD,KAAK4B,cAAgB4B,EAAQC,QAAQC,SAAWF,EAAQC,QAAQC,MAAMC,KACtE,GAAGC,KAAKC,MAAM7D,KAAK6B,qBAAsB2B,GACzCxD,KAAK2B,SAAU,EACfmC,EAAEC,SAEKP,G,EAGTtB,YAAA,SAAY8B,EAAYC,GACtB,QADsC,IAA5BD,IAAAA,EAAS,QAAmB,IAAhBC,IAAAA,EAAY,IAC/BjE,KAAK+B,eAAe/B,KAAK8B,eAA5B,CAIA,IAAIoC,EAAQD,EASZ,OAPGjE,KAAK+B,aAAa/B,KAAK8B,eAAemC,IACvCC,EAAQlE,KAAK+B,aAAa/B,KAAK8B,eAC/B9B,KAAK8B,eAAiB9B,KAAK+B,cAG7B/B,KAAK8B,gBAAgBmC,EAEd5D,IAAI8D,MACRC,KAAK,mBAAoB,CACxBC,KAAM,CACJL,OAAAA,EACAE,MAAAA,KAJC,OAOE,eACNI,KAAKtE,KAAKuD,aAAagB,KAAKvE,S,EA7HdwB,CAAkCgD,KCHvDnE,IAAAA,aAAAA,IAAqB,2BAA2B,WAC9CA,IAAAA,OAAAA,iBAAiC,CAC/BoE,KAAM,oBACNxB,UAAWzB,ICAbkD,EAAAA,EAAAA,QAAOC,IAAAA,UAA2B,SAAS,SAAUC,GAGnD,GAF6BvE,IAAAA,MAAAA,UAAoB,wBAEzB,CACtB,IAAIwE,EAAgCxE,IAAAA,MAAAA,UAAoB,iCAEtB,GAA/BwE,GCRPH,EAAAA,EAAAA,QAAO7B,IAAAA,UAAqB,YAAY,SAAU+B,GAChD,IAAME,EAAuBzE,IAAAA,MAAAA,UAAoB,wBAUjD,OARAuE,EAAMG,IACJ,mBACA,EAAC,IAAD,CAAYC,KAAMF,EAAsB7D,KAAMZ,IAAAA,MAAU,qBACrDA,IAAAA,WAAAA,MAAqB,mDAExB,IAGKuE,KDDmC,GAA/BC,IEVbH,EAAAA,EAAAA,QAAOO,IAAAA,UAA2B,SAAS,SAAUL,GACnD,IAAME,EAAuBzE,IAAAA,MAAAA,UAAoB,wBAEjDuE,EAAMG,IACJ,mBACA,EAAC,IAAD,CACEhE,UAAU,sBACVK,MAAM,aACNiC,QAAS,WACP6B,OAAOC,SAASlE,KAAOZ,IAAAA,MAAU,qBAEnC2E,KAAMF,GAELzE,IAAAA,WAAAA,MAAqB,mDAExB,e", "sources": ["webpack://@wusong8899/flarum-daily-check-in/external assign \"flarum.extensions['wusong8899-decoration-store']\"", "webpack://@wusong8899/flarum-daily-check-in/webpack/bootstrap", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/compat get default export", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/define property getters", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/hasOwnProperty shorthand", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/make namespace object", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['forum/app']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['extend']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/extend']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/IndexPage']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/LinkButton']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['forum/components/HeaderSecondary']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/components/Button']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['forum/components/SessionDropdown']\"", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Page']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/helpers/listItems']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/LoadingIndicator']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Button']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['Component']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['components/Link']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['helpers/avatar']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['helpers/username']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/MoneyLeaderboardListItem.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/MoneyLeaderboardIndexPage.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/index.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/attachMenu.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/addSidebarMenu.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/addTopRightMenu.js"], "sourcesContent": ["module.exports = flarum.extensions['wusong8899-decoration-store'];", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['forum/app'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['extend'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/extend'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/IndexPage'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/LinkButton'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['forum/components/HeaderSecondary'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/Button'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['forum/components/SessionDropdown'];", "export default function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}", "import setPrototypeOf from \"./setPrototypeOf.js\";\nexport default function _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  setPrototypeOf(subClass, superClass);\n}", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Page'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/helpers/listItems'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/LoadingIndicator'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Button'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['Component'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['components/Link'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['helpers/avatar'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['helpers/username'];", "import Component from \"flarum/Component\";\nimport Link from \"flarum/components/Link\";\nimport avatar from \"flarum/helpers/avatar\";\nimport username from \"flarum/helpers/username\";\n\nexport default class MoneyLeaderboardListItem extends Component {\n  view() {\n    const {leaderboardListItem,rankID} = this.attrs;\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n    const moneyValue = leaderboardListItem.attribute(\"money\");\n    const moneyText = moneyName.replace('[money]', moneyValue);\n    let trophyClass = \"MoneyLeaderboardListItemTrophyNone\";\n    let rankClass = \"MoneyLeaderboardListItemRankTop\";\n\n    if(rankID===1){\n      trophyClass = \"MoneyLeaderboardListItemTrophyGold\";\n    }else if(rankID===2){\n      trophyClass = \"MoneyLeaderboardListItemTrophySilver\";\n    }else if(rankID===3){\n      trophyClass = \"MoneyLeaderboardListItemTrophyBronze\";\n    }else{\n      rankClass = \"MoneyLeaderboardListItemRankLower\";\n    }\n\n    let avatarWithFrame,usernameWithColor;\n    if('wusong8899-decoration-store' in flarum.extensions){\n      const { components } = require('@wusong8899-decoration-store');\n      avatarWithFrame = components.avatarWithFrame;\n      usernameWithColor = components.usernameWithColor;\n    }\n\n    return (\n      <div className=\"MoneyLeaderboardListItemContainer\">\n        <div class=\"MoneyLeaderboardListHeaderRank\">\n          <div class={rankClass}>{rankID}</div>\n          <div class={trophyClass}>\n            <i class=\"fas fa-trophy\"></i>\n          </div>\n        </div>\n        <div class=\"MoneyLeaderboardListHeaderUser\">\n          <Link href={app.route.user(leaderboardListItem)} className=\"transferHistoryUser\" style=\"color:var(--heading-color)\">\n            {avatarWithFrame?avatarWithFrame(leaderboardListItem):avatar(leaderboardListItem)} {usernameWithColor?usernameWithColor(leaderboardListItem):username(leaderboardListItem)}\n          </Link>\n        </div>\n        <div class=\"MoneyLeaderboardListHeaderMoney\">\n          {moneyText}\n        </div>\n      </div>\n    );\n  }\n}\n", "import Page from 'flarum/components/Page';\nimport IndexPage from 'flarum/components/IndexPage';\nimport listItems from 'flarum/common/helpers/listItems';\nimport LoadingIndicator from \"flarum/components/LoadingIndicator\";\nimport Button from 'flarum/components/Button';\n\nimport MoneyLeaderboardListItem from \"./MoneyLeaderboardListItem\";\n\nexport default class MoneyLeaderboardIndexPage extends Page {\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.loading = true;\n    this.moreResults = false;\n    this.moneyLeaderboardList = [];\n    this.totalLoadCount = 0;\n\n    this.maxLoadCount = app.forum.attribute(\"leaderboardMaxLoadCount\") || 50;\n    this.initloadCount = app.forum.attribute(\"leaderboardInitLoadCount\") || 20;\n    this.loadMoreCount = app.forum.attribute(\"leaderboardLoadMoreCount\") || 10;\n    this.loadResults(0,this.initloadCount);\n  }\n\n  oncreate(vnode) {\n    super.oncreate(vnode);\n    const titleText = app.translator.trans(\"wusong8899-money-leaderboard.forum.leaderboard-list-title\");\n    app.setTitle(titleText);\n    app.setTitleCount(0);\n    $(\".item-nav button .Button-label\").text(titleText);\n  }\n\n  view() {\n    let loading;\n    let rankID = 0;\n\n    if(this.loading){\n      loading = LoadingIndicator.component({size: \"large\"});\n    }\n\n    return (\n      <div className=\"MoneyLeaderboardPage\">\n        {IndexPage.prototype.hero()}\n\n        <div className=\"container\">\n          <div className=\"sideNavContainer\">\n            <nav className=\"IndexPage-nav sideNav\">\n              <ul>{listItems(IndexPage.prototype.sidebarItems().toArray())}</ul>\n            </nav>\n\n            <div class=\"MoneyLeaderboardContainer\">\n              <div class=\"MoneyLeaderboardListTitle\">\n                {app.translator.trans(\"wusong8899-money-leaderboard.forum.leaderboard-list-title\")}\n              </div>\n\n              <div class=\"MoneyLeaderboardListHeader\">\n                <div class=\"MoneyLeaderboardListHeaderRank\">{app.translator.trans(\"wusong8899-money-leaderboard.forum.leaderboard-list-rank\")}</div>\n                <div class=\"MoneyLeaderboardListHeaderUser\">{app.translator.trans(\"wusong8899-money-leaderboard.forum.leaderboard-list-user\")}</div>\n                <div class=\"MoneyLeaderboardListHeaderMoney\">{app.translator.trans(\"wusong8899-money-leaderboard.forum.leaderboard-list-money\")}</div>\n              </div>\n\n              <ul class=\"MoneyLeaderboardList\">\n                {this.moneyLeaderboardList.map((leaderboardListItem) => {\n                  rankID++;\n\n                  return (\n                    <li class=\"MoneyLeaderboardListItems\">\n                      {MoneyLeaderboardListItem.component({ leaderboardListItem, rankID })}\n                    </li>\n                  );\n                })}\n              </ul>\n\n              {!this.loading && this.moneyLeaderboardList.length===0 && (\n                <div>\n                  <div style=\"font-size:1.4em;color: var(--muted-more-color);text-align: center;height: 300px;line-height: 100px;\">{app.translator.trans(\"wusong8899-transfer-money.forum.transfer-list-empty\")}</div>\n                </div>\n              )}\n\n              {!loading && this.hasMoreResults() && (\n                <div style=\"text-align:center;padding:20px\">\n                  <Button className={'Button Button--primary'} disabled={this.loading} loading={this.loading} onclick={() => this.loadMore()}>\n                    {app.translator.trans('wusong8899-money-leaderboard.forum.leaderboard-load-more')}\n                  </Button>\n                </div>\n              )}\n\n              {loading && <div className=\"MoneyLeaderboard-loadMore\">{loading}</div>}\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  hasMoreResults() {\n    return this.moreResults && this.maxLoadCount>this.totalLoadCount;\n  }\n\n  loadMore() {\n    this.loading = true;\n    this.loadResults(this.moneyLeaderboardList.length,this.loadMoreCount);\n  }\n\n  parseResults(results) {\n    this.moreResults = !!results.payload.links && !!results.payload.links.next;\n    [].push.apply(this.moneyLeaderboardList, results);\n    this.loading = false;\n    m.redraw();\n    \n    return results;\n  }\n\n  loadResults(offset = 0, loadCount = 20) {\n    if(this.maxLoadCount===this.totalLoadCount){\n      return;\n    }\n\n    let limit = loadCount;\n\n    if(this.maxLoadCount<this.totalLoadCount+loadCount){\n      limit = this.maxLoadCount-this.totalLoadCount;\n      this.totalLoadCount = this.maxLoadCount;\n    } \n\n    this.totalLoadCount+=loadCount;\n\n    return app.store\n      .find(\"moneyLeaderboard\", {\n        page: {\n          offset,\n          limit\n        },\n      })\n      .catch(() => {})\n      .then(this.parseResults.bind(this));\n  }\n}\n", "import app from 'flarum/forum/app';\r\nimport { extend } from 'flarum/extend';\r\nimport attachMenu from './attachMenu';\r\nimport MoneyLeaderboardIndexPage from './components/MoneyLeaderboardIndexPage';\r\n\r\napp.initializers.add('wusong8899-money-leaderboard', () => {\r\n  app.routes['moneyLeaderboard'] = {\r\n    path: '/moneyLeaderboard',\r\n    component: MoneyLeaderboardIndexPage,\r\n  };\r\n\r\n  attachMenu();\r\n});", "import app from 'flarum/forum/app';\nimport { extend } from 'flarum/common/extend';\n\nimport addSidebarMenu from './addSidebarMenu';\nimport addTopRightMenu from './addTopRightMenu';\nimport SessionDropdown from 'flarum/forum/components/SessionDropdown';\n\nexport default function () {\n  extend(SessionDropdown.prototype, 'items', function (items) {\n    const allowViewLeaderboard = app.forum.attribute('allowViewLeaderbaord');\n\n    if(allowViewLeaderboard){\n      let moneyLeaderBoardEntryPosition = app.forum.attribute(\"moneyLeaderBoardEntryPosition\");\n\n      if(moneyLeaderBoardEntryPosition==0){\n        addSidebarMenu();\n      }else if(moneyLeaderBoardEntryPosition==1){\n        addTopRightMenu();\n      }\n    }\n  });\n}", "import { extend } from 'flarum/extend';\nimport app from 'flarum/forum/app';\nimport IndexPage from 'flarum/components/IndexPage';\nimport LinkButton from 'flarum/components/LinkButton';\n\nexport default function addSidebarMenu() {\n  extend(IndexPage.prototype, 'navItems', function (items) {\n    const moneyLeaderBoardIcon = app.forum.attribute(\"moneyLeaderBoardIcon\");\n\n    items.add(\n      'MoneyLeaderboard',\n      <LinkButton icon={moneyLeaderBoardIcon} href={app.route('moneyLeaderboard')}>\n        {app.translator.trans('wusong8899-money-leaderboard.forum.leaderboard-name')}\n      </LinkButton>,\n      15\n    );\n\n    return items;\n  });\n}\n", "import app from 'flarum/forum/app';\nimport { extend } from 'flarum/common/extend';\nimport HeaderSecondary from 'flarum/forum/components/HeaderSecondary';\nimport Button from 'flarum/common/components/Button';\n\nexport default function () {\n  extend(HeaderSecondary.prototype, 'items', function (items) {\n    const moneyLeaderBoardIcon = app.forum.attribute(\"moneyLeaderBoardIcon\");\n\n    items.add(\n      'MoneyLeaderboard',\n      <Button\n        className=\"Button Button--flat\"\n        style=\"width:36px\"\n        onclick={() => {\n          window.location.href = app.route('moneyLeaderboard');\n        }}\n        icon={moneyLeaderBoardIcon}\n      >\n        {app.translator.trans('wusong8899-money-leaderboard.forum.leaderboard-name')}\n      </Button>,\n      15\n    );\n  });\n}\n"], "names": ["module", "exports", "flarum", "extensions", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "__webpack_modules__", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "r", "Symbol", "toStringTag", "value", "core", "compat", "_setPrototypeOf", "p", "setPrototypeOf", "__proto__", "_inherits<PERSON><PERSON>e", "subClass", "superClass", "create", "constructor", "MoneyLeaderboardListItem", "view", "avatar<PERSON><PERSON><PERSON><PERSON><PERSON>", "usernameWithColor", "this", "attrs", "leaderboardListItem", "rankID", "moneyName", "app", "forum", "attribute", "moneyValue", "moneyText", "replace", "trophyClass", "rankClass", "components", "require", "className", "class", "href", "route", "user", "style", "avatar", "username", "Component", "MoneyLeaderboardIndexPage", "oninit", "vnode", "loading", "moreResults", "moneyLeaderboardList", "totalLoadCount", "maxLoad<PERSON>ount", "initloadCount", "loadMoreCount", "loadResults", "oncreate", "titleText", "translator", "trans", "setTitle", "setTitleCount", "$", "text", "LoadingIndicator", "size", "IndexPage", "listItems", "toArray", "map", "component", "length", "hasMoreResults", "disabled", "onclick", "loadMore", "parseResults", "results", "payload", "links", "next", "push", "apply", "m", "redraw", "offset", "loadCount", "limit", "store", "find", "page", "then", "bind", "Page", "path", "extend", "SessionDropdown", "items", "moneyLeaderBoardEntryPosition", "moneyLeaderBoardIcon", "add", "icon", "HeaderSecondary", "window", "location"], "sourceRoot": ""}