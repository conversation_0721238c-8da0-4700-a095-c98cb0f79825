(function(d,r,a,l,g,c,s,n){"use strict";class u extends g{static isDismissibleViaBackdropClick=!1;static isDismissibleViaCloseButton=!0;oninit(t){super.oninit(t),this.biddingRankListItem=this.attrs.biddingRankListItem,this.loading=!1}className(){return"Modal--small"}title(){return app.translator.trans("wusong8899-bidding-rank.admin.delete-confirmation")}content(){return m("div",{className:"Modal-body"},m("div",{className:"Form-group",style:"text-align: center;"},a.component({className:"Button Button--primary",type:"submit",loading:this.loading},app.translator.trans("wusong8899-bidding-rank.admin.confirm"))," ",a.component({className:"Button",loading:this.loading,onclick:()=>{this.hide()}},app.translator.trans("wusong8899-bidding-rank.admin.cancel"))))}onsubmit(t){t.preventDefault(),this.loading=!0,this.biddingRankListItem.save({isDelete:1}).then(()=>{location.reload(),this.loading=!1})}}class p extends l{view(){const{biddingRankListItem:t,_rankID:i}=this.attrs,h=t.attribute("bid"),k=t.fromUser(),f=t.content(),R=t.title();return m("div",{className:"biddingRankSettingContainer"},m("div",{style:"float:right"},a.component({style:"font-weight:bold;",className:"Button Button--danger",onclick:()=>{this.deleteItem(t)}},app.translator.trans("wusong8899-bidding-rank.admin.delete"))),m("div",null,m("b",null,app.translator.trans("wusong8899-bidding-rank.admin.bidding-rank-list-username"),": "),c(k)," | ",m("b",null,app.translator.trans("wusong8899-bidding-rank.admin.bidding-rank-list-title"),": "),R," | ",m("b",null,app.translator.trans("wusong8899-bidding-rank.admin.bidding-rank-list-bid"),": "),h),m("div",null,m("b",null,app.translator.trans("wusong8899-bidding-rank.admin.bidding-rank-list-content"),": "),f))}deleteItem(t){app.modal.show(u,{biddingRankListItem:t})}}class b extends d{oninit(t){super.oninit(t),this.loading=!0,this.moreResults=!1,this.biddingRankList=[],this.loadResults()}content(){let t;return this.loading&&(t=r.component({size:"large"})),m("div",{className:"ExtensionPage-settings FlarumBadgesPage"},m("div",{className:"container"},m("ul",{style:"padding:0px;list-style-type: none;"},this.biddingRankList.map(i=>m("li",{itemID:i.id(),style:"margin-top:5px;background: var(--body-bg);"},p.component({biddingRankListItem:i})))),!this.loading&&this.biddingRankList.length===0&&m("div",null,m("div",{style:"font-size:1.4em;color: var(--muted-more-color);text-align: center;line-height: 100px;"},s.translator.trans("wusong8899-bidding-rank.forum.list-empty"))),!t&&this.hasMoreResults()&&m("div",{style:"text-align:center;padding:20px"},m(a,{className:"Button Button--primary",disabled:this.loading,loading:this.loading,onclick:()=>this.loadMore()},s.translator.trans("wusong8899-bidding-rank.admin.list-load-more"))),t&&m("div",{className:"BiddingRankContent-loadMore"},t)))}hasMoreResults(){return this.moreResults}loadMore(){this.loading=!0,this.loadResults(this.biddingRankList.length)}parseResults(t){return this.moreResults=!!t.payload.links&&!!t.payload.links.next,[].push.apply(this.biddingRankList,t),s.store.find("biddingRankHistory").catch(()=>{}).then(i=>(this.hasBid=i.length>0,this.hasBid&&(this.biddingRankHistoryData=i[0]),this.loading=!1,m.redraw(),t))}loadResults(t=0){return s.store.find("biddingRankList",{page:{offset:t}}).catch(()=>{}).then(this.parseResults.bind(this))}}class o extends n{}Object.assign(o.prototype,{id:n.attribute("id"),title:n.attribute("title"),content:n.attribute("content"),user_id:n.attribute("user_id"),url:n.attribute("url"),bid:n.attribute("bid"),fromUser:n.hasOne("fromUser")}),app.initializers.add("wusong8899-client1-bidding-rank-content",()=>{app.store.models.biddingRankList=o,app.extensionData.for("wusong8899-bidding-rank-content").registerPage(b)})})(flarum.core.compat["components/ExtensionPage"],flarum.core.compat["components/LoadingIndicator"],flarum.core.compat["components/Button"],flarum.core.compat.Component,flarum.core.compat["components/Modal"],flarum.core.compat["helpers/username"],flarum.core.compat["admin/app"],flarum.core.compat.Model);
//# sourceMappingURL=admin.js.map

module.exports={};