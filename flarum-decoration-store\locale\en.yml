wusong8899-decoration-store:
  lib:
    confirm: Confirm
    close: Close
    cancel: Cancel
    ok: OK
    none: None
    item-type: Item Type
    item-type-all: All Item Type
    item-type-avatarFrame: Avatar Frame
    item-type-profileBackground: Profile Background
    item-type-usernameColor: Username Color
    item-label: Item Label
    item-label-owned: Owned
    item-label-recommend: Recommend
    item-label-popular: Popular
    item-title: Item Name
    item-desc: Item Description
    item-cost: Cost
    item-cost-free: Free
    item-amount: Stock
    item-stock-remaining: Stock Remaining
    item-sold: Sales
    item-discount-ribbon: "{discount}% OFF"
    item-discount-days-text: "Discount duration: {days} days"
    item-discount-days-left: " ({days} days left)"
    item-purchase-type: Purchase Type
    item-purchase-type-all: All Purchase Type
    item-purchase-type-onetime: One time purchase
    item-purchase-type-monthly: Monthly subscription
    item-purchase-type-yearly: Yearly subscription
    list-load-more: Load More
    list-empty: There is no content
    save-error: Save Error
    save-error-item-expired: Item expired
    delete-error: Delete Error
    submit-error-field-empty: You need fill the required value
  forum:
    navigate: Navigate
    display-name: Display Name
    display-name-default: Decoration Store
    my-decoration: My Decoration
    view-shopping-cart: Shopping Cart
    processing: Processing...
    user-money-amount: You have
    item-subscribe: Subscribe
    item-unsubscribe: Unsubscribe
    item-soldout: Item sold out
    item-expired: Unsubscribed
    item-already-have: Item already purchased
    item-property-style-text-effect: Preview Text
    item-purchase-status-button-0: Equip Item
    item-purchase-status-button-1: Unequip Item
    item-purchase-title: Item name
    item-purchase-purchase-type: Purchase type
    item-purchase-cost: Purchase cost
    item-purchase-subscription-cost: Subscription cost
    item-purchase-item-type: Item type
    item-purchase-item-type-avatarFrame: Avatar Frame
    item-purchase-item-type-profileBackground: Profile Background
    item-purchase-item-type-usernameColor: Username Color
    item-purchase-item-status: Item status
    item-purchase-item-status-0: Not in use
    item-purchase-item-status-1: In use
    item-purchase-subscribe-item-status-0: Subscribing. Not in use
    item-purchase-subscribe-item-status-1: Subscribing. In use
    item-purchase-date: Purchse date
    item-status-to-0-confirmation: Are you sure to unequip this item?
    item-status-to-1-confirmation: Are you sure to equip this item?
    item-subscription-deduction-days: "Subscription fee deduction in {days} days"
    equipment-modal-title: Item List
    shopping-cart: Shopping Cart
    shopping-cart-close: Close
    purchase-history: Purchase History
    purchase-confirmation: Purchase Confirmation
    purchase-subscribe-confirmation: "Are you sure to pay {cost} to subscribe this item?"
    purchase-unsubscribe-confirmation: Are you sure to unsubscribe this item?
    purchase-success: Purchase Success
    purchase-add-cart: Add to cart
    purchase-now: Go Shopping
    purchase-error: Purchase Error
    purchase-error-insufficient-fund: Insufficient Fund
    purchase-error-item-alreay-have: You already have this item
    notification-receive-subscription: Subscription fee deduction
    notifications:
      subscription-expired: "Your subscription for {title} has expired"
      subscription-expired-excerpt: "Your don't have {cost} to subscribe"
      subscription-fee-deduction: "Subscription deduction fee for {title}"
      subscription-fee-deduction-excerpt: "Cost {cost}"
  admin:
    item-type-selection: Select item type
    data-add: Add
    data-save: Save
    item-add: Add Item
    item-edit: Edit
    item-activate: Activate
    item-deactivate: Deactivate
    item-delete: Delete
    item-id: ID
    item-discount: Discount
    item-discount-none: None
    item-discount-days: Discount Duration (days)
    item-filter-all: All
    item-activate-status: Activation Status
    item-activate-status-all: All Activation Status
    item-activate-status-down: Deactivated
    item-activate-status-up: Activated
    item-property-style: Style
    item-property-style-help-text: You can add your own text style in CSS, and enter the style name in the input below.
    item-property-style-text-effect: Preview Text
    item-property-style-select-from-gallery: Build-in Style
    item-property-image: Image
    item-property-image-upload: Upload
    item-property-image-select-from-gallery: Build-in Image
    item-assigned-at: Date
    item-activate-confirmation: Are you sure to activate this item?
    item-deactivate-confirmation: Are you sure to deactivate this item?
    item-delete-confirmation: Are you sure to delete this item?
    gallery-image-in-use: "{count} item is using"
    settings:
      display-name: Display Name
      display-name-default: Decoration Store
      timezone: Timezone
      timezone-help: Set the timezone for the transfer time. Default is "Asia/Shanghai"
      timezone-default: Asia/Shanghai
