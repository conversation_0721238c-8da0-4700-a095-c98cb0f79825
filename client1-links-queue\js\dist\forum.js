(function(t,s){"use strict";class n extends s{}Object.assign(n.prototype,{id:s.attribute("id"),name:s.attribute("name"),links:s.attribute("links"),sort:s.attribute("sort")});const a={env:"production",app:{extensionId:"wusong8899-client1-links-queue",translationPrefix:"wusong8899-links-queue"},data:{apiResources:{linksQueueList:"linksQueueList"}}};class e{constructor(){this.linksQueueListLoading=!1,this.linksQueueList=null,this.linksQueuePointer=0}static getInstance(){return e.instance||(e.instance=new e),e.instance}async loadLinksQueueList(){if(this.linksQueueListLoading)return this.waitForLinksQueueList();if(this.linksQueueList!==null)return this.linksQueueList;this.linksQueueListLoading=!0;try{const i=await t.store.find(a.data.apiResources.linksQueueList).catch(()=>[]);return this.linksQueueList=[],Array.isArray(i)&&this.linksQueueList.push(...i),this.linksQueueList}catch{return this.linksQueueList=[],this.linksQueueList}finally{this.linksQueueListLoading=!1}}async loadAllData(){return{links:await this.loadLinksQueueList()}}getLinksQueueList(){return this.linksQueueList}getLinksQueuePointer(){return this.linksQueuePointer}setLinksQueuePointer(i){this.linksQueuePointer=Math.max(0,i)}async waitForLinksQueueList(){return new Promise(i=>{const r=setInterval(()=>{!this.linksQueueListLoading&&this.linksQueueList!==null&&(clearInterval(r),i(this.linksQueueList))},100)})}}t.initializers.add("wusong8899-client1-links-queue",()=>{t.store.models.linksQueueList=n;const u=e.getInstance();typeof window<"u"&&(window.LinksQueueDataLoader=u)})})(flarum.core.compat["forum/app"],flarum.core.compat.Model);
//# sourceMappingURL=forum.js.map

module.exports={};