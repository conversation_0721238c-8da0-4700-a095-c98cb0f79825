---
type: "agent_requested"
description: "Flarum 后端视图与 Blade 模板 (Views and Blade)"
---

### **主题：Flarum 后端视图与 Blade 模板 (Views and Blade)**

#### **一、核心概念**

尽管 Flarum 的主要用户界面由前端 Mithril.js 框架驱动，但其后端仍然在多个场景下使用服务器端生成的模板。

- **Blade 模板引擎**: Flarum 使用 Laravel 的 Blade 模板引擎，通过 PHP 代码来动态生成 HTML 或其他静态内容。
- **Views 系统**: 这是 Laravel 组织和注册 Blade 模板（即`.blade.php`文件）的机制。

**主要应用场景**:

- 生成论坛基础的 HTML 骨架（SPA 的挂载点）。
- 包含用于 SEO（搜索引擎优化）的`<meta>`标签。
- 提供在禁用 JavaScript 环境下的“no-js”视图。
- 其他需要服务器端渲染的页面，如邮件模板、错误页等。

---

#### **二、添加视图 (Adding Views)**

要在你的扩展中使用自定义的 Blade 模板，必须先向 Flarum 的视图工厂(View Factory)注册你的视图文件所在的目录。

- **方法**: 在扩展的 `extend.php` 文件中，使用 `Extend\View` 扩展器的 `namespace()` 方法。
- **参数**:

  1.  `namespace`: (String) 一个唯一的命名空间，用于标识你的视图集合。推荐使用 `vendor.extension-name` 格式，例如 `'acme.hello-world'`。
  2.  `path`: (String) 存放 `.blade.php` 文件的目录的绝对路径。通常使用 `__DIR__.'/views'`。

- **示例代码**:

  ```php
  use Flarum\Extend;

  return [
      (new Extend\View)
          ->namespace('acme.hello-world', __DIR__.'/views'),
  ];
  ```

---

#### **三、使用 Blade 模板 (Using Blade Templates)**

注册视图后，你可以在代码中通过依赖注入 `Illuminate\Contracts\View\Factory` 的实例来渲染它们。

##### **1. 渲染视图**

- **方法**: 调用视图工厂实例的 `make()` 方法，并链式调用 `render()` 方法。
- **命名约定**: 使用 `NAMESPACE::VIEW_NAME` 的格式来引用一个视图。

  - `NAMESPACE` 是你在 `extend.php` 中注册的命名空间。
  - `VIEW_NAME` 是 `.blade.php` 文件的路径，其中目录分隔符 `/` 用 `.` 代替，且不包含 `.blade.php` 后缀。
  - 例如，`views/forum/error.blade.php` 文件对应的视图名为 `forum.error`。如果命名空间为 `custom-views`，则完整引用为 `custom-views::forum.error`。

- **示例代码**:

  ```php
  // $view 是一个 Illuminate\Contracts\View\Factory 实例

  // 基础渲染
  $renderedString = $view->make('acme.hello-world::greeting')->render();

  // 渲染并传递变量
  $renderedString = $view->make('acme.hello-world::greeting', ['varName' => true])->render();
  ```

##### **2. 模板中的可用变量**

所有通过 Flarum 视图系统渲染的 Blade 模板，都会自动获得以下全局变量：

- `$url`: 一个 URL 生成器实例 (`Flarum\Http\UrlGenerator`)。
- `$translator`: 一个翻译器实例 (`Symfony\Contracts\Translation\TranslatorInterface`)。
- `$settings`: 一个设置读取实例 (`Flarum\Settings\SettingsRepositoryInterface`)。
- `$slugManager`: 一个 slug 管理器实例 (`Flarum\Discussion\SlugManager`)。
- `$forum` (特定情境): 在通过路由的`Content`逻辑渲染的模板中可用，包含论坛的 API 属性 (`ForumSerializer` 的 attributes)。

---

#### **四、覆盖视图 (Overriding Views)**

你可以覆盖 Flarum 核心或其他扩展已有的 Blade 模板。

- **方法**: 使用 `Extend\View` 扩展器的 `extendNamespace()` 方法。
- **步骤**:

  1.  在你的扩展中创建一个目录（例如 `/override_views`）。
  2.  在该目录中，创建与**被覆盖的视图**完全相同的子目录结构和文件名。
  3.  在 `extend.php` 中，使用 `extendNamespace()` 指向你创建的覆盖目录。

- **示例代码** (假设要覆盖 `acme.hello-world` 命名空间下的某个视图):

  ```php
  use Flarum\Extend;

  return [
      (new Extend\View)
          ->extendNamespace('acme.hello-world', __DIR__.'/override_views'),
  ];
  ```

  Flarum 在查找视图时会优先检查你的 `/override_views` 目录，如果找到匹配文件则使用它，否则回退到原始路径。
