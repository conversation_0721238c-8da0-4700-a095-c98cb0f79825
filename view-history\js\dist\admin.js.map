{"version": 3, "file": "admin.js", "mappings": "MACA,IAAIA,ECAqBC,IACH,oBAAXC,QAA0BA,OAAOC,aAC1CC,OAAOC,eAAeJ,EAASC,OAAOC,YAAa,CAAEG,MAAO,WAE7DF,OAAOC,eAAeJ,EAAS,aAAc,CAAEK,OAAO,K,6BCLlBC,OAAOC,KAAKC,OAAe,OCEhEC,IAAIC,aAAaC,IAAI,sBAAsB,gB", "sources": ["webpack://@wusong8899/flarum-daily-check-in/webpack/bootstrap", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/make namespace object", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['extend']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/admin/index.js"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['extend'];", "import {extend, override} from 'flarum/extend';\n\napp.initializers.add('wusong8899-view-history', () => {\n\n});\n"], "names": ["__webpack_require__", "exports", "Symbol", "toStringTag", "Object", "defineProperty", "value", "flarum", "core", "compat", "app", "initializers", "add"], "sourceRoot": ""}