wusong8899-client1:
  forum:
    under-construction: Under Construction
    loading: Loading...
    error: An error occurred
    no-data: No data available
  admin:
    TransitionTime: Speed of transitions between slides (default is 5 seconds)
    HeaderIconUrl: Header Icon URL
    HeaderIconUrlHelp: Enter a URL for the header icon image, or upload a file below
    HeaderIconPreview: Preview
    HeaderIconPreviewError: Failed to load image
    HeaderIconUploadLabel: Or upload an image file
    HeaderIconUploadButton: Choose File
    HeaderIconUploadHelp: "Supported formats: PNG, JPG, GIF (max 2MB)"
    HeaderIconUploadComingSoon: "File upload feature coming soon!"
    # Dynamic Slide Settings
    SlideSettings: Advertisement Slides
    SlideSettingsHelp: Manage your advertisement slides. Add or remove slides as needed.
    AddSlide: Add Slide
    DeleteSlide: Delete
    SlideNumber: "Slide {number}"
    SlideLink: Link URL
    SlideImage: Image URL
    # Social Media Settings
    SocialKickUrl: Kick URL
    SocialKickUrlHelp: Enter the URL for your Kick channel (leave empty to hide this button)
    SocialKickIcon: Kick Icon URL
    SocialFacebookUrl: Facebook URL
    SocialFacebookUrlHelp: Enter the URL for your Facebook page (leave empty to hide this button)
    SocialFacebookIcon: Facebook Icon URL
    SocialTwitterUrl: Twitter URL
    SocialTwitterUrlHelp: Enter the URL for your Twitter profile (leave empty to hide this button)
    SocialTwitterIcon: Twitter Icon URL
    SocialYouTubeUrl: YouTube URL
    SocialYouTubeUrlHelp: Enter the URL for your YouTube channel (leave empty to hide this button)
    SocialYouTubeIcon: YouTube Icon URL
    SocialInstagramUrl: Instagram URL
    SocialInstagramUrlHelp: Enter the URL for your Instagram profile (leave empty to hide this button)
    SocialInstagramIcon: Instagram Icon URL
    SocialIconHelp: Enter the URL for the icon image (leave empty to use default icon)
    # Slide 1-10
    Link1: Button Link - Slide 1
    Image1: Slide Image 1
    Link2: Button Link - Slide 2
    Image2: Slide Image 2
    Link3: Button Link - Slide 3
    Image3: Slide Image 3
    Link4: Button Link - Slide 4
    Image4: Slide Image 4
    Link5: Button Link - Slide 5
    Image5: Slide Image 5
    Link6: Button Link - Slide 6
    Image6: Slide Image 6
    Link7: Button Link - Slide 7
    Image7: Slide Image 7
    Link8: Button Link - Slide 8
    Image8: Slide Image 8
    Link9: Button Link - Slide 9
    Image9: Slide Image 9
    Link10: Button Link - Slide 10
    Image10: Slide Image 10
    # Slide 11-20
    Link11: Button Link - Slide 11
    Image11: Slide Image 11
    Link12: Button Link - Slide 12
    Image12: Slide Image 12
    Link13: Button Link - Slide 13
    Image13: Slide Image 13
    Link14: Button Link - Slide 14
    Image14: Slide Image 14
    Link15: Button Link - Slide 15
    Image15: Slide Image 15
    Link16: Button Link - Slide 16
    Image16: Slide Image 16
    Link17: Button Link - Slide 17
    Image17: Slide Image 17
    Link18: Button Link - Slide 18
    Image18: Slide Image 18
    Link19: Button Link - Slide 19
    Image19: Slide Image 19
    Link20: Button Link - Slide 20
    Image20: Slide Image 20
    # Slide 21-30
    Link21: Button Link - Slide 21
    Image21: Slide Image 21
    Link22: Button Link - Slide 22
    Image22: Slide Image 22
    Link23: Button Link - Slide 23
    Image23: Slide Image 23
    Link24: Button Link - Slide 24
    Image24: Slide Image 24
    Link25: Button Link - Slide 25
    Image25: Slide Image 25
    Link26: Button Link - Slide 26
    Image26: Slide Image 26
    Link27: Button Link - Slide 27
    Image27: Slide Image 27
    Link28: Button Link - Slide 28
    Image28: Slide Image 28
    Link29: Button Link - Slide 29
    Image29: Slide Image 29
    Link30: Button Link - Slide 30
    Image30: Slide Image 30
