(function(v,K,J,O,P,i,r,k,w,Q,B,F,b,Z,M,E,q,ee,te,C,se,re,ne,V,z,d,ae,Y){"use strict";class G extends i{id(){return i.attribute("id").call(this)||""}transferMoney(){const t=i.attribute("transfer_money_value").call(this);return t===null||typeof t>"u"?0:t}notes(){return i.attribute("notes").call(this)}assignedAt(){const e=i.attribute("assigned_at").call(this);return e?new Date(e):new Date}fromUser(){return i.hasOne("fromUser").call(this)}targetUser(){return i.hasOne("targetUser").call(this)}createdAt(){const e=i.attribute("created_at").call(this);return e?new Date(e):new Date}updatedAt(){const e=i.attribute("updated_at").call(this);return e?new Date(e):new Date}}Object.assign(G.prototype,{id:i.attribute("id"),transferMoney:i.attribute("transfer_money_value"),notes:i.attribute("notes"),assignedAt:i.attribute("assigned_at"),fromUser:i.hasOne("fromUser"),targetUser:i.hasOne("targetUser")});const oe=3,ie=5;class me{constructor(){this.results=[]}search(e){return!e||e.length<oe?(this.results=[],Promise.resolve()):app.store.find("users",{filter:{query:e},page:{limit:ie}}).then(t=>{this.results=t})}view(e){return!this.results||!this.results.length?[]:this.results.map(t=>m("li",{className:"SearchResult","data-index":`users:${t.id()}`},m("a",{className:"SearchResult",tabindex:"-1"},E(t)," ",M(t),m("span",{className:"SearchResult-excerpt"},q(t.username(),e)))))}}const u=0,le=0,ce=0,ue="[money]",fe="antoinefr-money.moneyname",de="users:",he=1,ye=36,pe=2,ge=900,Te=3,W=0,Ne=20,Ie=255,ve="66px",we="tags",U="none",Me="hidden",j="1";class Ee extends Z{constructor(){super(...arguments),this.doSearch=!1}oninit(e){super.oninit(e),this.inputUuid=Math.random().toString(ye).substring(pe)}oncreate(e){super.oncreate(e);try{this.$(".Search-results").on("click",()=>{const t=this.$(".SearchResult.active");this.addRecipient(t.data("index")),this.$(".RecipientsInput").focus()}),this.$(".Search-results").on("touchstart",t=>{const s=this.$(t.target).parent();this.addRecipient(s.data("index")),this.$(".RecipientsInput").focus()}),this.$(".RecipientsInput").on("input",()=>{clearTimeout(this.typingTimer),this.doSearch=!1,this.typingTimer=globalThis.setTimeout(()=>{this.doSearch=!0,m.redraw()},ge)}).on("keydown",()=>{clearTimeout(this.typingTimer)})}catch{}}view(){typeof this.searchState.getValue()>"u"&&this.searchState.setValue("");const e=this.searchState.getValue(),t=e&&e.length>=Te;this.sources||(this.sources=this.sourceItems().toArray());const s=this.attrs.selected().toArray();return m("div",{className:"transferSearchContainer"},m("div",{className:"transferSearchUserListContainer"},s.length===W&&m("div",{style:"height:34px;cursor: default !important;",class:"transferSearchUserContainer"},m("span",{class:"transferSearchUser"},r.translator.trans("wusong8899-transfer-money.forum.transfer-search-no-user-selected"))),s.map(n=>{const c=M(n),o=E(n);return m("div",{class:"transferSearchUserContainer",onclick:l=>this.removeRecipient(n,l)},m("span",{class:"transferSearchUser"},o)," ",c)})),m("div",{className:"Search"},m("div",{className:"Search-input"},m("input",{className:"RecipientsInput FormControl",type:"search",placeholder:te(r.translator.trans("wusong8899-transfer-money.forum.transfer-search-placeholder")),value:this.searchState.getValue(),oninput:n=>this.searchState.setValue(n.target.value),onfocus:()=>this.hasFocus=!0,onblur:()=>this.hasFocus=!1}),m("ul",{className:ee("Dropdown-menu","Search-results","fade",{in:!!t})},this.renderSearchResults()))))}renderSearchResults(){return this.doSearch?this.sources.map(e=>e.view(this.searchState.getValue())):C.component({size:"tiny",className:"Button Button--icon Button--link"})}sourceItems(){const e=new B;return e.add("users",new me),e}addRecipient(e){const[t,s]=e.split(":"),n=this.findRecipient(t,s);n&&(this.attrs.selected().add(e,n),this.attrs.selectedUsers[n.data.id]=1,this.attrs.needMoney(this.getNeedMoney()),this.searchState.setValue(""))}removeRecipient(e,t){t.preventDefault();const s=e.data.id;Reflect.deleteProperty(this.attrs.selectedUsers,s),this.attrs.selected().remove("users:"+s),this.attrs.needMoney(this.getNeedMoney())}getNeedMoney(){const e=document.getElementById("moneyTransferInput");return e?Number.parseFloat(e.value)*Object.keys(this.attrs.selectedUsers).length:W}findRecipient(e,t){return r.store.getById(e,t)}}const R=class R extends k{oninit(e){super.oninit(e)}className(){return"Modal--small"}title(){return r.translator.trans("wusong8899-transfer-money.forum.transfer-money-success")}content(){return[m("div",{className:"Modal-body"},m("div",{style:"text-align:center"},w.component({style:`width:${ve}`,className:"Button Button--primary",onclick:()=>{location.reload()}},r.translator.trans("wusong8899-transfer-money.forum.ok"))))]}};R.isDismissible=!1;let _=R;const A=class A extends k{constructor(){super(...arguments),this.handleRedraw=()=>{m.redraw()}}oninit(e){super.oninit(e),this.selected=F(new B),this.selectedUsers={},this.moneyName=r.forum.attribute(fe)||ue;const t=this.attrs.user;t&&(this.selected().add(de+t.id(),t),this.selectedUsers[t.id()]=he),this.recipientSearch=new Q,this.needMoney=F(u)}className(){return"Modal--small"}title(){return r.translator.trans("wusong8899-transfer-money.forum.transfer-money")}getCurrentUserMoney(){if(!r.session.user)return u;const e=r.session.user.attribute("money");return typeof e=="number"?e:u}getMoneyTransferInputValue(){const e=document.getElementById("moneyTransferInput");if(!e)return u;const t=Number.parseFloat(e.value);return Number.isNaN(t)?u:t}getMoneyTransferNotesValue(){const e=document.getElementById("moneyTransferNotesInput");return e?e.value:""}content(){return m("div",{className:"Modal-body"},m("div",{className:"Form"},m("div",{style:"padding-bottom:20px;",className:"TransferMoneyModal-form"},Ee.component({state:this.recipientSearch,selected:this.selected,selectedUsers:this.selectedUsers,needMoney:this.needMoney,callback:this.handleRedraw})),m("div",{className:"Form-group"},m("label",null,r.translator.trans("wusong8899-transfer-money.forum.current-money-amount"),this.moneyName.replace("[money]",String(this.getCurrentUserMoney()))),m("input",{id:"moneyTransferInput",placeholder:r.translator.trans("wusong8899-transfer-money.forum.transfer-money-input-placeholder"),required:!0,className:"FormControl",type:"number",step:"any",min:"0",oninput:()=>this.moneyTransferChanged()}),m("div",{style:"padding-top:10px"},r.translator.trans("wusong8899-transfer-money.forum.need-money-amount"),m("span",{id:"needMoneyContainer"},this.moneyName.replace("[money]",String(this.needMoney()))))),m("div",{className:"Form-group"},m("label",null,r.translator.trans("wusong8899-transfer-money.forum.transfer-money-notes")),m("textarea",{id:"moneyTransferNotesInput",maxlength:Ie,className:"FormControl"})),m("div",{className:"Form-group",style:"text-align: center;"},w.component({className:"Button Button--primary",type:"submit",loading:this.loading},r.translator.trans("wusong8899-transfer-money.forum.ok"))," ",w.component({className:"Button transferMoneyButton--gray",loading:this.loading,onclick:()=>{this.hide(),this.attrs.callback&&typeof this.attrs.callback=="function"&&this.attrs.callback()}},r.translator.trans("wusong8899-transfer-money.forum.cancel")))))}getTotalNeedMoney(){const e=document.getElementById("moneyTransferInput");if(!e)return u;let t=Number.parseFloat(e.value);return Number.isNaN(t)&&(t=u),Object.keys(this.selectedUsers).length*t}moneyTransferChanged(){const e=this.getTotalNeedMoney(),t=this.moneyName.replace("[money]",String(e)),s=document.getElementById("needMoneyContainer");s&&(s.textContent=t)}onsubmit(e){e.preventDefault();const t=this.getCurrentUserMoney(),s=this.getMoneyTransferInputValue(),n=this.getTotalNeedMoney(),c=this.getMoneyTransferNotesValue();if(n>t){r.alerts.show(b,{type:"error"},r.translator.trans("wusong8899-transfer-money.forum.transfer-error-insufficient-fund"));return}if(Object.keys(this.selectedUsers).length===le){r.alerts.show(b,{type:"error"},r.translator.trans("wusong8899-transfer-money.forum.transfer-error-no-target-user-selected"));return}if(s>ce){const o={moneyTransfer:s,moneyTransferNotes:c,selectedUsers:JSON.stringify(Object.keys(this.selectedUsers))};this.loading=!0,r.store.createRecord("transferMoney").save(o).then(l=>{r.store.pushPayload(l),r.modal.show(_),this.loading=!1,this.attrs.callback&&typeof this.attrs.callback=="function"&&this.attrs.callback()}).catch(()=>{this.loading=!1})}}};A.isDismissible=!1;let T=A;class Se extends se{icon(){return"fas fa-money-bill"}href(){const{user:e}=r.session;let t="";return e&&(t=e.username()),r.route("user.transferHistory",{username:t})}content(){const e=this.attrs.notification.fromUser();return r.translator.trans("wusong8899-transfer-money.forum.notifications.user-transfer-money-to-you",{user:e})}excerpt(){const e=this.attrs.notification.subject();let t="",s="";e&&e.attribute&&(t=e.attribute("transfer_money_value"),s=e.attribute("id"));const c=(r.forum.attribute("antoinefr-money.moneyname")||"[money]").replace("[money]",t);return r.translator.trans("wusong8899-transfer-money.forum.notifications.user-transfer-money-details",{cost:c,id:s})}}const X=a=>a?app.route.user(a):"#";class be extends z{view(){const{transferHistory:e}=this.attrs,t=app.session.user;let s="";t&&(s=t.id());const n=e.attribute("from_user_id"),c=e.assignedAt(),o=e.fromUser(),l=e.targetUser(),x=e.transferMoney(),L=e.notes(),D=e.id(),p=s===n;let y="",f="";p?(y=app.translator.trans("wusong8899-transfer-money.forum.transfer-money-out"),f="TransferHistoryItem-outgoing"):(y=app.translator.trans("wusong8899-transfer-money.forum.transfer-money-in"),f="TransferHistoryItem-incoming");const I=(app.forum.attribute("antoinefr-money.moneyname")||"[money]").replace("[money]",x.toString()),g=L||app.translator.trans("wusong8899-transfer-money.forum.transfer-list-transfer-notes-none");return m("div",{className:`TransferHistoryItem ${f}`},m("div",{className:"TransferHistoryItem-header"},m("span",{className:"TransferHistoryItem-type"},m("strong",null,app.translator.trans("wusong8899-transfer-money.forum.transfer-list-type"),": "),m("span",{className:`TransferHistoryItem-typeLabel ${f}`},y)),m("span",{className:"TransferHistoryItem-separator"}," | "),m("span",{className:"TransferHistoryItem-date"},m("strong",null,app.translator.trans("wusong8899-transfer-money.forum.transfer-list-assign-at"),": "),m("span",{className:"TransferHistoryItem-dateValue"},c.toLocaleDateString()," ",c.toLocaleTimeString()))),m("div",{className:"TransferHistoryItem-details"},m("div",{className:"TransferHistoryItem-id"},m("strong",null,app.translator.trans("wusong8899-transfer-money.forum.transfer-list-id"),": "),D),m("div",{className:"TransferHistoryItem-users"},m("span",{className:"TransferHistoryItem-fromUser"},m("strong",null,app.translator.trans("wusong8899-transfer-money.forum.transfer-list-from-user"),": "),m(Y,{href:X(o),className:"TransferHistoryItem-userLink"},E(o)," ",M(o))),m("span",{className:"TransferHistoryItem-separator"}," | "),m("span",{className:"TransferHistoryItem-targetUser"},m("strong",null,app.translator.trans("wusong8899-transfer-money.forum.transfer-list-target-user"),": "),m(Y,{href:X(l),className:"TransferHistoryItem-userLink"},E(l)," ",M(l)))),m("div",{className:"TransferHistoryItem-amount"},m("strong",null,app.translator.trans("wusong8899-transfer-money.forum.transfer-list-transfer-amount"),": "),m("span",{className:"TransferHistoryItem-money"},I)),L&&m("div",{className:"TransferHistoryItem-notes"},m("strong",null,app.translator.trans("wusong8899-transfer-money.forum.transfer-list-transfer-notes"),": "),m("span",{className:"TransferHistoryItem-notesText"},g))))}}const h=class h extends z{constructor(){super(...arguments),this.state={loading:!1,error:"",data:[],hasMore:!1}}oninit(e){super.oninit(e),this.user=this.attrs.params.user,this.state={loading:!1,error:"",data:[],hasMore:!1},this.loadResults()}view(){if(this.state.loading)return m("div",{className:"TransferHistoryList"},m("div",{className:"TransferHistoryList-loading"},m(C,null)));if(this.state.error)return m("div",{className:"TransferHistoryList"},m("div",{className:"TransferHistoryList-error"},m(b,{type:"error",dismissible:!1},this.state.error)));const e=this.state.data.filter(t=>t&&typeof t.id=="function").map(t=>m("li",{className:"TransferHistoryList-item",key:t.id(),"data-id":t.id()},m(be,{transferHistory:t})));return m("div",{className:"TransferHistoryList"},m("div",{className:"TransferHistoryList-header"},m("h3",null,d.translator.trans("wusong8899-transfer-money.forum.transfer-history"))),m("div",{className:"TransferHistoryList-content"},this.state.data.length===h.EMPTY_LIST_LENGTH&&m("div",{className:"TransferHistoryList-empty"},m(ae,{text:d.translator.trans("wusong8899-transfer-money.forum.transfer-history-empty")})),this.state.data.length>h.EMPTY_LIST_LENGTH&&m("ul",{className:"TransferHistoryList-items"},e),this.state.hasMore&&m("div",{className:"TransferHistoryList-loadMore"},m(w,{className:"Button",onclick:this.loadMore.bind(this),loading:this.state.loading},d.translator.trans("wusong8899-transfer-money.forum.load-more")))))}loadMore(){this.state.loading||(this.state.loading=!0,this.loadResults(this.state.data.length).catch(e=>this.handleError(e)).finally(()=>{this.state.loading=!1,m.redraw()}))}parseResults(e){try{if(typeof e=="object"&&e){const s=e,n=s.payload&&s.payload.links&&s.payload.links.next;this.state.hasMore=!!n}else this.state.hasMore=!1;const t=d.store.all("transferMoney");this.state.data=[...this.state.data,...t.filter(s=>!this.state.data.some(n=>n.id()===s.id()))],this.state.error=""}catch(t){this.handleError(t)}}handleError(e){d.forum.attribute("debug")&&console.error("Transfer history loading error:",e),isApiError(e)?this.state.error=e.message:e instanceof Error?this.state.error=e.message:this.state.error=d.translator.trans("wusong8899-transfer-money.forum.error-loading-history")}loadResults(e){const s=e??0;return this.state.loading=!0,this.state.error="",d.store.find("transferMoney",{filter:{user:this.user.id()},page:{offset:s,limit:h.ITEMS_PER_PAGE}}).then(n=>{this.parseResults(n)}).catch(n=>{throw this.handleError(n),n}).finally(()=>{this.state.loading=!1})}};h.ITEMS_PER_PAGE=Ne,h.EMPTY_LIST_LENGTH=u;let H=h;class Ce extends V{oninit(e){super.oninit(e);const t=m.route.param("username");t&&this.loadUser(t)}content(){if(!this.user)return m("div",{className:"TransferHistoryPage"},m("div",{className:"TransferHistoryPage-loading"},m(C,null)));const e=this.user;return m("div",{className:"TransferHistoryPage"},m("div",{className:"TransferHistoryPage-header"},m("h2",{className:"TransferHistoryPage-title"},app.translator.trans("wusong8899-transfer-money.forum.transfer-history"))),m("div",{className:"TransferHistoryPage-content"},m(H,{params:{user:e}})))}show(e){super.show(e)}title(){const e=app.translator.trans("wusong8899-transfer-money.forum.transfer-history");return this.user?`${e} - ${this.user.displayName()}`:e}}const Ue=10;function _e(){app.routes["user.transferHistory"]={component:Ce,path:"/u/:username/transferHistory"},re.extend(V.prototype,"navItems",function(e){if(app.session.user){const t=app.session.user,s=this.user;t&&s&&t.id()===s.id()&&e.add("transferHistory",ne.component({href:app.route("user.transferHistory",{username:s.username()}),icon:"fas fa-exchange-alt"},app.translator.trans("wusong8899-transfer-money.forum.transfer-history")),Ue)}})}const He=10,Re=0,Ae=()=>{if(r.forum.attribute("moneyTransferClient1Customization")!==j)return;const e=document.getElementById("transferMoneyLabelContainer");e!==null&&$(e).remove()},xe=(a,e)=>{const t=$("#drawer").css("visibility")===Me,s=r.forum.attribute("moneyTransferClient1Customization");if(t===!1||s!==j)return;$("#content .IndexPage-nav .item-nav").css("display",U),$("#content .IndexPage-nav .item-newDiscussion").remove();const n=setInterval(function(){if(a.dom&&(clearInterval(n),a.dom)){$("#content .IndexPage-nav .item-nav").css("display",U),$("#content .IndexPage-nav .item-newDiscussion").remove();let o=document.getElementById("transferMoneyLabelContainer");if(o!==null)return;$("#content .IndexPage-nav .item-nav .ButtonGroup").removeClass("App-titleControl"),$("#content .IndexPage-nav .item-nav .ButtonGroup button").addClass("Button--link");let l=$("#content .IndexPage-nav .item-nav").clone();l.length>Re&&($("#itemNavClone").remove(),$(l).attr("id","itemNavClone"),$(l).css("display",""),$("#header-secondary .Header-controls").prepend(l));const x=document.getElementById("app-navigation"),D=(r.forum.attribute("antoinefr-money.moneyname")||"[money]").replace("[money]",r.session.user.attribute("money"));o=document.createElement("div"),o.id="transferMoneyLabelContainer",o.className="clientCustomizeWithdrawalButtonContainer";const p=document.createElement("div");p.className="clientCustomizeWithdrawalHeaderItems clientCustomizeWithdrawalHeaderTotalMoney";const y=document.createElement("div");y.innerHTML='<span style="font-size:16px;"><i class="fab fa-bitcoin" style="padding-right: 8px;color: gold;"></i></span>'+D,y.className="clientCustomizeWithdrawalHeaderText";const f=document.createElement("div");f.innerHTML='<i class="fas fa-wallet"></i>',f.className="clientCustomizeWithdrawalHeaderIcon",p.appendChild(y),p.appendChild(f);const N=document.createElement("div");N.innerHTML=r.translator.trans("wusong8899-transfer-money.forum.withdrawal"),N.className="clientCustomizeWithdrawalHeaderItems clientCustomizeWithdrawalHeaderWithdrawal",$(N).click(function(){r.modal.show(T)});const I=document.createElement("div");I.className="clientCustomizeWithdrawalHeaderItems clientCustomizeWithdrawalHeaderUser";const g=$("#header-secondary .item-session .SessionDropdown").clone();$(g).attr("id","avatarClone"),$(g).addClass("App-primaryControl"),$(I).html(g);let S="";$(g).on("click",function(){S===""?S=U:S="",$("#content .IndexPage-nav").css("display",S)}),o.appendChild(p),o.appendChild(N),o.appendChild(I),x.appendChild(o)}},He)},Le=()=>{v.extend(O.prototype,"view",function(e){if(!r.session.user)return;const t=r.current.get("routeName");t&&(t!==we?Ae():xe(e,this.attrs.user))})};app.initializers.add("wusong8899-money-transfer",()=>{app.store.models.transferMoney=G,app.notificationComponents.transferMoney=Se,_e(),Le(),v.extend(J.prototype,"notificationTypes",function(t){t.add("transferMoney",{name:"transferMoney",icon:"fas fa-dollar-sign",label:app.translator.trans("wusong8899-transfer-money.forum.receive-transfer-from-user")})}),v.extend(K,"moderationControls",(e,t)=>{const s=app.forum.attribute("allowUseTransferMoney");if(app.session.user&&s){const n=app.session.user.id(),c=t.id();n!==c&&e.add("transferMoney",P.component({icon:"fas fa-money-bill",onclick:()=>app.modal.show(T,{user:t})},app.translator.trans("wusong8899-transfer-money.forum.transfer-money")))}});const a=-1;v.extend(O.prototype,"items",function(t){app.session.user&&t.add("transferMoney",P.component({icon:"fas fa-money-bill",onclick:()=>{app.modal.show(T)}},app.translator.trans("wusong8899-transfer-money.forum.transfer-money")),a)})})})(flarum.core.compat.extend,flarum.core.compat["utils/UserControls"],flarum.core.compat["components/NotificationGrid"],flarum.core.compat["forum/components/SessionDropdown"],flarum.core.compat["components/Button"],flarum.core.compat["common/Model"],flarum.core.compat["forum/app"],flarum.core.compat["common/components/Modal"],flarum.core.compat["common/components/Button"],flarum.core.compat["forum/states/SearchState"],flarum.core.compat["common/utils/ItemList"],flarum.core.compat["common/utils/Stream"],flarum.core.compat["common/components/Alert"],flarum.core.compat["forum/components/Search"],flarum.core.compat["common/helpers/username"],flarum.core.compat["common/helpers/avatar"],flarum.core.compat["common/helpers/highlight"],flarum.core.compat["common/utils/classList"],flarum.core.compat["common/utils/extractText"],flarum.core.compat["common/components/LoadingIndicator"],flarum.core.compat["forum/components/Notification"],flarum.core.compat["common/extend"],flarum.core.compat["components/LinkButton"],flarum.core.compat["components/UserPage"],flarum.core.compat.Component,flarum.core.compat.app,flarum.core.compat["common/components/Placeholder"],flarum.core.compat["common/components/Link"]);
//# sourceMappingURL=forum.js.map

module.exports={};