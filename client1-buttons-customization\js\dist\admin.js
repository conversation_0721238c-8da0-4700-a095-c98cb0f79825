(function(v,He,rt,ve,J,We,X,at){"use strict";const ge=class ge extends ve{oninit(t){super.oninit(t),this.ButtonsCustomizationItemData=this.attrs.ButtonsCustomizationItemData,this.settingType="add",this.loading=!1,this.ButtonsCustomizationItemData?(this.settingType="edit",this.itemName=J(this.ButtonsCustomizationItemData.name()),this.itemUrl=J(this.ButtonsCustomizationItemData.url()),this.itemIcon=J(this.ButtonsCustomizationItemData.icon()),this.itemColor=J(this.ButtonsCustomizationItemData.color())):(this.itemName=J(""),this.itemUrl=J(""),this.itemIcon=J(""),this.itemColor=J(""))}className(){return"Modal--medium"}title(){return this.settingType==="add"?v.translator.trans("client1-buttons-customization.admin.settings.item-add"):v.translator.trans("client1-buttons-customization.admin.settings.item-edit")}content(){const t=window.m||v.m||globalThis.m;return t?t("div",{className:"Modal-body"},[t("div",{className:"Form"},[t("div",{className:"Form-group",style:"text-align: center;"},[t("div",[t("div",{className:"GuaGuaLeSettingsLabel"},v.translator.trans("client1-buttons-customization.admin.settings.item-name")),t("input",{maxlength:255,required:!0,className:"FormControl",value:this.itemName(),oninput:e=>this.itemName(e.target.value)}),t("div",{className:"GuaGuaLeSettingsLabel"},v.translator.trans("client1-buttons-customization.admin.settings.item-url")),t("input",{maxlength:500,required:!0,className:"FormControl",value:this.itemUrl(),oninput:e=>this.itemUrl(e.target.value)}),t("div",{className:"GuaGuaLeSettingsLabel"},v.translator.trans("client1-buttons-customization.admin.settings.item-icon")),t("input",{maxlength:50,required:!0,className:"FormControl",value:this.itemIcon(),oninput:e=>this.itemIcon(e.target.value)})])]),t("div",{className:"Form-group",style:"text-align: center;"},[t(rt,{className:"Button Button--primary",type:"submit",loading:this.loading},v.translator.trans("client1-buttons-customization.admin.confirm"))," ",t(rt,{className:"Button guagualeButton--gray",loading:this.loading,onclick:()=>this.hide()},v.translator.trans("client1-buttons-customization.admin.cancel"))])])]):"Error: Mithril not loaded"}onsubmit(t){t.preventDefault(),this.loading=!0,this.settingType==="edit"&&this.ButtonsCustomizationItemData?this.ButtonsCustomizationItemData.save({name:this.itemName(),url:this.itemUrl(),icon:this.itemIcon(),color:this.itemColor()}).then(()=>{this.attrs.onSave&&this.attrs.onSave(),this.hide()}).catch(e=>{this.loading=!1,this.handleErrors(e)}):v.store.createRecord("buttonsCustomizationList").save({name:this.itemName(),url:this.itemUrl(),icon:this.itemIcon(),color:this.itemColor()}).then(()=>{this.attrs.onSave&&this.attrs.onSave(),this.hide()}).catch(e=>{this.loading=!1,this.handleErrors(e)})}handleErrors(t){v.alerts.show({type:"error"},v.translator.trans("client1-buttons-customization.admin.save-error"))}};ge.isDismissible=!1;let Ft=ge;const pe=class pe extends ve{oninit(t){super.oninit(t),this.ButtonsCustomizationItemData=this.attrs.ButtonsCustomizationItemData,this.loading=!1}className(){return"Modal--small"}title(){return v.translator.trans("client1-buttons-customization.admin.settings.item-delete-confirmation")}content(){return X("div",{className:"Modal-body"},[X("div",{className:"Form-group",style:"text-align: center;"},[X(rt,{className:"Button Button--primary",type:"submit",loading:this.loading},v.translator.trans("client1-buttons-customization.admin.confirm"))," ",X(rt,{className:"Button guagualeButton--gray",loading:this.loading,onclick:()=>this.hide()},v.translator.trans("client1-buttons-customization.admin.cancel"))])])}onsubmit(t){t.preventDefault(),this.loading=!0,this.ButtonsCustomizationItemData.delete().then(()=>{location.reload()})}};pe.isDismissible=!1;let Zt=pe;class je extends We{view(){const t=window.m||v.m||globalThis.m;if(!t)return"Error: Mithril not loaded";const{ButtonsCustomizationItemData:e}=this.attrs,n=e.id(),o=e.name(),r=e.url(),a=e.icon();return t("div",{style:"border: 1px dotted var(--control-color);padding: 10px;border-radius: 4px;"},[t("div",[t("div",{style:"padding-top: 5px;"},[t(rt,{className:"Button Button--primary",onclick:()=>this.editItem(e)},v.translator.trans("client1-buttons-customization.admin.settings.item-edit"))," ",t(rt,{className:"Button Button--danger",style:"font-weight:bold;width:66px;",onclick:()=>this.deleteItem(e)},v.translator.trans("client1-buttons-customization.admin.settings.item-delete"))," ",t("b",v.translator.trans("client1-buttons-customization.admin.settings.item-id")+": "),n," | ",t("i",{className:a})," ",t("b",v.translator.trans("client1-buttons-customization.admin.settings.item-name")+": "),o," | ",t("b",v.translator.trans("client1-buttons-customization.admin.settings.item-url")+": "),r," "])])])}editItem(t){v.modal.show(Ft,{ButtonsCustomizationItemData:t,onSave:this.attrs.onSave})}deleteItem(t){v.modal.show(Zt,{ButtonsCustomizationItemData:t})}}/**!
 * Sortable 1.15.6
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function be(i,t){var e=Object.keys(i);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(i);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(i,o).enumerable})),e.push.apply(e,n)}return e}function j(i){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?be(Object(e),!0).forEach(function(n){Ue(i,n,e[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(e)):be(Object(e)).forEach(function(n){Object.defineProperty(i,n,Object.getOwnPropertyDescriptor(e,n))})}return i}function Mt(i){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Mt=function(t){return typeof t}:Mt=function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mt(i)}function Ue(i,t,e){return t in i?Object.defineProperty(i,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):i[t]=e,i}function q(){return q=Object.assign||function(i){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(i[n]=e[n])}return i},q.apply(this,arguments)}function $e(i,t){if(i==null)return{};var e={},n=Object.keys(i),o,r;for(r=0;r<n.length;r++)o=n[r],!(t.indexOf(o)>=0)&&(e[o]=i[o]);return e}function qe(i,t){if(i==null)return{};var e=$e(i,t),n,o;if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(i);for(o=0;o<r.length;o++)n=r[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(i,n)&&(e[n]=i[n])}return e}var Ve="1.15.6";function V(i){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(i)}var K=V(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),yt=V(/Edge/i),ye=V(/firefox/i),wt=V(/safari/i)&&!V(/chrome/i)&&!V(/android/i),Qt=V(/iP(ad|od|hone)/i),we=V(/chrome/i)&&V(/android/i),Ee={capture:!1,passive:!1};function y(i,t,e){i.addEventListener(t,e,!K&&Ee)}function b(i,t,e){i.removeEventListener(t,e,!K&&Ee)}function Bt(i,t){if(t){if(t[0]===">"&&(t=t.substring(1)),i)try{if(i.matches)return i.matches(t);if(i.msMatchesSelector)return i.msMatchesSelector(t);if(i.webkitMatchesSelector)return i.webkitMatchesSelector(t)}catch{return!1}return!1}}function De(i){return i.host&&i!==document&&i.host.nodeType?i.host:i.parentNode}function Y(i,t,e,n){if(i){e=e||document;do{if(t!=null&&(t[0]===">"?i.parentNode===e&&Bt(i,t):Bt(i,t))||n&&i===e)return i;if(i===e)break}while(i=De(i))}return null}var _e=/\s+/g;function M(i,t,e){if(i&&t)if(i.classList)i.classList[e?"add":"remove"](t);else{var n=(" "+i.className+" ").replace(_e," ").replace(" "+t+" "," ");i.className=(n+(e?" "+t:"")).replace(_e," ")}}function h(i,t,e){var n=i&&i.style;if(n){if(e===void 0)return document.defaultView&&document.defaultView.getComputedStyle?e=document.defaultView.getComputedStyle(i,""):i.currentStyle&&(e=i.currentStyle),t===void 0?e:e[t];!(t in n)&&t.indexOf("webkit")===-1&&(t="-webkit-"+t),n[t]=e+(typeof e=="string"?"":"px")}}function ht(i,t){var e="";if(typeof i=="string")e=i;else do{var n=h(i,"transform");n&&n!=="none"&&(e=n+" "+e)}while(!t&&(i=i.parentNode));var o=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return o&&new o(e)}function Se(i,t,e){if(i){var n=i.getElementsByTagName(t),o=0,r=n.length;if(e)for(;o<r;o++)e(n[o],o);return n}return[]}function U(){var i=document.scrollingElement;return i||document.documentElement}function I(i,t,e,n,o){if(!(!i.getBoundingClientRect&&i!==window)){var r,a,s,l,u,f,d;if(i!==window&&i.parentNode&&i!==U()?(r=i.getBoundingClientRect(),a=r.top,s=r.left,l=r.bottom,u=r.right,f=r.height,d=r.width):(a=0,s=0,l=window.innerHeight,u=window.innerWidth,f=window.innerHeight,d=window.innerWidth),(t||e)&&i!==window&&(o=o||i.parentNode,!K))do if(o&&o.getBoundingClientRect&&(h(o,"transform")!=="none"||e&&h(o,"position")!=="static")){var w=o.getBoundingClientRect();a-=w.top+parseInt(h(o,"border-top-width")),s-=w.left+parseInt(h(o,"border-left-width")),l=a+r.height,u=s+r.width;break}while(o=o.parentNode);if(n&&i!==window){var _=ht(o||i),E=_&&_.a,D=_&&_.d;_&&(a/=D,s/=E,d/=E,f/=D,l=a+f,u=s+d)}return{top:a,left:s,bottom:l,right:u,width:d,height:f}}}function Ce(i,t,e){for(var n=tt(i,!0),o=I(i)[t];n;){var r=I(n)[e],a=void 0;if(a=o>=r,!a)return n;if(n===U())break;n=tt(n,!1)}return!1}function mt(i,t,e,n){for(var o=0,r=0,a=i.children;r<a.length;){if(a[r].style.display!=="none"&&a[r]!==g.ghost&&(n||a[r]!==g.dragged)&&Y(a[r],e.draggable,i,!1)){if(o===t)return a[r];o++}r++}return null}function Jt(i,t){for(var e=i.lastElementChild;e&&(e===g.ghost||h(e,"display")==="none"||t&&!Bt(e,t));)e=e.previousElementSibling;return e||null}function L(i,t){var e=0;if(!i||!i.parentNode)return-1;for(;i=i.previousElementSibling;)i.nodeName.toUpperCase()!=="TEMPLATE"&&i!==g.clone&&(!t||Bt(i,t))&&e++;return e}function Te(i){var t=0,e=0,n=U();if(i)do{var o=ht(i),r=o.a,a=o.d;t+=i.scrollLeft*r,e+=i.scrollTop*a}while(i!==n&&(i=i.parentNode));return[t,e]}function Ke(i,t){for(var e in i)if(i.hasOwnProperty(e)){for(var n in t)if(t.hasOwnProperty(n)&&t[n]===i[e][n])return Number(e)}return-1}function tt(i,t){if(!i||!i.getBoundingClientRect)return U();var e=i,n=!1;do if(e.clientWidth<e.scrollWidth||e.clientHeight<e.scrollHeight){var o=h(e);if(e.clientWidth<e.scrollWidth&&(o.overflowX=="auto"||o.overflowX=="scroll")||e.clientHeight<e.scrollHeight&&(o.overflowY=="auto"||o.overflowY=="scroll")){if(!e.getBoundingClientRect||e===document.body)return U();if(n||t)return e;n=!0}}while(e=e.parentNode);return U()}function Ze(i,t){if(i&&t)for(var e in t)t.hasOwnProperty(e)&&(i[e]=t[e]);return i}function te(i,t){return Math.round(i.top)===Math.round(t.top)&&Math.round(i.left)===Math.round(t.left)&&Math.round(i.height)===Math.round(t.height)&&Math.round(i.width)===Math.round(t.width)}var Et;function Ie(i,t){return function(){if(!Et){var e=arguments,n=this;e.length===1?i.call(n,e[0]):i.apply(n,e),Et=setTimeout(function(){Et=void 0},t)}}}function Qe(){clearTimeout(Et),Et=void 0}function Ne(i,t,e){i.scrollLeft+=t,i.scrollTop+=e}function Oe(i){var t=window.Polymer,e=window.jQuery||window.Zepto;return t&&t.dom?t.dom(i).cloneNode(!0):e?e(i).clone(!0)[0]:i.cloneNode(!0)}function xe(i,t,e){var n={};return Array.from(i.children).forEach(function(o){var r,a,s,l;if(!(!Y(o,t.draggable,i,!1)||o.animated||o===e)){var u=I(o);n.left=Math.min((r=n.left)!==null&&r!==void 0?r:1/0,u.left),n.top=Math.min((a=n.top)!==null&&a!==void 0?a:1/0,u.top),n.right=Math.max((s=n.right)!==null&&s!==void 0?s:-1/0,u.right),n.bottom=Math.max((l=n.bottom)!==null&&l!==void 0?l:-1/0,u.bottom)}}),n.width=n.right-n.left,n.height=n.bottom-n.top,n.x=n.left,n.y=n.top,n}var z="Sortable"+new Date().getTime();function Je(){var i=[],t;return{captureAnimationState:function(){if(i=[],!!this.options.animation){var n=[].slice.call(this.el.children);n.forEach(function(o){if(!(h(o,"display")==="none"||o===g.ghost)){i.push({target:o,rect:I(o)});var r=j({},i[i.length-1].rect);if(o.thisAnimationDuration){var a=ht(o,!0);a&&(r.top-=a.f,r.left-=a.e)}o.fromRect=r}})}},addAnimationState:function(n){i.push(n)},removeAnimationState:function(n){i.splice(Ke(i,{target:n}),1)},animateAll:function(n){var o=this;if(!this.options.animation){clearTimeout(t),typeof n=="function"&&n();return}var r=!1,a=0;i.forEach(function(s){var l=0,u=s.target,f=u.fromRect,d=I(u),w=u.prevFromRect,_=u.prevToRect,E=s.rect,D=ht(u,!0);D&&(d.top-=D.f,d.left-=D.e),u.toRect=d,u.thisAnimationDuration&&te(w,d)&&!te(f,d)&&(E.top-d.top)/(E.left-d.left)===(f.top-d.top)/(f.left-d.left)&&(l=en(E,w,_,o.options)),te(d,f)||(u.prevFromRect=f,u.prevToRect=d,l||(l=o.options.animation),o.animate(u,E,d,l)),l&&(r=!0,a=Math.max(a,l),clearTimeout(u.animationResetTimer),u.animationResetTimer=setTimeout(function(){u.animationTime=0,u.prevFromRect=null,u.fromRect=null,u.prevToRect=null,u.thisAnimationDuration=null},l),u.thisAnimationDuration=l)}),clearTimeout(t),r?t=setTimeout(function(){typeof n=="function"&&n()},a):typeof n=="function"&&n(),i=[]},animate:function(n,o,r,a){if(a){h(n,"transition",""),h(n,"transform","");var s=ht(this.el),l=s&&s.a,u=s&&s.d,f=(o.left-r.left)/(l||1),d=(o.top-r.top)/(u||1);n.animatingX=!!f,n.animatingY=!!d,h(n,"transform","translate3d("+f+"px,"+d+"px,0)"),this.forRepaintDummy=tn(n),h(n,"transition","transform "+a+"ms"+(this.options.easing?" "+this.options.easing:"")),h(n,"transform","translate3d(0,0,0)"),typeof n.animated=="number"&&clearTimeout(n.animated),n.animated=setTimeout(function(){h(n,"transition",""),h(n,"transform",""),n.animated=!1,n.animatingX=!1,n.animatingY=!1},a)}}}}function tn(i){return i.offsetWidth}function en(i,t,e,n){return Math.sqrt(Math.pow(t.top-i.top,2)+Math.pow(t.left-i.left,2))/Math.sqrt(Math.pow(t.top-e.top,2)+Math.pow(t.left-e.left,2))*n.animation}var gt=[],ee={initializeByDefault:!0},Dt={mount:function(t){for(var e in ee)ee.hasOwnProperty(e)&&!(e in t)&&(t[e]=ee[e]);gt.forEach(function(n){if(n.pluginName===t.pluginName)throw"Sortable: Cannot mount plugin ".concat(t.pluginName," more than once")}),gt.push(t)},pluginEvent:function(t,e,n){var o=this;this.eventCanceled=!1,n.cancel=function(){o.eventCanceled=!0};var r=t+"Global";gt.forEach(function(a){e[a.pluginName]&&(e[a.pluginName][r]&&e[a.pluginName][r](j({sortable:e},n)),e.options[a.pluginName]&&e[a.pluginName][t]&&e[a.pluginName][t](j({sortable:e},n)))})},initializePlugins:function(t,e,n,o){gt.forEach(function(s){var l=s.pluginName;if(!(!t.options[l]&&!s.initializeByDefault)){var u=new s(t,e,t.options);u.sortable=t,u.options=t.options,t[l]=u,q(n,u.defaults)}});for(var r in t.options)if(t.options.hasOwnProperty(r)){var a=this.modifyOption(t,r,t.options[r]);typeof a<"u"&&(t.options[r]=a)}},getEventProperties:function(t,e){var n={};return gt.forEach(function(o){typeof o.eventProperties=="function"&&q(n,o.eventProperties.call(e[o.pluginName],t))}),n},modifyOption:function(t,e,n){var o;return gt.forEach(function(r){t[r.pluginName]&&r.optionListeners&&typeof r.optionListeners[e]=="function"&&(o=r.optionListeners[e].call(t[r.pluginName],n))}),o}};function nn(i){var t=i.sortable,e=i.rootEl,n=i.name,o=i.targetEl,r=i.cloneEl,a=i.toEl,s=i.fromEl,l=i.oldIndex,u=i.newIndex,f=i.oldDraggableIndex,d=i.newDraggableIndex,w=i.originalEvent,_=i.putSortable,E=i.extraEventProperties;if(t=t||e&&e[z],!!t){var D,H=t.options,Z="on"+n.charAt(0).toUpperCase()+n.substr(1);window.CustomEvent&&!K&&!yt?D=new CustomEvent(n,{bubbles:!0,cancelable:!0}):(D=document.createEvent("Event"),D.initEvent(n,!0,!0)),D.to=a||e,D.from=s||e,D.item=o||e,D.clone=r,D.oldIndex=l,D.newIndex=u,D.oldDraggableIndex=f,D.newDraggableIndex=d,D.originalEvent=w,D.pullMode=_?_.lastPutMode:void 0;var A=j(j({},E),Dt.getEventProperties(n,t));for(var W in A)D[W]=A[W];e&&e.dispatchEvent(D),H[Z]&&H[Z].call(t,D)}}var on=["evt"],F=function(t,e){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},o=n.evt,r=qe(n,on);Dt.pluginEvent.bind(g)(t,e,j({dragEl:c,parentEl:T,ghostEl:p,rootEl:S,nextEl:st,lastDownEl:Rt,cloneEl:C,cloneHidden:et,dragStarted:St,putSortable:O,activeSortable:g.active,originalEvent:o,oldIndex:pt,oldDraggableIndex:_t,newIndex:B,newDraggableIndex:nt,hideGhostForTarget:Re,unhideGhostForTarget:ke,cloneNowHidden:function(){et=!0},cloneNowShown:function(){et=!1},dispatchSortableEvent:function(s){P({sortable:e,name:s,originalEvent:o})}},r))};function P(i){nn(j({putSortable:O,cloneEl:C,targetEl:c,rootEl:S,oldIndex:pt,oldDraggableIndex:_t,newIndex:B,newDraggableIndex:nt},i))}var c,T,p,S,st,Rt,C,et,pt,B,_t,nt,kt,O,vt=!1,Lt=!1,Xt=[],lt,G,ne,ie,Pe,Ae,St,bt,Ct,Tt=!1,Yt=!1,Gt,x,oe=[],re=!1,Ht=[],Wt=typeof document<"u",jt=Qt,ze=yt||K?"cssFloat":"float",rn=Wt&&!we&&!Qt&&"draggable"in document.createElement("div"),Fe=(function(){if(Wt){if(K)return!1;var i=document.createElement("x");return i.style.cssText="pointer-events:auto",i.style.pointerEvents==="auto"}})(),Me=function(t,e){var n=h(t),o=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),r=mt(t,0,e),a=mt(t,1,e),s=r&&h(r),l=a&&h(a),u=s&&parseInt(s.marginLeft)+parseInt(s.marginRight)+I(r).width,f=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+I(a).width;if(n.display==="flex")return n.flexDirection==="column"||n.flexDirection==="column-reverse"?"vertical":"horizontal";if(n.display==="grid")return n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(r&&s.float&&s.float!=="none"){var d=s.float==="left"?"left":"right";return a&&(l.clear==="both"||l.clear===d)?"vertical":"horizontal"}return r&&(s.display==="block"||s.display==="flex"||s.display==="table"||s.display==="grid"||u>=o&&n[ze]==="none"||a&&n[ze]==="none"&&u+f>o)?"vertical":"horizontal"},an=function(t,e,n){var o=n?t.left:t.top,r=n?t.right:t.bottom,a=n?t.width:t.height,s=n?e.left:e.top,l=n?e.right:e.bottom,u=n?e.width:e.height;return o===s||r===l||o+a/2===s+u/2},sn=function(t,e){var n;return Xt.some(function(o){var r=o[z].options.emptyInsertThreshold;if(!(!r||Jt(o))){var a=I(o),s=t>=a.left-r&&t<=a.right+r,l=e>=a.top-r&&e<=a.bottom+r;if(s&&l)return n=o}}),n},Be=function(t){function e(r,a){return function(s,l,u,f){var d=s.options.group.name&&l.options.group.name&&s.options.group.name===l.options.group.name;if(r==null&&(a||d))return!0;if(r==null||r===!1)return!1;if(a&&r==="clone")return r;if(typeof r=="function")return e(r(s,l,u,f),a)(s,l,u,f);var w=(a?s:l).options.group.name;return r===!0||typeof r=="string"&&r===w||r.join&&r.indexOf(w)>-1}}var n={},o=t.group;(!o||Mt(o)!="object")&&(o={name:o}),n.name=o.name,n.checkPull=e(o.pull,!0),n.checkPut=e(o.put),n.revertClone=o.revertClone,t.group=n},Re=function(){!Fe&&p&&h(p,"display","none")},ke=function(){!Fe&&p&&h(p,"display","")};Wt&&!we&&document.addEventListener("click",function(i){if(Lt)return i.preventDefault(),i.stopPropagation&&i.stopPropagation(),i.stopImmediatePropagation&&i.stopImmediatePropagation(),Lt=!1,!1},!0);var ut=function(t){if(c){t=t.touches?t.touches[0]:t;var e=sn(t.clientX,t.clientY);if(e){var n={};for(var o in t)t.hasOwnProperty(o)&&(n[o]=t[o]);n.target=n.rootEl=e,n.preventDefault=void 0,n.stopPropagation=void 0,e[z]._onDragOver(n)}}},ln=function(t){c&&c.parentNode[z]._isOutsideThisEl(t.target)};function g(i,t){if(!(i&&i.nodeType&&i.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(i));this.el=i,this.options=t=q({},t),i[z]=this;var e={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(i.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return Me(i,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(a,s){a.setData("Text",s.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:g.supportPointer!==!1&&"PointerEvent"in window&&(!wt||Qt),emptyInsertThreshold:5};Dt.initializePlugins(this,i,e);for(var n in e)!(n in t)&&(t[n]=e[n]);Be(t);for(var o in this)o.charAt(0)==="_"&&typeof this[o]=="function"&&(this[o]=this[o].bind(this));this.nativeDraggable=t.forceFallback?!1:rn,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?y(i,"pointerdown",this._onTapStart):(y(i,"mousedown",this._onTapStart),y(i,"touchstart",this._onTapStart)),this.nativeDraggable&&(y(i,"dragover",this),y(i,"dragenter",this)),Xt.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),q(this,Je())}g.prototype={constructor:g,_isOutsideThisEl:function(t){!this.el.contains(t)&&t!==this.el&&(bt=null)},_getDirection:function(t,e){return typeof this.options.direction=="function"?this.options.direction.call(this,t,e,c):this.options.direction},_onTapStart:function(t){if(t.cancelable){var e=this,n=this.el,o=this.options,r=o.preventOnFilter,a=t.type,s=t.touches&&t.touches[0]||t.pointerType&&t.pointerType==="touch"&&t,l=(s||t).target,u=t.target.shadowRoot&&(t.path&&t.path[0]||t.composedPath&&t.composedPath()[0])||l,f=o.filter;if(pn(n),!c&&!(/mousedown|pointerdown/.test(a)&&t.button!==0||o.disabled)&&!u.isContentEditable&&!(!this.nativeDraggable&&wt&&l&&l.tagName.toUpperCase()==="SELECT")&&(l=Y(l,o.draggable,n,!1),!(l&&l.animated)&&Rt!==l)){if(pt=L(l),_t=L(l,o.draggable),typeof f=="function"){if(f.call(this,t,l,this)){P({sortable:e,rootEl:u,name:"filter",targetEl:l,toEl:n,fromEl:n}),F("filter",e,{evt:t}),r&&t.preventDefault();return}}else if(f&&(f=f.split(",").some(function(d){if(d=Y(u,d.trim(),n,!1),d)return P({sortable:e,rootEl:d,name:"filter",targetEl:l,fromEl:n,toEl:n}),F("filter",e,{evt:t}),!0}),f)){r&&t.preventDefault();return}o.handle&&!Y(u,o.handle,n,!1)||this._prepareDragStart(t,s,l)}}},_prepareDragStart:function(t,e,n){var o=this,r=o.el,a=o.options,s=r.ownerDocument,l;if(n&&!c&&n.parentNode===r){var u=I(n);if(S=r,c=n,T=c.parentNode,st=c.nextSibling,Rt=n,kt=a.group,g.dragged=c,lt={target:c,clientX:(e||t).clientX,clientY:(e||t).clientY},Pe=lt.clientX-u.left,Ae=lt.clientY-u.top,this._lastX=(e||t).clientX,this._lastY=(e||t).clientY,c.style["will-change"]="all",l=function(){if(F("delayEnded",o,{evt:t}),g.eventCanceled){o._onDrop();return}o._disableDelayedDragEvents(),!ye&&o.nativeDraggable&&(c.draggable=!0),o._triggerDragStart(t,e),P({sortable:o,name:"choose",originalEvent:t}),M(c,a.chosenClass,!0)},a.ignore.split(",").forEach(function(f){Se(c,f.trim(),ae)}),y(s,"dragover",ut),y(s,"mousemove",ut),y(s,"touchmove",ut),a.supportPointer?(y(s,"pointerup",o._onDrop),!this.nativeDraggable&&y(s,"pointercancel",o._onDrop)):(y(s,"mouseup",o._onDrop),y(s,"touchend",o._onDrop),y(s,"touchcancel",o._onDrop)),ye&&this.nativeDraggable&&(this.options.touchStartThreshold=4,c.draggable=!0),F("delayStart",this,{evt:t}),a.delay&&(!a.delayOnTouchOnly||e)&&(!this.nativeDraggable||!(yt||K))){if(g.eventCanceled){this._onDrop();return}a.supportPointer?(y(s,"pointerup",o._disableDelayedDrag),y(s,"pointercancel",o._disableDelayedDrag)):(y(s,"mouseup",o._disableDelayedDrag),y(s,"touchend",o._disableDelayedDrag),y(s,"touchcancel",o._disableDelayedDrag)),y(s,"mousemove",o._delayedDragTouchMoveHandler),y(s,"touchmove",o._delayedDragTouchMoveHandler),a.supportPointer&&y(s,"pointermove",o._delayedDragTouchMoveHandler),o._dragStartTimer=setTimeout(l,a.delay)}else l()}},_delayedDragTouchMoveHandler:function(t){var e=t.touches?t.touches[0]:t;Math.max(Math.abs(e.clientX-this._lastX),Math.abs(e.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){c&&ae(c),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var t=this.el.ownerDocument;b(t,"mouseup",this._disableDelayedDrag),b(t,"touchend",this._disableDelayedDrag),b(t,"touchcancel",this._disableDelayedDrag),b(t,"pointerup",this._disableDelayedDrag),b(t,"pointercancel",this._disableDelayedDrag),b(t,"mousemove",this._delayedDragTouchMoveHandler),b(t,"touchmove",this._delayedDragTouchMoveHandler),b(t,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(t,e){e=e||t.pointerType=="touch"&&t,!this.nativeDraggable||e?this.options.supportPointer?y(document,"pointermove",this._onTouchMove):e?y(document,"touchmove",this._onTouchMove):y(document,"mousemove",this._onTouchMove):(y(c,"dragend",this),y(S,"dragstart",this._onDragStart));try{document.selection?$t(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(t,e){if(vt=!1,S&&c){F("dragStarted",this,{evt:e}),this.nativeDraggable&&y(document,"dragover",ln);var n=this.options;!t&&M(c,n.dragClass,!1),M(c,n.ghostClass,!0),g.active=this,t&&this._appendGhost(),P({sortable:this,name:"start",originalEvent:e})}else this._nulling()},_emulateDragOver:function(){if(G){this._lastX=G.clientX,this._lastY=G.clientY,Re();for(var t=document.elementFromPoint(G.clientX,G.clientY),e=t;t&&t.shadowRoot&&(t=t.shadowRoot.elementFromPoint(G.clientX,G.clientY),t!==e);)e=t;if(c.parentNode[z]._isOutsideThisEl(t),e)do{if(e[z]){var n=void 0;if(n=e[z]._onDragOver({clientX:G.clientX,clientY:G.clientY,target:t,rootEl:e}),n&&!this.options.dragoverBubble)break}t=e}while(e=De(e));ke()}},_onTouchMove:function(t){if(lt){var e=this.options,n=e.fallbackTolerance,o=e.fallbackOffset,r=t.touches?t.touches[0]:t,a=p&&ht(p,!0),s=p&&a&&a.a,l=p&&a&&a.d,u=jt&&x&&Te(x),f=(r.clientX-lt.clientX+o.x)/(s||1)+(u?u[0]-oe[0]:0)/(s||1),d=(r.clientY-lt.clientY+o.y)/(l||1)+(u?u[1]-oe[1]:0)/(l||1);if(!g.active&&!vt){if(n&&Math.max(Math.abs(r.clientX-this._lastX),Math.abs(r.clientY-this._lastY))<n)return;this._onDragStart(t,!0)}if(p){a?(a.e+=f-(ne||0),a.f+=d-(ie||0)):a={a:1,b:0,c:0,d:1,e:f,f:d};var w="matrix(".concat(a.a,",").concat(a.b,",").concat(a.c,",").concat(a.d,",").concat(a.e,",").concat(a.f,")");h(p,"webkitTransform",w),h(p,"mozTransform",w),h(p,"msTransform",w),h(p,"transform",w),ne=f,ie=d,G=r}t.cancelable&&t.preventDefault()}},_appendGhost:function(){if(!p){var t=this.options.fallbackOnBody?document.body:S,e=I(c,!0,jt,!0,t),n=this.options;if(jt){for(x=t;h(x,"position")==="static"&&h(x,"transform")==="none"&&x!==document;)x=x.parentNode;x!==document.body&&x!==document.documentElement?(x===document&&(x=U()),e.top+=x.scrollTop,e.left+=x.scrollLeft):x=U(),oe=Te(x)}p=c.cloneNode(!0),M(p,n.ghostClass,!1),M(p,n.fallbackClass,!0),M(p,n.dragClass,!0),h(p,"transition",""),h(p,"transform",""),h(p,"box-sizing","border-box"),h(p,"margin",0),h(p,"top",e.top),h(p,"left",e.left),h(p,"width",e.width),h(p,"height",e.height),h(p,"opacity","0.8"),h(p,"position",jt?"absolute":"fixed"),h(p,"zIndex","100000"),h(p,"pointerEvents","none"),g.ghost=p,t.appendChild(p),h(p,"transform-origin",Pe/parseInt(p.style.width)*100+"% "+Ae/parseInt(p.style.height)*100+"%")}},_onDragStart:function(t,e){var n=this,o=t.dataTransfer,r=n.options;if(F("dragStart",this,{evt:t}),g.eventCanceled){this._onDrop();return}F("setupClone",this),g.eventCanceled||(C=Oe(c),C.removeAttribute("id"),C.draggable=!1,C.style["will-change"]="",this._hideClone(),M(C,this.options.chosenClass,!1),g.clone=C),n.cloneId=$t(function(){F("clone",n),!g.eventCanceled&&(n.options.removeCloneOnHide||S.insertBefore(C,c),n._hideClone(),P({sortable:n,name:"clone"}))}),!e&&M(c,r.dragClass,!0),e?(Lt=!0,n._loopId=setInterval(n._emulateDragOver,50)):(b(document,"mouseup",n._onDrop),b(document,"touchend",n._onDrop),b(document,"touchcancel",n._onDrop),o&&(o.effectAllowed="move",r.setData&&r.setData.call(n,o,c)),y(document,"drop",n),h(c,"transform","translateZ(0)")),vt=!0,n._dragStartId=$t(n._dragStarted.bind(n,e,t)),y(document,"selectstart",n),St=!0,window.getSelection().removeAllRanges(),wt&&h(document.body,"user-select","none")},_onDragOver:function(t){var e=this.el,n=t.target,o,r,a,s=this.options,l=s.group,u=g.active,f=kt===l,d=s.sort,w=O||u,_,E=this,D=!1;if(re)return;function H(zt,yn){F(zt,E,j({evt:t,isOwner:f,axis:_?"vertical":"horizontal",revert:a,dragRect:o,targetRect:r,canSort:d,fromSortable:w,target:n,completed:A,onMove:function(Ge,wn){return Ut(S,e,c,o,Ge,I(Ge),t,wn)},changed:W},yn))}function Z(){H("dragOverAnimationCapture"),E.captureAnimationState(),E!==w&&w.captureAnimationState()}function A(zt){return H("dragOverCompleted",{insertion:zt}),zt&&(f?u._hideClone():u._showClone(E),E!==w&&(M(c,O?O.options.ghostClass:u.options.ghostClass,!1),M(c,s.ghostClass,!0)),O!==E&&E!==g.active?O=E:E===g.active&&O&&(O=null),w===E&&(E._ignoreWhileAnimating=n),E.animateAll(function(){H("dragOverAnimationComplete"),E._ignoreWhileAnimating=null}),E!==w&&(w.animateAll(),w._ignoreWhileAnimating=null)),(n===c&&!c.animated||n===e&&!n.animated)&&(bt=null),!s.dragoverBubble&&!t.rootEl&&n!==document&&(c.parentNode[z]._isOutsideThisEl(t.target),!zt&&ut(t)),!s.dragoverBubble&&t.stopPropagation&&t.stopPropagation(),D=!0}function W(){B=L(c),nt=L(c,s.draggable),P({sortable:E,name:"change",toEl:e,newIndex:B,newDraggableIndex:nt,originalEvent:t})}if(t.preventDefault!==void 0&&t.cancelable&&t.preventDefault(),n=Y(n,s.draggable,e,!0),H("dragOver"),g.eventCanceled)return D;if(c.contains(t.target)||n.animated&&n.animatingX&&n.animatingY||E._ignoreWhileAnimating===n)return A(!1);if(Lt=!1,u&&!s.disabled&&(f?d||(a=T!==S):O===this||(this.lastPutMode=kt.checkPull(this,u,c,t))&&l.checkPut(this,u,c,t))){if(_=this._getDirection(t,n)==="vertical",o=I(c),H("dragOverValid"),g.eventCanceled)return D;if(a)return T=S,Z(),this._hideClone(),H("revert"),g.eventCanceled||(st?S.insertBefore(c,st):S.appendChild(c)),A(!0);var R=Jt(e,s.draggable);if(!R||fn(t,_,this)&&!R.animated){if(R===c)return A(!1);if(R&&e===t.target&&(n=R),n&&(r=I(n)),Ut(S,e,c,o,n,r,t,!!n)!==!1)return Z(),R&&R.nextSibling?e.insertBefore(c,R.nextSibling):e.appendChild(c),T=e,W(),A(!0)}else if(R&&dn(t,_,this)){var ct=mt(e,0,s,!0);if(ct===c)return A(!1);if(n=ct,r=I(n),Ut(S,e,c,o,n,r,t,!1)!==!1)return Z(),e.insertBefore(c,ct),T=e,W(),A(!0)}else if(n.parentNode===e){r=I(n);var $=0,dt,Ot=c.parentNode!==e,k=!an(c.animated&&c.toRect||o,n.animated&&n.toRect||r,_),xt=_?"top":"left",it=Ce(n,"top","top")||Ce(c,"top","top"),Pt=it?it.scrollTop:void 0;bt!==n&&(dt=r[xt],Tt=!1,Yt=!k&&s.invertSwap||Ot),$=hn(t,n,r,_,k?1:s.swapThreshold,s.invertedSwapThreshold==null?s.swapThreshold:s.invertedSwapThreshold,Yt,bt===n);var Q;if($!==0){var ft=L(c);do ft-=$,Q=T.children[ft];while(Q&&(h(Q,"display")==="none"||Q===p))}if($===0||Q===n)return A(!1);bt=n,Ct=$;var At=n.nextElementSibling,ot=!1;ot=$===1;var Kt=Ut(S,e,c,o,n,r,t,ot);if(Kt!==!1)return(Kt===1||Kt===-1)&&(ot=Kt===1),re=!0,setTimeout(cn,30),Z(),ot&&!At?e.appendChild(c):n.parentNode.insertBefore(c,ot?At:n),it&&Ne(it,0,Pt-it.scrollTop),T=c.parentNode,dt!==void 0&&!Yt&&(Gt=Math.abs(dt-I(n)[xt])),W(),A(!0)}if(e.contains(c))return A(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){b(document,"mousemove",this._onTouchMove),b(document,"touchmove",this._onTouchMove),b(document,"pointermove",this._onTouchMove),b(document,"dragover",ut),b(document,"mousemove",ut),b(document,"touchmove",ut)},_offUpEvents:function(){var t=this.el.ownerDocument;b(t,"mouseup",this._onDrop),b(t,"touchend",this._onDrop),b(t,"pointerup",this._onDrop),b(t,"pointercancel",this._onDrop),b(t,"touchcancel",this._onDrop),b(document,"selectstart",this)},_onDrop:function(t){var e=this.el,n=this.options;if(B=L(c),nt=L(c,n.draggable),F("drop",this,{evt:t}),T=c&&c.parentNode,B=L(c),nt=L(c,n.draggable),g.eventCanceled){this._nulling();return}vt=!1,Yt=!1,Tt=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),se(this.cloneId),se(this._dragStartId),this.nativeDraggable&&(b(document,"drop",this),b(e,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),wt&&h(document.body,"user-select",""),h(c,"transform",""),t&&(St&&(t.cancelable&&t.preventDefault(),!n.dropBubble&&t.stopPropagation()),p&&p.parentNode&&p.parentNode.removeChild(p),(S===T||O&&O.lastPutMode!=="clone")&&C&&C.parentNode&&C.parentNode.removeChild(C),c&&(this.nativeDraggable&&b(c,"dragend",this),ae(c),c.style["will-change"]="",St&&!vt&&M(c,O?O.options.ghostClass:this.options.ghostClass,!1),M(c,this.options.chosenClass,!1),P({sortable:this,name:"unchoose",toEl:T,newIndex:null,newDraggableIndex:null,originalEvent:t}),S!==T?(B>=0&&(P({rootEl:T,name:"add",toEl:T,fromEl:S,originalEvent:t}),P({sortable:this,name:"remove",toEl:T,originalEvent:t}),P({rootEl:T,name:"sort",toEl:T,fromEl:S,originalEvent:t}),P({sortable:this,name:"sort",toEl:T,originalEvent:t})),O&&O.save()):B!==pt&&B>=0&&(P({sortable:this,name:"update",toEl:T,originalEvent:t}),P({sortable:this,name:"sort",toEl:T,originalEvent:t})),g.active&&((B==null||B===-1)&&(B=pt,nt=_t),P({sortable:this,name:"end",toEl:T,originalEvent:t}),this.save()))),this._nulling()},_nulling:function(){F("nulling",this),S=c=T=p=st=C=Rt=et=lt=G=St=B=nt=pt=_t=bt=Ct=O=kt=g.dragged=g.ghost=g.clone=g.active=null,Ht.forEach(function(t){t.checked=!0}),Ht.length=ne=ie=0},handleEvent:function(t){switch(t.type){case"drop":case"dragend":this._onDrop(t);break;case"dragenter":case"dragover":c&&(this._onDragOver(t),un(t));break;case"selectstart":t.preventDefault();break}},toArray:function(){for(var t=[],e,n=this.el.children,o=0,r=n.length,a=this.options;o<r;o++)e=n[o],Y(e,a.draggable,this.el,!1)&&t.push(e.getAttribute(a.dataIdAttr)||gn(e));return t},sort:function(t,e){var n={},o=this.el;this.toArray().forEach(function(r,a){var s=o.children[a];Y(s,this.options.draggable,o,!1)&&(n[r]=s)},this),e&&this.captureAnimationState(),t.forEach(function(r){n[r]&&(o.removeChild(n[r]),o.appendChild(n[r]))}),e&&this.animateAll()},save:function(){var t=this.options.store;t&&t.set&&t.set(this)},closest:function(t,e){return Y(t,e||this.options.draggable,this.el,!1)},option:function(t,e){var n=this.options;if(e===void 0)return n[t];var o=Dt.modifyOption(this,t,e);typeof o<"u"?n[t]=o:n[t]=e,t==="group"&&Be(n)},destroy:function(){F("destroy",this);var t=this.el;t[z]=null,b(t,"mousedown",this._onTapStart),b(t,"touchstart",this._onTapStart),b(t,"pointerdown",this._onTapStart),this.nativeDraggable&&(b(t,"dragover",this),b(t,"dragenter",this)),Array.prototype.forEach.call(t.querySelectorAll("[draggable]"),function(e){e.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Xt.splice(Xt.indexOf(this.el),1),this.el=t=null},_hideClone:function(){if(!et){if(F("hideClone",this),g.eventCanceled)return;h(C,"display","none"),this.options.removeCloneOnHide&&C.parentNode&&C.parentNode.removeChild(C),et=!0}},_showClone:function(t){if(t.lastPutMode!=="clone"){this._hideClone();return}if(et){if(F("showClone",this),g.eventCanceled)return;c.parentNode==S&&!this.options.group.revertClone?S.insertBefore(C,c):st?S.insertBefore(C,st):S.appendChild(C),this.options.group.revertClone&&this.animate(c,C),h(C,"display",""),et=!1}}};function un(i){i.dataTransfer&&(i.dataTransfer.dropEffect="move"),i.cancelable&&i.preventDefault()}function Ut(i,t,e,n,o,r,a,s){var l,u=i[z],f=u.options.onMove,d;return window.CustomEvent&&!K&&!yt?l=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(l=document.createEvent("Event"),l.initEvent("move",!0,!0)),l.to=t,l.from=i,l.dragged=e,l.draggedRect=n,l.related=o||t,l.relatedRect=r||I(t),l.willInsertAfter=s,l.originalEvent=a,i.dispatchEvent(l),f&&(d=f.call(u,l,a)),d}function ae(i){i.draggable=!1}function cn(){re=!1}function dn(i,t,e){var n=I(mt(e.el,0,e.options,!0)),o=xe(e.el,e.options,p),r=10;return t?i.clientX<o.left-r||i.clientY<n.top&&i.clientX<n.right:i.clientY<o.top-r||i.clientY<n.bottom&&i.clientX<n.left}function fn(i,t,e){var n=I(Jt(e.el,e.options.draggable)),o=xe(e.el,e.options,p),r=10;return t?i.clientX>o.right+r||i.clientY>n.bottom&&i.clientX>n.left:i.clientY>o.bottom+r||i.clientX>n.right&&i.clientY>n.top}function hn(i,t,e,n,o,r,a,s){var l=n?i.clientY:i.clientX,u=n?e.height:e.width,f=n?e.top:e.left,d=n?e.bottom:e.right,w=!1;if(!a){if(s&&Gt<u*o){if(!Tt&&(Ct===1?l>f+u*r/2:l<d-u*r/2)&&(Tt=!0),Tt)w=!0;else if(Ct===1?l<f+Gt:l>d-Gt)return-Ct}else if(l>f+u*(1-o)/2&&l<d-u*(1-o)/2)return mn(t)}return w=w||a,w&&(l<f+u*r/2||l>d-u*r/2)?l>f+u/2?1:-1:0}function mn(i){return L(c)<L(i)?1:-1}function gn(i){for(var t=i.tagName+i.className+i.src+i.href+i.textContent,e=t.length,n=0;e--;)n+=t.charCodeAt(e);return n.toString(36)}function pn(i){Ht.length=0;for(var t=i.getElementsByTagName("input"),e=t.length;e--;){var n=t[e];n.checked&&Ht.push(n)}}function $t(i){return setTimeout(i,0)}function se(i){return clearTimeout(i)}Wt&&y(document,"touchmove",function(i){(g.active||vt)&&i.cancelable&&i.preventDefault()}),g.utils={on:y,off:b,css:h,find:Se,is:function(t,e){return!!Y(t,e,t,!1)},extend:Ze,throttle:Ie,closest:Y,toggleClass:M,clone:Oe,index:L,nextTick:$t,cancelNextTick:se,detectDirection:Me,getChild:mt,expando:z},g.get=function(i){return i[z]},g.mount=function(){for(var i=arguments.length,t=new Array(i),e=0;e<i;e++)t[e]=arguments[e];t[0].constructor===Array&&(t=t[0]),t.forEach(function(n){if(!n.prototype||!n.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(n));n.utils&&(g.utils=j(j({},g.utils),n.utils)),Dt.mount(n)})},g.create=function(i,t){return new g(i,t)},g.version=Ve;var N=[],It,le,ue=!1,ce,de,qt,Nt;function vn(){function i(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var t in this)t.charAt(0)==="_"&&typeof this[t]=="function"&&(this[t]=this[t].bind(this))}return i.prototype={dragStarted:function(e){var n=e.originalEvent;this.sortable.nativeDraggable?y(document,"dragover",this._handleAutoScroll):this.options.supportPointer?y(document,"pointermove",this._handleFallbackAutoScroll):n.touches?y(document,"touchmove",this._handleFallbackAutoScroll):y(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(e){var n=e.originalEvent;!this.options.dragOverBubble&&!n.rootEl&&this._handleAutoScroll(n)},drop:function(){this.sortable.nativeDraggable?b(document,"dragover",this._handleAutoScroll):(b(document,"pointermove",this._handleFallbackAutoScroll),b(document,"touchmove",this._handleFallbackAutoScroll),b(document,"mousemove",this._handleFallbackAutoScroll)),Le(),Vt(),Qe()},nulling:function(){qt=le=It=ue=Nt=ce=de=null,N.length=0},_handleFallbackAutoScroll:function(e){this._handleAutoScroll(e,!0)},_handleAutoScroll:function(e,n){var o=this,r=(e.touches?e.touches[0]:e).clientX,a=(e.touches?e.touches[0]:e).clientY,s=document.elementFromPoint(r,a);if(qt=e,n||this.options.forceAutoScrollFallback||yt||K||wt){fe(e,this.options,s,n);var l=tt(s,!0);ue&&(!Nt||r!==ce||a!==de)&&(Nt&&Le(),Nt=setInterval(function(){var u=tt(document.elementFromPoint(r,a),!0);u!==l&&(l=u,Vt()),fe(e,o.options,u,n)},10),ce=r,de=a)}else{if(!this.options.bubbleScroll||tt(s,!0)===U()){Vt();return}fe(e,this.options,tt(s,!1),!1)}}},q(i,{pluginName:"scroll",initializeByDefault:!0})}function Vt(){N.forEach(function(i){clearInterval(i.pid)}),N=[]}function Le(){clearInterval(Nt)}var fe=Ie(function(i,t,e,n){if(t.scroll){var o=(i.touches?i.touches[0]:i).clientX,r=(i.touches?i.touches[0]:i).clientY,a=t.scrollSensitivity,s=t.scrollSpeed,l=U(),u=!1,f;le!==e&&(le=e,Vt(),It=t.scroll,f=t.scrollFn,It===!0&&(It=tt(e,!0)));var d=0,w=It;do{var _=w,E=I(_),D=E.top,H=E.bottom,Z=E.left,A=E.right,W=E.width,R=E.height,ct=void 0,$=void 0,dt=_.scrollWidth,Ot=_.scrollHeight,k=h(_),xt=_.scrollLeft,it=_.scrollTop;_===l?(ct=W<dt&&(k.overflowX==="auto"||k.overflowX==="scroll"||k.overflowX==="visible"),$=R<Ot&&(k.overflowY==="auto"||k.overflowY==="scroll"||k.overflowY==="visible")):(ct=W<dt&&(k.overflowX==="auto"||k.overflowX==="scroll"),$=R<Ot&&(k.overflowY==="auto"||k.overflowY==="scroll"));var Pt=ct&&(Math.abs(A-o)<=a&&xt+W<dt)-(Math.abs(Z-o)<=a&&!!xt),Q=$&&(Math.abs(H-r)<=a&&it+R<Ot)-(Math.abs(D-r)<=a&&!!it);if(!N[d])for(var ft=0;ft<=d;ft++)N[ft]||(N[ft]={});(N[d].vx!=Pt||N[d].vy!=Q||N[d].el!==_)&&(N[d].el=_,N[d].vx=Pt,N[d].vy=Q,clearInterval(N[d].pid),(Pt!=0||Q!=0)&&(u=!0,N[d].pid=setInterval((function(){n&&this.layer===0&&g.active._onTouchMove(qt);var At=N[this.layer].vy?N[this.layer].vy*s:0,ot=N[this.layer].vx?N[this.layer].vx*s:0;typeof f=="function"&&f.call(g.dragged.parentNode[z],ot,At,i,qt,N[this.layer].el)!=="continue"||Ne(N[this.layer].el,ot,At)}).bind({layer:d}),24))),d++}while(t.bubbleScroll&&w!==l&&(w=tt(w,!1)));ue=u}},30),Xe=function(t){var e=t.originalEvent,n=t.putSortable,o=t.dragEl,r=t.activeSortable,a=t.dispatchSortableEvent,s=t.hideGhostForTarget,l=t.unhideGhostForTarget;if(e){var u=n||r;s();var f=e.changedTouches&&e.changedTouches.length?e.changedTouches[0]:e,d=document.elementFromPoint(f.clientX,f.clientY);l(),u&&!u.el.contains(d)&&(a("spill"),this.onSpill({dragEl:o,putSortable:n}))}};function he(){}he.prototype={startIndex:null,dragStart:function(t){var e=t.oldDraggableIndex;this.startIndex=e},onSpill:function(t){var e=t.dragEl,n=t.putSortable;this.sortable.captureAnimationState(),n&&n.captureAnimationState();var o=mt(this.sortable.el,this.startIndex,this.options);o?this.sortable.el.insertBefore(e,o):this.sortable.el.appendChild(e),this.sortable.animateAll(),n&&n.animateAll()},drop:Xe},q(he,{pluginName:"revertOnSpill"});function me(){}me.prototype={onSpill:function(t){var e=t.dragEl,n=t.putSortable,o=n||this.sortable;o.captureAnimationState(),e.parentNode&&e.parentNode.removeChild(e),o.animateAll()},drop:Xe},q(me,{pluginName:"removeOnSpill"}),g.mount(new vn),g.mount(me,he);class bn extends He{oninit(t){super.oninit(t),this.loading=!1,this.buttonsCustomizationList=[],this.loadResults()}initSort(){const t=document.getElementById("buttonsCustomizationSortableItems");t&&g.create(t,{animation:150,swapThreshold:.65,onEnd:e=>this.updateSort(e)})}content(t){const e=X||v.m||window.m;return e?e("div",{className:"ExtensionPage-settings FlarumBadgesPage"},[e("div",{className:"container"},[e("div",{style:"padding-bottom:10px"},[e(rt,{className:"Button",onclick:()=>v.modal.show(Ft,{onSave:()=>this.refreshData()})},v.translator.trans("client1-buttons-customization.admin.link-add"))]),e("ul",{id:"buttonsCustomizationSortableItems",style:"padding:0px;list-style-type: none;",oncreate:this.initSort.bind(this)},this.buttonsCustomizationList.map(n=>e("li",{"data-item-id":n.id(),style:"margin-top:5px;background: var(--body-bg);"},e(je,{ButtonsCustomizationItemData:n,onSave:()=>this.refreshData()}))))])]):"Error: Mithril not loaded"}updateSort(t){const e=t.newIndex,n=t.oldIndex;if(e!==n){const o=t.from?.children,r={};if(o){for(let a=0;a<o.length;a++){const l=o[a].getAttribute("data-item-id");l&&(r[l]=a)}v.request({url:`${v.forum.attribute("apiUrl")}/buttonsCustomizationList/order`,method:"POST",body:{buttonsCustomizationOrder:r}})}}}parseResults(t){return this.buttonsCustomizationList.push(...t),v&&v.m&&v.m.redraw?v.m.redraw():X&&X.redraw&&X.redraw(),t}loadResults(){return v.store.find("buttonsCustomizationList").catch(()=>[]).then(this.parseResults.bind(this))}refreshData(){this.buttonsCustomizationList=[],v.store.data.buttonsCustomizationList={},this.loadResults().then(()=>{v&&v.m&&v.m.redraw?v.m.redraw():X&&X.redraw&&X.redraw()})}}class Ye extends at{}Object.assign(Ye.prototype,{id:at.attribute("id"),name:at.attribute("name"),icon:at.attribute("icon"),color:at.attribute("color"),url:at.attribute("url"),sort:at.attribute("sort")}),v.initializers.add("wusong8899-buttons-customization",()=>{v.store.models.buttonsCustomizationList=Ye,v.extensionData.for("wusong8899-client1-buttons-customization").registerPage(bn)})})(flarum.core.compat["admin/app"],flarum.core.compat["admin/components/ExtensionPage"],flarum.core.compat["common/components/Button"],flarum.core.compat["common/components/Modal"],flarum.core.compat["common/utils/Stream"],flarum.core.compat["common/Component"],m,flarum.core.compat["common/Model"]);
//# sourceMappingURL=admin.js.map

module.exports={};