(function(an,fe,on){"use strict";function or(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function ur(e,t,n){return t&&or(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}/*!
 * Splide.js
 * Version  : 4.1.4
 * License  : MIT
 * Copyright: 2022 Naotoshi Fujita
 */var un="(prefers-reduced-motion: reduce)",De=1,sr=2,we=3,Me=4,Ke=5,dt=6,gt=7,cr={CREATED:De,MOUNTED:sr,IDLE:we,MOVING:Me,SCROLLING:Ke,DRAGGING:dt,DESTROYED:gt};function ve(e){e.length=0}function me(e,t,n){return Array.prototype.slice.call(e,t,n)}function V(e){return e.bind.apply(e,[null].concat(me(arguments,1)))}var sn=setTimeout,Ct=function(){};function cn(e){return requestAnimationFrame(e)}function Et(e,t){return typeof t===e}function He(e){return!wt(e)&&Et("object",e)}var Dt=Array.isArray,ln=V(Et,"function"),_e=V(Et,"string"),We=V(Et,"undefined");function wt(e){return e===null}function fn(e){try{return e instanceof(e.ownerDocument.defaultView||window).HTMLElement}catch{return!1}}function Xe(e){return Dt(e)?e:[e]}function te(e,t){Xe(e).forEach(t)}function Mt(e,t){return e.indexOf(t)>-1}function ht(e,t){return e.push.apply(e,Xe(t)),e}function de(e,t,n){e&&te(t,function(r){r&&e.classList[n?"add":"remove"](r)})}function se(e,t){de(e,_e(t)?t.split(" "):t,!0)}function qe(e,t){te(t,e.appendChild.bind(e))}function xt(e,t){te(e,function(n){var r=(t||n).parentNode;r&&r.insertBefore(n,t)})}function je(e,t){return fn(e)&&(e.msMatchesSelector||e.matches).call(e,t)}function vn(e,t){var n=e?me(e.children):[];return t?n.filter(function(r){return je(r,t)}):n}function Ze(e,t){return t?vn(e,t)[0]:e.firstElementChild}var Je=Object.keys;function Le(e,t,n){return e&&(n?Je(e).reverse():Je(e)).forEach(function(r){r!=="__proto__"&&t(e[r],r)}),e}function Qe(e){return me(arguments,1).forEach(function(t){Le(t,function(n,r){e[r]=t[r]})}),e}function Ae(e){return me(arguments,1).forEach(function(t){Le(t,function(n,r){Dt(n)?e[r]=n.slice():He(n)?e[r]=Ae({},He(e[r])?e[r]:{},n):e[r]=n})}),e}function dn(e,t){te(t||Je(e),function(n){delete e[n]})}function ce(e,t){te(e,function(n){te(t,function(r){n&&n.removeAttribute(r)})})}function M(e,t,n){He(t)?Le(t,function(r,i){M(e,i,r)}):te(e,function(r){wt(n)||n===""?ce(r,t):r.setAttribute(t,String(n))})}function xe(e,t,n){var r=document.createElement(e);return t&&(_e(t)?se(r,t):M(r,t)),n&&qe(n,r),r}function ie(e,t,n){if(We(n))return getComputedStyle(e)[t];wt(n)||(e.style[t]=""+n)}function et(e,t){ie(e,"display",t)}function gn(e){e.setActive&&e.setActive()||e.focus({preventScroll:!0})}function ae(e,t){return e.getAttribute(t)}function En(e,t){return e&&e.classList.contains(t)}function ne(e){return e.getBoundingClientRect()}function Ne(e){te(e,function(t){t&&t.parentNode&&t.parentNode.removeChild(t)})}function hn(e){return Ze(new DOMParser().parseFromString(e,"text/html").body)}function ge(e,t){e.preventDefault(),t&&(e.stopPropagation(),e.stopImmediatePropagation())}function Tn(e,t){return e&&e.querySelector(t)}function Ft(e,t){return t?me(e.querySelectorAll(t)):[]}function Ee(e,t){de(e,t,!1)}function Gt(e){return e.timeStamp}function be(e){return _e(e)?e:e?e+"px":""}var tt="splide",kt="data-"+tt;function nt(e,t){if(!e)throw new Error("["+tt+"] "+(t||""))}var Se=Math.min,Tt=Math.max,mt=Math.floor,rt=Math.ceil,Z=Math.abs;function mn(e,t,n){return Z(e-t)<n}function _t(e,t,n,r){var i=Se(t,n),o=Tt(t,n);return r?i<e&&e<o:i<=e&&e<=o}function Fe(e,t,n){var r=Se(t,n),i=Tt(t,n);return Se(Tt(r,e),i)}function Ut(e){return+(e>0)-+(e<0)}function Vt(e,t){return te(t,function(n){e=e.replace("%s",""+n)}),e}function Bt(e){return e<10?"0"+e:""+e}var _n={};function lr(e){return""+e+Bt(_n[e]=(_n[e]||0)+1)}function An(){var e=[];function t(a,u,s,f){i(a,u,function(c,E,d){var g="addEventListener"in c,l=g?c.removeEventListener.bind(c,E,s,f):c.removeListener.bind(c,s);g?c.addEventListener(E,s,f):c.addListener(s),e.push([c,E,d,s,l])})}function n(a,u,s){i(a,u,function(f,c,E){e=e.filter(function(d){return d[0]===f&&d[1]===c&&d[2]===E&&(!s||d[3]===s)?(d[4](),!1):!0})})}function r(a,u,s){var f,c=!0;return typeof CustomEvent=="function"?f=new CustomEvent(u,{bubbles:c,detail:s}):(f=document.createEvent("CustomEvent"),f.initCustomEvent(u,c,!1,s)),a.dispatchEvent(f),f}function i(a,u,s){te(a,function(f){f&&te(u,function(c){c.split(" ").forEach(function(E){var d=E.split(".");s(f,d[0],d[1])})})})}function o(){e.forEach(function(a){a[4]()}),ve(e)}return{bind:t,unbind:n,dispatch:r,destroy:o}}var Oe="mounted",Sn="ready",pe="move",it="moved",pn="click",fr="active",vr="inactive",dr="visible",gr="hidden",W="refresh",J="updated",at="resize",zt="resized",Er="drag",hr="dragging",Tr="dragged",Yt="scroll",Ge="scrolled",mr="overflow",yn="destroy",_r="arrows:mounted",Ar="arrows:updated",Sr="pagination:mounted",pr="pagination:updated",In="navigation:mounted",Ln="autoplay:play",yr="autoplay:playing",Nn="autoplay:pause",bn="lazyload:loaded",On="sk",Rn="sh",At="ei";function Y(e){var t=e?e.event.bus:document.createDocumentFragment(),n=An();function r(o,a){n.bind(t,Xe(o).join(" "),function(u){a.apply(a,Dt(u.detail)?u.detail:[])})}function i(o){n.dispatch(t,o,me(arguments,1))}return e&&e.event.on(yn,n.destroy),Qe(n,{bus:t,on:r,off:V(n.unbind,t),emit:i})}function St(e,t,n,r){var i=Date.now,o,a=0,u,s=!0,f=0;function c(){if(!s){if(a=e?Se((i()-o)/e,1):1,n&&n(a),a>=1&&(t(),o=i(),r&&++f>=r))return d();u=cn(c)}}function E(m){m||l(),o=i()-(m?a*e:0),s=!1,u=cn(c)}function d(){s=!0}function g(){o=i(),a=0,n&&n(a)}function l(){u&&cancelAnimationFrame(u),a=0,u=0,s=!0}function v(m){e=m}function _(){return s}return{start:E,rewind:g,pause:d,cancel:l,set:v,isPaused:_}}function Ir(e){var t=e;function n(i){t=i}function r(i){return Mt(Xe(i),t)}return{set:n,is:r}}function Lr(e,t){var n=St(0,e,null,1);return function(){n.isPaused()&&n.start()}}function Nr(e,t,n){var r=e.state,i=n.breakpoints||{},o=n.reducedMotion||{},a=An(),u=[];function s(){var l=n.mediaQuery==="min";Je(i).sort(function(v,_){return l?+v-+_:+_-+v}).forEach(function(v){c(i[v],"("+(l?"min":"max")+"-width:"+v+"px)")}),c(o,un),E()}function f(l){l&&a.destroy()}function c(l,v){var _=matchMedia(v);a.bind(_,"change",E),u.push([l,_])}function E(){var l=r.is(gt),v=n.direction,_=u.reduce(function(m,T){return Ae(m,T[1].matches?T[0]:{})},{});dn(n),g(_),n.destroy?e.destroy(n.destroy==="completely"):l?(f(!0),e.mount()):v!==n.direction&&e.refresh()}function d(l){matchMedia(un).matches&&(l?Ae(n,o):dn(n,Je(o)))}function g(l,v,_){Ae(n,l),v&&Ae(Object.getPrototypeOf(n),l),(_||!r.is(De))&&e.emit(J,n)}return{setup:s,destroy:f,reduce:d,set:g}}var pt="Arrow",yt=pt+"Left",It=pt+"Right",Pn=pt+"Up",Cn=pt+"Down",Dn="rtl",Lt="ttb",$t={width:["height"],left:["top","right"],right:["bottom","left"],x:["y"],X:["Y"],Y:["X"],ArrowLeft:[Pn,It],ArrowRight:[Cn,yt]};function br(e,t,n){function r(o,a,u){u=u||n.direction;var s=u===Dn&&!a?1:u===Lt?0:-1;return $t[o]&&$t[o][s]||o.replace(/width|left|right/i,function(f,c){var E=$t[f.toLowerCase()][s]||f;return c>0?E.charAt(0).toUpperCase()+E.slice(1):E})}function i(o){return o*(n.direction===Dn?1:-1)}return{resolve:r,orient:i}}var he="role",ke="tabindex",Or="disabled",oe="aria-",ot=oe+"controls",wn=oe+"current",Mn=oe+"selected",re=oe+"label",Kt=oe+"labelledby",xn=oe+"hidden",Ht=oe+"orientation",ut=oe+"roledescription",Fn=oe+"live",Gn=oe+"busy",kn=oe+"atomic",Wt=[he,ke,Or,ot,wn,re,Kt,xn,Ht,ut],le=tt+"__",ye="is-",Xt=tt,Un=le+"track",Rr=le+"list",Nt=le+"slide",Vn=Nt+"--clone",Pr=Nt+"__container",qt=le+"arrows",bt=le+"arrow",Bn=bt+"--prev",zn=bt+"--next",Ot=le+"pagination",Yn=Ot+"__page",Cr=le+"progress",Dr=Cr+"__bar",wr=le+"toggle",Mr=le+"spinner",xr=le+"sr",Fr=ye+"initialized",Re=ye+"active",$n=ye+"prev",Kn=ye+"next",jt=ye+"visible",Zt=ye+"loading",Hn=ye+"focus-in",Wn=ye+"overflow",Gr=[Re,jt,$n,Kn,Zt,Hn,Wn],kr={slide:Nt,clone:Vn,arrows:qt,arrow:bt,prev:Bn,next:zn,pagination:Ot,page:Yn,spinner:Mr};function Ur(e,t){if(ln(e.closest))return e.closest(t);for(var n=e;n&&n.nodeType===1&&!je(n,t);)n=n.parentElement;return n}var Vr=5,Xn=200,qn="touchstart mousedown",Jt="touchmove mousemove",Qt="touchend touchcancel mouseup click";function Br(e,t,n){var r=Y(e),i=r.on,o=r.bind,a=e.root,u=n.i18n,s={},f=[],c=[],E=[],d,g,l;function v(){h(),D(),T()}function _(){i(W,m),i(W,v),i(J,T),o(document,qn+" keydown",function(S){l=S.type==="keydown"},{capture:!0}),o(a,"focusin",function(){de(a,Hn,!!l)})}function m(S){var O=Wt.concat("style");ve(f),Ee(a,c),Ee(d,E),ce([d,g],O),ce(a,S?O:["style",ut])}function T(){Ee(a,c),Ee(d,E),c=x(Xt),E=x(Un),se(a,c),se(d,E),M(a,re,n.label),M(a,Kt,n.labelledby)}function h(){d=b("."+Un),g=Ze(d,"."+Rr),nt(d&&g,"A track/list element is missing."),ht(f,vn(g,"."+Nt+":not(."+Vn+")")),Le({arrows:qt,pagination:Ot,prev:Bn,next:zn,bar:Dr,toggle:wr},function(S,O){s[O]=b("."+S)}),Qe(s,{root:a,track:d,list:g,slides:f})}function D(){var S=a.id||lr(tt),O=n.role;a.id=S,d.id=d.id||S+"-track",g.id=g.id||S+"-list",!ae(a,he)&&a.tagName!=="SECTION"&&O&&M(a,he,O),M(a,ut,u.carousel),M(g,he,"presentation")}function b(S){var O=Tn(a,S);return O&&Ur(O,"."+Xt)===a?O:void 0}function x(S){return[S+"--"+n.type,S+"--"+n.direction,n.drag&&S+"--draggable",n.isNavigation&&S+"--nav",S===Xt&&Re]}return Qe(s,{setup:v,mount:_,destroy:m})}var Ue="slide",Ve="loop",st="fade";function zr(e,t,n,r){var i=Y(e),o=i.on,a=i.emit,u=i.bind,s=e.Components,f=e.root,c=e.options,E=c.isNavigation,d=c.updateOnMove,g=c.i18n,l=c.pagination,v=c.slideFocus,_=s.Direction.resolve,m=ae(r,"style"),T=ae(r,re),h=n>-1,D=Ze(r,"."+Pr),b;function x(){h||(r.id=f.id+"-slide"+Bt(t+1),M(r,he,l?"tabpanel":"group"),M(r,ut,g.slide),M(r,re,T||Vt(g.slideLabel,[t+1,e.length]))),S()}function S(){u(r,"click",V(a,pn,w)),u(r,"keydown",V(a,On,w)),o([it,Rn,Ge],y),o(In,k),d&&o(pe,C)}function O(){b=!0,i.destroy(),Ee(r,Gr),ce(r,Wt),M(r,"style",m),M(r,re,T||"")}function k(){var P=e.splides.map(function(p){var R=p.splide.Components.Slides.getAt(t);return R?R.slide.id:""}).join(" ");M(r,re,Vt(g.slideX,(h?n:t)+1)),M(r,ot,P),M(r,he,v?"button":""),v&&ce(r,ut)}function C(){b||y()}function y(){if(!b){var P=e.index;I(),L(),de(r,$n,t===P-1),de(r,Kn,t===P+1)}}function I(){var P=G();P!==En(r,Re)&&(de(r,Re,P),M(r,wn,E&&P||""),a(P?fr:vr,w))}function L(){var P=K(),p=!P&&(!G()||h);if(e.state.is([Me,Ke])||M(r,xn,p||""),M(Ft(r,c.focusableNodes||""),ke,p?-1:""),v&&M(r,ke,p?-1:0),P!==En(r,jt)&&(de(r,jt,P),a(P?dr:gr,w)),!P&&document.activeElement===r){var R=s.Slides.getAt(e.index);R&&gn(R.slide)}}function F(P,p,R){ie(R&&D||r,P,p)}function G(){var P=e.index;return P===t||c.cloneStatus&&P===n}function K(){if(e.is(st))return G();var P=ne(s.Elements.track),p=ne(r),R=_("left",!0),U=_("right",!0);return mt(P[R])<=rt(p[R])&&mt(p[U])<=rt(P[U])}function $(P,p){var R=Z(P-t);return!h&&(c.rewind||e.is(Ve))&&(R=Se(R,e.length-R)),R<=p}var w={index:t,slideIndex:n,slide:r,container:D,isClone:h,mount:x,destroy:O,update:y,style:F,isWithin:$};return w}function Yr(e,t,n){var r=Y(e),i=r.on,o=r.emit,a=r.bind,u=t.Elements,s=u.slides,f=u.list,c=[];function E(){d(),i(W,g),i(W,d)}function d(){s.forEach(function(y,I){v(y,I,-1)})}function g(){b(function(y){y.destroy()}),ve(c)}function l(){b(function(y){y.update()})}function v(y,I,L){var F=zr(e,I,L,y);F.mount(),c.push(F),c.sort(function(G,K){return G.index-K.index})}function _(y){return y?x(function(I){return!I.isClone}):c}function m(y){var I=t.Controller,L=I.toIndex(y),F=I.hasFocus()?1:n.perPage;return x(function(G){return _t(G.index,L,L+F-1)})}function T(y){return x(y)[0]}function h(y,I){te(y,function(L){if(_e(L)&&(L=hn(L)),fn(L)){var F=s[I];F?xt(L,F):qe(f,L),se(L,n.classes.slide),O(L,V(o,at))}}),o(W)}function D(y){Ne(x(y).map(function(I){return I.slide})),o(W)}function b(y,I){_(I).forEach(y)}function x(y){return c.filter(ln(y)?y:function(I){return _e(y)?je(I.slide,y):Mt(Xe(y),I.index)})}function S(y,I,L){b(function(F){F.style(y,I,L)})}function O(y,I){var L=Ft(y,"img"),F=L.length;F?L.forEach(function(G){a(G,"load error",function(){--F||I()})}):I()}function k(y){return y?s.length:c.length}function C(){return c.length>n.perPage}return{mount:E,destroy:g,update:l,register:v,get:_,getIn:m,getAt:T,add:h,remove:D,forEach:b,filter:x,style:S,getLength:k,isEnough:C}}function $r(e,t,n){var r=Y(e),i=r.on,o=r.bind,a=r.emit,u=t.Slides,s=t.Direction.resolve,f=t.Elements,c=f.root,E=f.track,d=f.list,g=u.getAt,l=u.style,v,_,m;function T(){h(),o(window,"resize load",Lr(V(a,at))),i([J,W],h),i(at,D)}function h(){v=n.direction===Lt,ie(c,"maxWidth",be(n.width)),ie(E,s("paddingLeft"),b(!1)),ie(E,s("paddingRight"),b(!0)),D(!0)}function D(w){var P=ne(c);(w||_.width!==P.width||_.height!==P.height)&&(ie(E,"height",x()),l(s("marginRight"),be(n.gap)),l("width",O()),l("height",k(),!0),_=P,a(zt),m!==(m=$())&&(de(c,Wn,m),a(mr,m)))}function b(w){var P=n.padding,p=s(w?"right":"left");return P&&be(P[p]||(He(P)?0:P))||"0px"}function x(){var w="";return v&&(w=S(),nt(w,"height or heightRatio is missing."),w="calc("+w+" - "+b(!1)+" - "+b(!0)+")"),w}function S(){return be(n.height||ne(d).width*n.heightRatio)}function O(){return n.autoWidth?null:be(n.fixedWidth)||(v?"":C())}function k(){return be(n.fixedHeight)||(v?n.autoHeight?null:C():S())}function C(){var w=be(n.gap);return"calc((100%"+(w&&" + "+w)+")/"+(n.perPage||1)+(w&&" - "+w)+")"}function y(){return ne(d)[s("width")]}function I(w,P){var p=g(w||0);return p?ne(p.slide)[s("width")]+(P?0:G()):0}function L(w,P){var p=g(w);if(p){var R=ne(p.slide)[s("right")],U=ne(d)[s("left")];return Z(R-U)+(P?0:G())}return 0}function F(w){return L(e.length-1)-L(0)+I(0,w)}function G(){var w=g(0);return w&&parseFloat(ie(w.slide,s("marginRight")))||0}function K(w){return parseFloat(ie(E,s("padding"+(w?"Right":"Left"))))||0}function $(){return e.is(st)||F(!0)>y()}return{mount:T,resize:D,listSize:y,slideSize:I,sliderSize:F,totalSize:L,getPadding:K,isOverflow:$}}var Kr=2;function Hr(e,t,n){var r=Y(e),i=r.on,o=t.Elements,a=t.Slides,u=t.Direction.resolve,s=[],f;function c(){i(W,E),i([J,at],g),(f=_())&&(l(f),t.Layout.resize(!0))}function E(){d(),c()}function d(){Ne(s),ve(s),r.destroy()}function g(){var m=_();f!==m&&(f<m||!m)&&r.emit(W)}function l(m){var T=a.get().slice(),h=T.length;if(h){for(;T.length<m;)ht(T,T);ht(T.slice(-m),T.slice(0,m)).forEach(function(D,b){var x=b<m,S=v(D.slide,b);x?xt(S,T[0].slide):qe(o.list,S),ht(s,S),a.register(S,b-m+(x?0:h),D.index)})}}function v(m,T){var h=m.cloneNode(!0);return se(h,n.classes.clone),h.id=e.root.id+"-clone"+Bt(T+1),h}function _(){var m=n.clones;if(!e.is(Ve))m=0;else if(We(m)){var T=n[u("fixedWidth")]&&t.Layout.slideSize(0),h=T&&rt(ne(o.track)[u("width")]/T);m=h||n[u("autoWidth")]&&e.length||n.perPage*Kr}return m}return{mount:c,destroy:d}}function Wr(e,t,n){var r=Y(e),i=r.on,o=r.emit,a=e.state.set,u=t.Layout,s=u.slideSize,f=u.getPadding,c=u.totalSize,E=u.listSize,d=u.sliderSize,g=t.Direction,l=g.resolve,v=g.orient,_=t.Elements,m=_.list,T=_.track,h;function D(){h=t.Transition,i([Oe,zt,J,W],b)}function b(){t.Controller.isBusy()||(t.Scroll.cancel(),S(e.index),t.Slides.update())}function x(p,R,U,q){p!==R&&w(p>U)&&(y(),O(C(F(),p>U),!0)),a(Me),o(pe,R,U,p),h.start(R,function(){a(we),o(it,R,U,p),q&&q()})}function S(p){O(L(p,!0))}function O(p,R){if(!e.is(st)){var U=R?p:k(p);ie(m,"transform","translate"+l("X")+"("+U+"px)"),p!==U&&o(Rn)}}function k(p){if(e.is(Ve)){var R=I(p),U=R>t.Controller.getEnd(),q=R<0;(q||U)&&(p=C(p,U))}return p}function C(p,R){var U=p-$(R),q=d();return p-=v(q*(rt(Z(U)/q)||1))*(R?1:-1),p}function y(){O(F(),!0),h.cancel()}function I(p){for(var R=t.Slides.get(),U=0,q=1/0,X=0;X<R.length;X++){var Ie=R[X].index,A=Z(L(Ie,!0)-p);if(A<=q)q=A,U=Ie;else break}return U}function L(p,R){var U=v(c(p-1)-K(p));return R?G(U):U}function F(){var p=l("left");return ne(m)[p]-ne(T)[p]+v(f(!1))}function G(p){return n.trimSpace&&e.is(Ue)&&(p=Fe(p,0,v(d(!0)-E()))),p}function K(p){var R=n.focus;return R==="center"?(E()-s(p,!0))/2:+R*s(p)||0}function $(p){return L(p?t.Controller.getEnd():0,!!n.trimSpace)}function w(p){var R=v(C(F(),p));return p?R>=0:R<=m[l("scrollWidth")]-ne(T)[l("width")]}function P(p,R){R=We(R)?F():R;var U=p!==!0&&v(R)<v($(!1)),q=p!==!1&&v(R)>v($(!0));return U||q}return{mount:D,move:x,jump:S,translate:O,shift:C,cancel:y,toIndex:I,toPosition:L,getPosition:F,getLimit:$,exceededLimit:P,reposition:b}}function Xr(e,t,n){var r=Y(e),i=r.on,o=r.emit,a=t.Move,u=a.getPosition,s=a.getLimit,f=a.toPosition,c=t.Slides,E=c.isEnough,d=c.getLength,g=n.omitEnd,l=e.is(Ve),v=e.is(Ue),_=V(F,!1),m=V(F,!0),T=n.start||0,h,D=T,b,x,S;function O(){k(),i([J,W,At],k),i(zt,C)}function k(){b=d(!0),x=n.perMove,S=n.perPage,h=w();var A=Fe(T,0,g?h:b-1);A!==T&&(T=A,a.reposition())}function C(){h!==w()&&o(At)}function y(A,B,Q){if(!Ie()){var H=L(A),j=$(H);j>-1&&(B||j!==T)&&(U(j),a.move(H,j,D,Q))}}function I(A,B,Q,H){t.Scroll.scroll(A,B,Q,function(){var j=$(a.toIndex(u()));U(g?Se(j,h):j),H&&H()})}function L(A){var B=T;if(_e(A)){var Q=A.match(/([+\-<>])(\d+)?/)||[],H=Q[1],j=Q[2];H==="+"||H==="-"?B=G(T+ +(""+H+(+j||1)),T):H===">"?B=j?P(+j):_(!0):H==="<"&&(B=m(!0))}else B=l?A:Fe(A,0,h);return B}function F(A,B){var Q=x||(X()?1:S),H=G(T+Q*(A?-1:1),T,!(x||X()));return H===-1&&v&&!mn(u(),s(!A),1)?A?0:h:B?H:$(H)}function G(A,B,Q){if(E()||X()){var H=K(A);H!==A&&(B=A,A=H,Q=!1),A<0||A>h?!x&&(_t(0,A,B,!0)||_t(h,B,A,!0))?A=P(p(A)):l?A=Q?A<0?-(b%S||S):b:A:n.rewind?A=A<0?h:0:A=-1:Q&&A!==B&&(A=P(p(B)+(A<B?-1:1)))}else A=-1;return A}function K(A){if(v&&n.trimSpace==="move"&&A!==T)for(var B=u();B===f(A,!0)&&_t(A,0,e.length-1,!n.rewind);)A<T?--A:++A;return A}function $(A){return l?(A+b)%b||0:A}function w(){for(var A=b-(X()||l&&x?1:S);g&&A-- >0;)if(f(b-1,!0)!==f(A,!0)){A++;break}return Fe(A,0,b-1)}function P(A){return Fe(X()?A:S*A,0,h)}function p(A){return X()?Se(A,h):mt((A>=h?b-1:A)/S)}function R(A){var B=a.toIndex(A);return v?Fe(B,0,h):B}function U(A){A!==T&&(D=T,T=A)}function q(A){return A?D:T}function X(){return!We(n.focus)||n.isNavigation}function Ie(){return e.state.is([Me,Ke])&&!!n.waitForTransition}return{mount:O,go:y,scroll:I,getNext:_,getPrev:m,getAdjacent:F,getEnd:w,setIndex:U,getIndex:q,toIndex:P,toPage:p,toDest:R,hasFocus:X,isBusy:Ie}}var qr="http://www.w3.org/2000/svg",jr="m15.5 0.932-4.3 4.38 14.5 14.6-14.5 14.5 4.3 4.4 14.6-14.6 4.4-4.3-4.4-4.4-14.6-14.6z",Rt=40;function Zr(e,t,n){var r=Y(e),i=r.on,o=r.bind,a=r.emit,u=n.classes,s=n.i18n,f=t.Elements,c=t.Controller,E=f.arrows,d=f.track,g=E,l=f.prev,v=f.next,_,m,T={};function h(){b(),i(J,D)}function D(){x(),h()}function b(){var I=n.arrows;I&&!(l&&v)&&k(),l&&v&&(Qe(T,{prev:l,next:v}),et(g,I?"":"none"),se(g,m=qt+"--"+n.direction),I&&(S(),y(),M([l,v],ot,d.id),a(_r,l,v)))}function x(){r.destroy(),Ee(g,m),_?(Ne(E?[l,v]:g),l=v=null):ce([l,v],Wt)}function S(){i([Oe,it,W,Ge,At],y),o(v,"click",V(O,">")),o(l,"click",V(O,"<"))}function O(I){c.go(I,!0)}function k(){g=E||xe("div",u.arrows),l=C(!0),v=C(!1),_=!0,qe(g,[l,v]),!E&&xt(g,d)}function C(I){var L='<button class="'+u.arrow+" "+(I?u.prev:u.next)+'" type="button"><svg xmlns="'+qr+'" viewBox="0 0 '+Rt+" "+Rt+'" width="'+Rt+'" height="'+Rt+'" focusable="false"><path d="'+(n.arrowPath||jr)+'" />';return hn(L)}function y(){if(l&&v){var I=e.index,L=c.getPrev(),F=c.getNext(),G=L>-1&&I<L?s.last:s.prev,K=F>-1&&I>F?s.first:s.next;l.disabled=L<0,v.disabled=F<0,M(l,re,G),M(v,re,K),a(Ar,l,v,L,F)}}return{arrows:T,mount:h,destroy:x,update:y}}var Jr=kt+"-interval";function Qr(e,t,n){var r=Y(e),i=r.on,o=r.bind,a=r.emit,u=St(n.interval,e.go.bind(e,">"),S),s=u.isPaused,f=t.Elements,c=t.Elements,E=c.root,d=c.toggle,g=n.autoplay,l,v,_=g==="pause";function m(){g&&(T(),d&&M(d,ot,f.track.id),_||h(),x())}function T(){n.pauseOnHover&&o(E,"mouseenter mouseleave",function(k){l=k.type==="mouseenter",b()}),n.pauseOnFocus&&o(E,"focusin focusout",function(k){v=k.type==="focusin",b()}),d&&o(d,"click",function(){_?h():D(!0)}),i([pe,Yt,W],u.rewind),i(pe,O)}function h(){s()&&t.Slides.isEnough()&&(u.start(!n.resetProgress),v=l=_=!1,x(),a(Ln))}function D(k){k===void 0&&(k=!0),_=!!k,x(),s()||(u.pause(),a(Nn))}function b(){_||(l||v?D(!1):h())}function x(){d&&(de(d,Re,!_),M(d,re,n.i18n[_?"play":"pause"]))}function S(k){var C=f.bar;C&&ie(C,"width",k*100+"%"),a(yr,k)}function O(k){var C=t.Slides.getAt(k);u.set(C&&+ae(C.slide,Jr)||n.interval)}return{mount:m,destroy:u.cancel,play:h,pause:D,isPaused:s}}function ei(e,t,n){var r=Y(e),i=r.on;function o(){n.cover&&(i(bn,V(u,!0)),i([Oe,J,W],V(a,!0)))}function a(s){t.Slides.forEach(function(f){var c=Ze(f.container||f.slide,"img");c&&c.src&&u(s,c,f)})}function u(s,f,c){c.style("background",s?'center/cover no-repeat url("'+f.src+'")':"",!0),et(f,s?"none":"")}return{mount:o,destroy:V(a,!1)}}var ti=10,ni=600,ri=.6,ii=1.5,ai=800;function oi(e,t,n){var r=Y(e),i=r.on,o=r.emit,a=e.state.set,u=t.Move,s=u.getPosition,f=u.getLimit,c=u.exceededLimit,E=u.translate,d=e.is(Ue),g,l,v=1;function _(){i(pe,D),i([J,W],b)}function m(S,O,k,C,y){var I=s();if(D(),k&&(!d||!c())){var L=t.Layout.sliderSize(),F=Ut(S)*L*mt(Z(S)/L)||0;S=u.toPosition(t.Controller.toDest(S%L))+F}var G=mn(I,S,1);v=1,O=G?0:O||Tt(Z(S-I)/ii,ai),l=C,g=St(O,T,V(h,I,S,y),1),a(Ke),o(Yt),g.start()}function T(){a(we),l&&l(),o(Ge)}function h(S,O,k,C){var y=s(),I=S+(O-S)*x(C),L=(I-y)*v;E(y+L),d&&!k&&c()&&(v*=ri,Z(L)<ti&&m(f(c(!0)),ni,!1,l,!0))}function D(){g&&g.cancel()}function b(){g&&!g.isPaused()&&(D(),T())}function x(S){var O=n.easingFunc;return O?O(S):1-Math.pow(1-S,4)}return{mount:_,destroy:D,scroll:m,cancel:b}}var Be={passive:!1,capture:!0};function ui(e,t,n){var r=Y(e),i=r.on,o=r.emit,a=r.bind,u=r.unbind,s=e.state,f=t.Move,c=t.Scroll,E=t.Controller,d=t.Elements.track,g=t.Media.reduce,l=t.Direction,v=l.resolve,_=l.orient,m=f.getPosition,T=f.exceededLimit,h,D,b,x,S,O=!1,k,C,y;function I(){a(d,Jt,Ct,Be),a(d,Qt,Ct,Be),a(d,qn,F,Be),a(d,"click",$,{capture:!0}),a(d,"dragstart",ge),i([Oe,J],L)}function L(){var N=n.drag;ar(!N),x=N==="free"}function F(N){if(k=!1,!C){var z=j(N);H(N.target)&&(z||!N.button)&&(E.isBusy()?ge(N,!0):(y=z?d:window,S=s.is([Me,Ke]),b=null,a(y,Jt,G,Be),a(y,Qt,K,Be),f.cancel(),c.cancel(),w(N)))}}function G(N){if(s.is(dt)||(s.set(dt),o(Er)),N.cancelable)if(S){f.translate(h+Q(X(N)));var z=Ie(N)>Xn,Ce=O!==(O=T());(z||Ce)&&w(N),k=!0,o(hr),ge(N)}else R(N)&&(S=p(N),ge(N))}function K(N){s.is(dt)&&(s.set(we),o(Tr)),S&&(P(N),ge(N)),u(y,Jt,G),u(y,Qt,K),S=!1}function $(N){!C&&k&&ge(N,!0)}function w(N){b=D,D=N,h=m()}function P(N){var z=U(N),Ce=q(z),vt=n.rewind&&n.rewindByDrag;g(!1),x?E.scroll(Ce,0,n.snap):e.is(st)?E.go(_(Ut(z))<0?vt?"<":"-":vt?">":"+"):e.is(Ue)&&O&&vt?E.go(T(!0)?">":"<"):E.go(E.toDest(Ce),!0),g(!0)}function p(N){var z=n.dragMinThreshold,Ce=He(z),vt=Ce&&z.mouse||0,Ci=(Ce?z.touch:+z)||10;return Z(X(N))>(j(N)?Ci:vt)}function R(N){return Z(X(N))>Z(X(N,!0))}function U(N){if(e.is(Ve)||!O){var z=Ie(N);if(z&&z<Xn)return X(N)/z}return 0}function q(N){return m()+Ut(N)*Se(Z(N)*(n.flickPower||600),x?1/0:t.Layout.listSize()*(n.flickMaxPages||1))}function X(N,z){return B(N,z)-B(A(N),z)}function Ie(N){return Gt(N)-Gt(A(N))}function A(N){return D===N&&b||D}function B(N,z){return(j(N)?N.changedTouches[0]:N)["page"+v(z?"Y":"X")]}function Q(N){return N/(O&&e.is(Ue)?Vr:1)}function H(N){var z=n.noDrag;return!je(N,"."+Yn+", ."+bt)&&(!z||!je(N,z))}function j(N){return typeof TouchEvent<"u"&&N instanceof TouchEvent}function Pi(){return S}function ar(N){C=N}return{mount:I,disable:ar,isDragging:Pi}}var si={Spacebar:" ",Right:It,Left:yt,Up:Pn,Down:Cn};function en(e){return e=_e(e)?e:e.key,si[e]||e}var jn="keydown";function ci(e,t,n){var r=Y(e),i=r.on,o=r.bind,a=r.unbind,u=e.root,s=t.Direction.resolve,f,c;function E(){d(),i(J,g),i(J,d),i(pe,v)}function d(){var m=n.keyboard;m&&(f=m==="global"?window:u,o(f,jn,_))}function g(){a(f,jn)}function l(m){c=m}function v(){var m=c;c=!0,sn(function(){c=m})}function _(m){if(!c){var T=en(m);T===s(yt)?e.go("<"):T===s(It)&&e.go(">")}}return{mount:E,destroy:g,disable:l}}var ct=kt+"-lazy",Pt=ct+"-srcset",li="["+ct+"], ["+Pt+"]";function fi(e,t,n){var r=Y(e),i=r.on,o=r.off,a=r.bind,u=r.emit,s=n.lazyLoad==="sequential",f=[it,Ge],c=[];function E(){n.lazyLoad&&(d(),i(W,d))}function d(){ve(c),g(),s?m():(o(f),i(f,l),l())}function g(){t.Slides.forEach(function(T){Ft(T.slide,li).forEach(function(h){var D=ae(h,ct),b=ae(h,Pt);if(D!==h.src||b!==h.srcset){var x=n.classes.spinner,S=h.parentElement,O=Ze(S,"."+x)||xe("span",x,S);c.push([h,T,O]),h.src||et(h,"none")}})})}function l(){c=c.filter(function(T){var h=n.perPage*((n.preloadPages||1)+1)-1;return T[1].isWithin(e.index,h)?v(T):!0}),c.length||o(f)}function v(T){var h=T[0];se(T[1].slide,Zt),a(h,"load error",V(_,T)),M(h,"src",ae(h,ct)),M(h,"srcset",ae(h,Pt)),ce(h,ct),ce(h,Pt)}function _(T,h){var D=T[0],b=T[1];Ee(b.slide,Zt),h.type!=="error"&&(Ne(T[2]),et(D,""),u(bn,D,b),u(at)),s&&m()}function m(){c.length&&v(c.shift())}return{mount:E,destroy:V(ve,c),check:l}}function vi(e,t,n){var r=Y(e),i=r.on,o=r.emit,a=r.bind,u=t.Slides,s=t.Elements,f=t.Controller,c=f.hasFocus,E=f.getIndex,d=f.go,g=t.Direction.resolve,l=s.pagination,v=[],_,m;function T(){h(),i([J,W,At],T);var C=n.pagination;l&&et(l,C?"":"none"),C&&(i([pe,Yt,Ge],k),D(),k(),o(Sr,{list:_,items:v},O(e.index)))}function h(){_&&(Ne(l?me(_.children):_),Ee(_,m),ve(v),_=null),r.destroy()}function D(){var C=e.length,y=n.classes,I=n.i18n,L=n.perPage,F=c()?f.getEnd()+1:rt(C/L);_=l||xe("ul",y.pagination,s.track.parentElement),se(_,m=Ot+"--"+S()),M(_,he,"tablist"),M(_,re,I.select),M(_,Ht,S()===Lt?"vertical":"");for(var G=0;G<F;G++){var K=xe("li",null,_),$=xe("button",{class:y.page,type:"button"},K),w=u.getIn(G).map(function(p){return p.slide.id}),P=!c()&&L>1?I.pageX:I.slideX;a($,"click",V(b,G)),n.paginationKeyboard&&a($,"keydown",V(x,G)),M(K,he,"presentation"),M($,he,"tab"),M($,ot,w.join(" ")),M($,re,Vt(P,G+1)),M($,ke,-1),v.push({li:K,button:$,page:G})}}function b(C){d(">"+C,!0)}function x(C,y){var I=v.length,L=en(y),F=S(),G=-1;L===g(It,!1,F)?G=++C%I:L===g(yt,!1,F)?G=(--C+I)%I:L==="Home"?G=0:L==="End"&&(G=I-1);var K=v[G];K&&(gn(K.button),d(">"+G),ge(y,!0))}function S(){return n.paginationDirection||n.direction}function O(C){return v[f.toPage(C)]}function k(){var C=O(E(!0)),y=O(E());if(C){var I=C.button;Ee(I,Re),ce(I,Mn),M(I,ke,-1)}if(y){var L=y.button;se(L,Re),M(L,Mn,!0),M(L,ke,"")}o(pr,{list:_,items:v},C,y)}return{items:v,mount:T,destroy:h,getAt:O,update:k}}var di=[" ","Enter"];function gi(e,t,n){var r=n.isNavigation,i=n.slideFocus,o=[];function a(){e.splides.forEach(function(l){l.isParent||(f(e,l.splide),f(l.splide,e))}),r&&c()}function u(){o.forEach(function(l){l.destroy()}),ve(o)}function s(){u(),a()}function f(l,v){var _=Y(l);_.on(pe,function(m,T,h){v.go(v.is(Ve)?h:m)}),o.push(_)}function c(){var l=Y(e),v=l.on;v(pn,d),v(On,g),v([Oe,J],E),o.push(l),l.emit(In,e.splides)}function E(){M(t.Elements.list,Ht,n.direction===Lt?"vertical":"")}function d(l){e.go(l.index)}function g(l,v){Mt(di,en(v))&&(d(l),ge(v))}return{setup:V(t.Media.set,{slideFocus:We(i)?r:i},!0),mount:a,destroy:u,remount:s}}function Ei(e,t,n){var r=Y(e),i=r.bind,o=0;function a(){n.wheel&&i(t.Elements.track,"wheel",u,Be)}function u(f){if(f.cancelable){var c=f.deltaY,E=c<0,d=Gt(f),g=n.wheelMinThreshold||0,l=n.wheelSleep||0;Z(c)>g&&d-o>l&&(e.go(E?"<":">"),o=d),s(E)&&ge(f)}}function s(f){return!n.releaseWheel||e.state.is(Me)||t.Controller.getAdjacent(f)!==-1}return{mount:a}}var hi=90;function Ti(e,t,n){var r=Y(e),i=r.on,o=t.Elements.track,a=n.live&&!n.isNavigation,u=xe("span",xr),s=St(hi,V(c,!1));function f(){a&&(d(!t.Autoplay.isPaused()),M(o,kn,!0),u.textContent="…",i(Ln,V(d,!0)),i(Nn,V(d,!1)),i([it,Ge],V(c,!0)))}function c(g){M(o,Gn,g),g?(qe(o,u),s.start()):(Ne(u),s.cancel())}function E(){ce(o,[Fn,kn,Gn]),Ne(u)}function d(g){a&&M(o,Fn,g?"off":"polite")}return{mount:f,disable:d,destroy:E}}var mi=Object.freeze({__proto__:null,Media:Nr,Direction:br,Elements:Br,Slides:Yr,Layout:$r,Clones:Hr,Move:Wr,Controller:Xr,Arrows:Zr,Autoplay:Qr,Cover:ei,Scroll:oi,Drag:ui,Keyboard:ci,LazyLoad:fi,Pagination:vi,Sync:gi,Wheel:Ei,Live:Ti}),_i={prev:"Previous slide",next:"Next slide",first:"Go to first slide",last:"Go to last slide",slideX:"Go to slide %s",pageX:"Go to page %s",play:"Start autoplay",pause:"Pause autoplay",carousel:"carousel",slide:"slide",select:"Select a slide to show",slideLabel:"%s of %s"},Ai={type:"slide",role:"region",speed:400,perPage:1,cloneStatus:!0,arrows:!0,pagination:!0,paginationKeyboard:!0,interval:5e3,pauseOnHover:!0,pauseOnFocus:!0,resetProgress:!0,easing:"cubic-bezier(0.25, 1, 0.5, 1)",drag:!0,direction:"ltr",trimSpace:!0,focusableNodes:"a, button, textarea, input, select, iframe",live:!0,classes:kr,i18n:_i,reducedMotion:{speed:0,rewindSpeed:0,autoplay:"pause"}};function Si(e,t,n){var r=t.Slides;function i(){Y(e).on([Oe,W],o)}function o(){r.forEach(function(u){u.style("transform","translateX(-"+100*u.index+"%)")})}function a(u,s){r.style("transition","opacity "+n.speed+"ms "+n.easing),sn(s)}return{mount:i,start:a,cancel:Ct}}function pi(e,t,n){var r=t.Move,i=t.Controller,o=t.Scroll,a=t.Elements.list,u=V(ie,a,"transition"),s;function f(){Y(e).bind(a,"transitionend",function(g){g.target===a&&s&&(E(),s())})}function c(g,l){var v=r.toPosition(g,!0),_=r.getPosition(),m=d(g);Z(v-_)>=1&&m>=1?n.useScroll?o.scroll(v,m,!1,l):(u("transform "+m+"ms "+n.easing),r.translate(v,!0),s=l):(r.jump(g),l())}function E(){u(""),o.cancel()}function d(g){var l=n.rewindSpeed;if(e.is(Ue)&&l){var v=i.getIndex(!0),_=i.getEnd();if(v===0&&g>=_||v>=_&&g===0)return l}return n.speed}return{mount:f,start:c,cancel:E}}var yi=(function(){function e(n,r){this.event=Y(),this.Components={},this.state=Ir(De),this.splides=[],this._o={},this._E={};var i=_e(n)?Tn(document,n):n;nt(i,i+" is invalid."),this.root=i,r=Ae({label:ae(i,re)||"",labelledby:ae(i,Kt)||""},Ai,e.defaults,r||{});try{Ae(r,JSON.parse(ae(i,kt)))}catch{nt(!1,"Invalid JSON")}this._o=Object.create(Ae({},r))}var t=e.prototype;return t.mount=function(r,i){var o=this,a=this.state,u=this.Components;nt(a.is([De,gt]),"Already mounted!"),a.set(De),this._C=u,this._T=i||this._T||(this.is(st)?Si:pi),this._E=r||this._E;var s=Qe({},mi,this._E,{Transition:this._T});return Le(s,function(f,c){var E=f(o,u,o._o);u[c]=E,E.setup&&E.setup()}),Le(u,function(f){f.mount&&f.mount()}),this.emit(Oe),se(this.root,Fr),a.set(we),this.emit(Sn),this},t.sync=function(r){return this.splides.push({splide:r}),r.splides.push({splide:this,isParent:!0}),this.state.is(we)&&(this._C.Sync.remount(),r.Components.Sync.remount()),this},t.go=function(r){return this._C.Controller.go(r),this},t.on=function(r,i){return this.event.on(r,i),this},t.off=function(r){return this.event.off(r),this},t.emit=function(r){var i;return(i=this.event).emit.apply(i,[r].concat(me(arguments,1))),this},t.add=function(r,i){return this._C.Slides.add(r,i),this},t.remove=function(r){return this._C.Slides.remove(r),this},t.is=function(r){return this._o.type===r},t.refresh=function(){return this.emit(W),this},t.destroy=function(r){r===void 0&&(r=!0);var i=this.event,o=this.state;return o.is(De)?Y(this).on(Sn,this.destroy.bind(this,r)):(Le(this._C,function(a){a.destroy&&a.destroy(r)},!0),i.emit(yn),i.destroy(),r&&ve(this.splides),o.set(gt)),this},ur(e,[{key:"options",get:function(){return this._o},set:function(r){this._C.Media.set(r,!0,!0)}},{key:"length",get:function(){return this._C.Slides.getLength(!0)}},{key:"index",get:function(){return this._C.Controller.getIndex()}}]),e})(),tn=yi;tn.defaults={},tn.STATES=cr;const nn=e=>{try{return document.querySelector(e)}catch{return}},Zn=e=>{try{return document.querySelectorAll(e)}catch{return document.querySelectorAll("")}},Jn=e=>{try{return document.getElementById(e)}catch{return}},Pe=(e,t={},n="")=>{try{const r=document.createElement(e);for(const[i,o]of Object.entries(t))i==="className"?r.className=String(o):i==="id"?r.id=String(o):r.setAttribute(i,String(o));return n&&(r.innerHTML=n),r}catch{return document.createElement("div")}},lt=(e,t)=>{try{e.appendChild(t)}catch{}},Qn=(e,t)=>{try{e.prepend(t)}catch{}},Ii=e=>{try{e.remove()}catch{}},er=(e,t)=>{try{for(const[n,r]of Object.entries(t))e.style.setProperty(n,String(r))}catch{}},tr={USER_AGENT_SUBSTR_START:0,USER_AGENT_SUBSTR_LENGTH:4},ft={MOBILE:{GAP:"80px",PER_PAGE:4},DESKTOP:{GAP:"10px",PER_PAGE:7},AUTOPLAY_INTERVAL:3e3},ze={MIN_SLIDES_FOR_LOOP:2,MIN_SLIDES_FOR_AUTOPLAY:2,AUTOPLAY_INTERVAL:3e3,TRANSITION_SPEED:800,GAP:"10px",SPLIDE_INIT_DELAY:100},Li={MAX_ERROR_LOG_ENTRIES:50,DOM_READY_TIMEOUT:5e3},ee={EMPTY_LENGTH:0,FIRST_INDEX:0,NOT_FOUND_INDEX:-1,NEXT_ITEM_OFFSET:1,LAST_ITEM_OFFSET:-1},nr={CHECK_INTERVAL:10,DATA_CHECK_INTERVAL:100},rr={SPLIDE_TAG_CONTAINER_ID:"splideTagContainer",SPLIDE_TAG_WRAPPER_ID:"splideTagWrapper"},ir={ID:"wusong8899-tag-tiles",TRANSLATION_PREFIX:"wusong8899-tag-tiles"},rn=()=>{try{const{userAgent:e}=navigator;return e.substring(tr.USER_AGENT_SUBSTR_START,tr.USER_AGENT_SUBSTR_LENGTH)==="Mobi"}catch{return!1}},ue={env:"production",app:{extensionId:ir.ID,translationPrefix:ir.TRANSLATION_PREFIX},tagTiles:{autoplayInterval:ft.AUTOPLAY_INTERVAL,checkInterval:nr.CHECK_INTERVAL,dataCheckInterval:nr.DATA_CHECK_INTERVAL,mobile:{gap:ft.MOBILE.GAP,perPage:ft.MOBILE.PER_PAGE},desktop:{gap:ft.DESKTOP.GAP,perPage:ft.DESKTOP.PER_PAGE},advanced:{minSlidesForLoop:ze.MIN_SLIDES_FOR_LOOP,enableAutoplay:!0,autoplayInterval:ze.AUTOPLAY_INTERVAL,enableLoopMode:!0,transitionSpeed:ze.TRANSITION_SPEED,gap:ze.GAP,pauseOnMouseEnter:!0,enableGrabCursor:!0,enableFreeMode:!1}},ui:{tagContainerId:rr.SPLIDE_TAG_CONTAINER_ID,tagWrapperId:rr.SPLIDE_TAG_WRAPPER_ID}},Ni="wusong8899-tag-tiles",bi=e=>{try{const t=fe&&fe.forum,n=t&&t.attribute;return typeof n=="function"?n.call(t,e):void 0}catch{return}},Te=(e,t)=>{const n=bi(`${Ni}.${e}`),r=1;if(typeof n<"u"&&n!==null){if(typeof t=="boolean")return n===!0||n==="1"||n===r;if(typeof t=="number"){const i=Number(n);return Number.isNaN(i)?t:i}return n}return t},Oi=()=>{const e=ue.tagTiles.advanced;return{minSlidesForLoop:Te("AdvancedSplideMinSlidesForLoop",e.minSlidesForLoop),enableAutoplay:Te("AdvancedSplideEnableAutoplay",e.enableAutoplay),autoplayInterval:Te("AdvancedSplideAutoplayInterval",e.autoplayInterval),enableLoopMode:Te("AdvancedSplideEnableLoopMode",e.enableLoopMode),transitionSpeed:Te("AdvancedSplideTransitionSpeed",e.transitionSpeed),gap:Te("AdvancedSplideGap",e.gap),pauseOnMouseEnter:Te("AdvancedSplidePauseOnMouseEnter",e.pauseOnMouseEnter),enableGrabCursor:Te("AdvancedSplideEnableGrabCursor",e.enableGrabCursor),enableFreeMode:Te("AdvancedSplideEnableFreeMode",e.enableFreeMode)}};class Ri{changeCategoryLayout(){try{if(Jn(ue.ui.tagContainerId))return;const t=Zn(".TagTile");t.length>ee.EMPTY_LENGTH?this.processTagTiles(t):this.waitForTagTilesAndProcess()}catch{}}waitForTagTilesAndProcess(){let r=0;const i=()=>{r+=ee.NEXT_ITEM_OFFSET;const o=Zn(".TagTile");o.length>ee.EMPTY_LENGTH?this.processTagTiles(o):r<10&&setTimeout(i,200)};i()}processTagTiles(t){try{const n=this.createTagSplideContainer();if(!n)return;const r=this.createTagSplide(n);if(!r)return;const i=this.createTagSplideWrapper(r);if(!i)return;this.populateTagSlides(i,t),this.appendTagContainer(n),this.addTagSplideContent(n),this.removeOriginalTagTiles(),this.setupMobileStyles(),this.initializeTagSplide(),this.notifyTagsLayoutChanged()}catch{}}createTagSplideContainer(){const t=Pe("div",{className:"splideTagContainer",id:ue.ui.tagContainerId}),n=Pe("div",{className:"TagTextOuterContainer"});return lt(t,n),t}createTagSplide(t){const n=Pe("div",{className:"splide tagSplide"});return lt(t,n),n}createTagSplideWrapper(t){const n=Pe("div",{className:"splide__list",id:ue.ui.tagWrapperId}),r=Pe("div",{className:"splide__track"});return lt(r,n),lt(t,r),n}populateTagSlides(t,n){const r=rn();for(const i of n){const o=i,a=this.extractTagData(o);if(a){const u=this.createTagSlide(a,r);lt(t,u)}}}extractTagData(t){const n=t.querySelector("a"),r=t.querySelector(".TagTile-name"),i=t.querySelector(".TagTile-description");if(!n||!r)return;const o=this.getTagBackgroundImage(n.href,t),a=globalThis.getComputedStyle(t),u=o||a.background;let s="",f="";return i&&(s=i.textContent||"",f=globalThis.getComputedStyle(i).color),{url:n.href,background:u,name:r.textContent||"",nameColor:globalThis.getComputedStyle(r).color,description:s,descColor:f}}getTagBackgroundImage(t,n){try{const i=new URL(t,globalThis.location.origin).pathname.split("/").filter(Boolean),o=i.indexOf("t"),a=i.indexOf("tags");let u="";if(o!==ee.NOT_FOUND_INDEX&&i[o+ee.NEXT_ITEM_OFFSET]?u=i[o+ee.NEXT_ITEM_OFFSET]:a!==ee.NOT_FOUND_INDEX&&i[a+ee.NEXT_ITEM_OFFSET]?u=i[a+ee.NEXT_ITEM_OFFSET]:i.length>ee.EMPTY_LENGTH&&(u=i[i.length+ee.LAST_ITEM_OFFSET]),!u)return;const s=this.getTagBackgroundUrlBySlug(u);return s?`url(${s})`:void 0}catch{const r=n.style.background;return r&&r.includes("url(")?r:void 0}}getTagBackgroundUrlBySlug(t){try{const r=fe.store.all("tags").find(o=>{const a=o;let u="";return typeof a.slug=="function"?u=a.slug():a.attribute&&typeof a.attribute=="function"&&(u=a.attribute("slug")),u===t});if(!r)return;const i=r;if(i.attribute&&typeof i.attribute=="function"){const o=i.attribute("wusong8899BackgroundURL");if(o)return o}return}catch{return}}createTagSlide(t,n){const r=Pe("div",{className:"splide__slide splide__slide-tag"});let i="splide__slide-tag-inner";n&&(i="splide__slide-tag-inner-mobile");const o=`background:${t.background};background-size: cover;background-position: center;background-repeat: no-repeat;`,a=this.hasBackgroundImage(t.background);let u="";return a||(u=`
            <div style='font-weight:bold;font-size:14px;color:${t.nameColor}'>
                ${t.name}
            </div>
        `),r.innerHTML=`
            <a href='${t.url}'>
                <div class='${i}' style='${o}'>
                    ${u}
                </div>
            </a>
        `,r}hasBackgroundImage(t){return t?t.includes("url(")&&!t.includes("url()"):!1}appendTagContainer(t){const n=nn("#content .container .TagsPage-content");n&&Qn(n,t)}addTagSplideContent(t){const n=t.querySelector(".TagTextOuterContainer");if(n){const r=Pe("div",{className:"TagTextContainer"},"<div class='TagTextIcon'></div>中文玩家社区资讯");Qn(n,r);const i=this.createSocialButtonsHTML();n.insertAdjacentHTML("beforeend",i)}}createSocialButtonsHTML(){const{extensionId:t}=ue.app,r=[{urlKey:`${t}.SocialKickUrl`,iconKey:`${t}.SocialKickIcon`,defaultIcon:""},{urlKey:`${t}.SocialFacebookUrl`,iconKey:`${t}.SocialFacebookIcon`,defaultIcon:""},{urlKey:`${t}.SocialTwitterUrl`,iconKey:`${t}.SocialTwitterIcon`,defaultIcon:""},{urlKey:`${t}.SocialYouTubeUrl`,iconKey:`${t}.SocialYouTubeIcon`,defaultIcon:""},{urlKey:`${t}.SocialInstagramUrl`,iconKey:`${t}.SocialInstagramIcon`,defaultIcon:""}].map((i,o)=>{const a=fe.forum.attribute(i.urlKey)||"",u=fe.forum.attribute(i.iconKey)||i.defaultIcon;if(!a.trim()||!u.trim())return"";let s="";return o>ee.FIRST_INDEX&&(s="margin-left: 20px;"),`<img onClick="window.open('${a}', '_blank')" style="width: 32px;${s}" src="${u}">`}).filter(i=>i!=="").join("");return r?`
            <div style="text-align:center;padding-top: 10px;">
                <button class="Button Button--primary" type="button" style="font-weight: normal !important; color:#ffa000; background: #1a1d2e !important;border-radius: 2rem !important;">
                    <div style="margin-top: 5px;" class="Button-label">
                        ${r}
                    </div>
                </button>
            </div>
        `:""}removeOriginalTagTiles(){const t=nn(".TagTiles");t&&Ii(t)}setupMobileStyles(){if(rn()){const t=Jn("app"),n=nn(".App-content");t&&er(t,{"overflow-x":"hidden"}),n&&er(n,{"min-height":"auto",background:""})}}initializeTagSplide(){const t=Oi();setTimeout(()=>{try{const n=document.querySelectorAll(".tagSplide .splide__slide"),r=n.length>=t.minSlidesForLoop,i=t.enableLoopMode&&r;let o="slide";i&&(o="loop");let a=!1;t.enableAutoplay&&n.length>=ze.MIN_SLIDES_FOR_AUTOPLAY&&(a=!0);const s=rn(),f=4;let E=7;s&&(E=f);const d=new tn(".tagSplide",{perPage:E,gap:t.gap,type:o,autoplay:a,speed:t.transitionSpeed,drag:t.enableGrabCursor,focus:ee.EMPTY_LENGTH,pagination:!1,arrows:!0});d.mount()}catch{}},ze.SPLIDE_INIT_DELAY)}notifyTagsLayoutChanged(){try{const t=new CustomEvent("tagsLayoutChanged",{detail:{extensionId:ue.app.extensionId,layoutType:"splide"}});document.dispatchEvent(t)}catch{}}}class Ye{constructor(){this.errorLog=[],this.isInitialized=!1}static getInstance(){return Ye.instance||(Ye.instance=new Ye),Ye.instance}initialize(){try{return this.isInitialized||(this.setupGlobalErrorHandling(),this.isInitialized=!0),!0}catch{return!1}}handleSync(t,n){try{return t()}catch(r){this.logError(r,n);return}}handleAsync(t,n){return t().catch(r=>{this.logError(r,n)})}logError(t,n){try{const r={timestamp:new Date,error:t,context:n};this.errorLog.push(r),this.errorLog.length>Li.MAX_ERROR_LOG_ENTRIES&&this.errorLog.shift()}catch{}}setupGlobalErrorHandling(){try{globalThis.addEventListener("unhandledrejection",t=>{this.logError(new Error(String(t.reason)),"Unhandled Promise Rejection")})}catch{}}getErrorLog(){return[...this.errorLog]}clearErrorLog(){this.errorLog=[]}}class $e{constructor(){}static getInstance(){return $e.instance||($e.instance=new $e),$e.instance}isTagsPage(){try{return fe.current.get("routeName")==="tags"}catch{try{return globalThis.location.pathname.includes("/tags")}catch{return!1}}}getConfig(){return ue}isConfigured(){try{const t=["Kick","Facebook","Twitter","YouTube","Instagram"];for(const n of t){const r=fe.forum.attribute(`${ue.app.extensionId}.Social${n}Url`),i=fe.forum.attribute(`${ue.app.extensionId}.Social${n}Icon`);if(r&&i)return!0}return!1}catch{return!1}}}fe.initializers.add(ue.app.extensionId,()=>{const e=Ye.getInstance(),t=$e.getInstance();if(!e.initialize())return;const n=new Ri;an.extend(on.prototype,"oncreate",function(i){e.handleSync(()=>{t.isTagsPage()&&setTimeout(()=>{n.changeCategoryLayout()},100)},"TagsPage oncreate extension")}),an.extend(on.prototype,"onupdate",function(i){e.handleSync(()=>{document.getElementById(ue.ui.tagContainerId)||setTimeout(()=>{n.changeCategoryLayout()},100)},"TagsPage onupdate extension")})})})(flarum.core.compat["common/extend"],flarum.core.compat["forum/app"],flarum.core.compat["tags/components/TagsPage"]);
//# sourceMappingURL=forum.js.map

module.exports={};