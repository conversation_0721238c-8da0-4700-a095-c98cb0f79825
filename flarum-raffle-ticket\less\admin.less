.GuaGuaLeImageSettingsContainer {
  height:150px;
  border-radius: 4px;
  color: #ddd;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  -webkit-user-select: none;
  -ms-user-select: none; 
  user-select: none; 
}

.GuaGuaLeSettingsLabel{
  text-align: left;
  padding: 10px 0px;
  font-size: 14px;
  font-weight: bold;
}

.GuaGuaLeColorAndBackground{
  position:relative;
  width:100%;
  display: flex;
}

.guagualeButton--gray {background-color:#ddd;color:var(--button-color);padding-left:20px;padding-right:20px;}
.guagualeButton--gray:focus {background-color:#ddd;color:var(--button-color)}
.guagualeButton--gray:hover {background-color:#c6c6c6;}
.guagualeButton--gray:active {background-color:#c6c6c6;}
.guagualeButton--gray:disabled {background-color:#eee;}