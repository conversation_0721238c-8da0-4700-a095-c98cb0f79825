{"version": 3, "file": "forum.js", "sources": ["../src/forum/model/SyncTronscan.ts", "../src/common/config/defaults.ts", "../src/forum/services/TronscanDataLoader.ts", "../src/forum/utils/DOMUtils.ts", "../src/forum/utils/MobileDetection.ts", "../src/forum/components/TronscanUIManager.ts", "../src/forum/index.ts"], "sourcesContent": ["import Model from 'flarum/common/Model';\nimport { SyncTronscanModel } from '../../common/types';\n\n/**\n * SyncTronscan model class for handling TronScan synchronization data\n */\nexport default class SyncTronscan extends Model implements SyncTronscanModel {\n  id!: () => string | number;\n  name!: () => string;\n  url!: () => string;\n  img!: () => string;\n  desc!: () => string;\n  valueUsd!: () => string | number;\n  sort!: () => number;\n  updateTime!: () => string;\n}\n\nObject.assign(SyncTronscan.prototype, {\n  id: Model.attribute('id'),\n  name: Model.attribute('name'),\n  url: Model.attribute('url'),\n  img: Model.attribute('img'),\n  desc: Model.attribute('desc'),\n  valueUsd: Model.attribute('valueUsd'),\n  sort: Model.attribute('sort'),\n  updateTime: Model.attribute('updateTime'),\n});\n", "/**\n * Default configuration for client1-sync-tronscan extension\n * Independent configuration migrated from client1-header-adv\n */\n\nexport default {\n  app: {\n    extensionId: 'wusong8899-client1-sync-tronscan',\n    name: 'Client1 Sync Tronscan',\n    version: '1.0.0',\n  },\n  api: {\n    endpoints: {\n      tronscanList: '/api/syncTronscanList',\n      tronscanAdd: '/api/syncTronscanList',\n      tronscanUpdate: '/api/syncTronscanList/{id}',\n      tronscanDelete: '/api/syncTronscanList/{id}',\n      tronscanSort: '/api/syncTronscanList/order',\n      tronscanValueUpdate: '/api/syncTronscanValueUsd',\n    },\n  },\n  data: {\n    apiResources: {\n      tronscanList: 'syncTronscanList',\n    },\n  },\n  ui: {\n    containerIds: {\n      tronscanText: 'TronscanTextContainer',\n      swiperTag: 'swiperTagContainer',\n    },\n    classes: {\n      tronscanSwiper: 'tronscanSwiper',\n      tronscanSlide: 'swiper-slide swiper-slide-tag',\n      tronscanMask: 'tronscanMask',\n      tronscanIcon: 'TronscanTextIcon',\n      tronscanContainer: 'TronscanTextContainer',\n    },\n  },\n  swiper: {\n    autoplay: {\n      delay: 3000,\n      disableOnInteraction: false,\n    },\n    speed: 800,\n    effect: 'slide',\n    grabCursor: true,\n    centeredSlides: false,\n    loop: true,\n  },\n  text: {\n    containerText: \"知名博彩公司USDT/TRC公开链钱包额度\",\n  },\n};\n", "import app from 'flarum/forum/app';\nimport defaultConfig from '../../common/config/defaults';\n\n/**\n * TronscanDataLoader - Handles loading and caching of Tronscan data\n * Migrated from client1-header-adv extension for independent operation\n */\nexport class TronscanDataLoader {\n    private static instance: TronscanDataLoader;\n\n    // Loading states\n    private tronscanListLoading = false;\n\n    // Data storage\n    private tronscanList: any[] | null = null;\n\n    private constructor() { }\n\n    /**\n     * Get singleton instance\n     */\n    static getInstance(): TronscanDataLoader {\n        if (!TronscanDataLoader.instance) {\n            TronscanDataLoader.instance = new TronscanDataLoader();\n        }\n        return TronscanDataLoader.instance;\n    }\n\n    /**\n     * Load Tronscan data\n     * @returns {Promise<any[]>} Promise resolving to tronscan data\n     */\n    async loadTronscanList(): Promise<any[]> {\n        if (this.tronscanListLoading) {\n            return this.waitForTronscanList();\n        }\n\n        if (this.tronscanList !== null) {\n            return this.tronscanList;\n        }\n\n        this.tronscanListLoading = true;\n\n        try {\n            const results = await app.store.find(defaultConfig.data.apiResources.tronscanList).catch(() => []);\n            this.tronscanList = [];\n\n            if (Array.isArray(results)) {\n                this.tronscanList.push(...results);\n            } else if (results) {\n                // Handle single item response\n                this.tronscanList.push(results);\n            }\n\n            return this.tronscanList;\n        } catch {\n            // Silently handle errors and return empty array\n            this.tronscanList = [];\n            return this.tronscanList;\n        } finally {\n            this.tronscanListLoading = false;\n        }\n    }\n\n    /**\n     * Get cached Tronscan list\n     */\n    getTronscanList(): any[] | null {\n        return this.tronscanList;\n    }\n\n    /**\n     * Clear cached data\n     */\n    clearCache(): void {\n        this.tronscanList = null;\n    }\n\n    /**\n     * Refresh Tronscan data\n     */\n    async refreshTronscanList(): Promise<any[]> {\n        this.clearCache();\n        return this.loadTronscanList();\n    }\n\n    /**\n     * Check if data is currently loading\n     */\n    isLoading(): boolean {\n        return this.tronscanListLoading;\n    }\n\n    /**\n     * Check if data is available\n     */\n    hasData(): boolean {\n        return this.tronscanList !== null && this.tronscanList.length > 0;\n    }\n\n    /**\n     * Get data count\n     */\n    getDataCount(): number {\n        return this.tronscanList ? this.tronscanList.length : 0;\n    }\n\n    /**\n     * Force reload data (bypass cache)\n     */\n    async forceReload(): Promise<any[]> {\n        this.tronscanListLoading = false; // Reset loading state\n        this.clearCache();\n        return this.loadTronscanList();\n    }\n\n    /**\n     * Helper method for waiting for data to load\n     */\n    private async waitForTronscanList(): Promise<any[]> {\n        return new Promise((resolve) => {\n            const checkInterval = setInterval(() => {\n                if (!this.tronscanListLoading && this.tronscanList !== null) {\n                    clearInterval(checkInterval);\n                    resolve(this.tronscanList);\n                }\n            }, 100);\n\n            // Add timeout to prevent infinite waiting\n            setTimeout(() => {\n                clearInterval(checkInterval);\n                resolve(this.tronscanList || []);\n            }, 10000); // 10 second timeout\n        });\n    }\n}\n", "/**\n * DOM manipulation utilities\n * Migrated from client1-header-adv extension for independent operation\n */\nexport class DOMUtils {\n    /**\n     * Create a DOM element with specified attributes\n     * @param {string} tagName - HTML tag name\n     * @param {object} attributes - Element attributes\n     * @param {string} innerHTML - Inner HTML content\n     * @returns {HTMLElement} Created element\n     */\n    static createElement(\n        tagName: string,\n        attributes: Record<string, string> = {},\n        innerHTML: string = ''\n    ): HTMLElement {\n        const element = document.createElement(tagName);\n\n        Object.entries(attributes).forEach(([key, value]) => {\n            if (key === 'className') {\n                element.className = value;\n            } else if (key === 'style') {\n                element.setAttribute('style', value);\n            } else {\n                element.setAttribute(key, value);\n            }\n        });\n\n        if (innerHTML) {\n            element.innerHTML = innerHTML;\n        }\n\n        return element;\n    }\n\n    /**\n     * Safely get element by ID\n     * @param {string} id - Element ID\n     * @returns {HTMLElement | null} Element or null if not found\n     */\n    static getElementById(id: string): HTMLElement | null {\n        return document.getElementById(id);\n    }\n\n    /**\n     * Safely query selector\n     * @param {string} selector - CSS selector\n     * @param {Element} parent - Parent element (default: document)\n     * @returns {Element | null} Element or null if not found\n     */\n    static querySelector(selector: string, parent: Element | Document = document): Element | null {\n        try {\n            if (!parent || !selector) {\n                return null;\n            }\n            return parent.querySelector(selector);\n        } catch {\n            return null;\n        }\n    }\n\n    /**\n     * Safely query all elements\n     * @param {string} selector - CSS selector\n     * @param {Element} parent - Parent element (default: document)\n     * @returns {NodeListOf<Element>} NodeList of elements\n     */\n    static querySelectorAll(selector: string, parent: Element | Document = document): NodeListOf<Element> {\n        try {\n            if (!parent || !selector) {\n                return document.querySelectorAll(''); // Return empty NodeList\n            }\n            return parent.querySelectorAll(selector);\n        } catch {\n            return document.querySelectorAll(''); // Return empty NodeList\n        }\n    }\n\n    /**\n     * Add event listener with error handling\n     * @param {Element} element - Target element\n     * @param {string} event - Event type\n     * @param {Function} handler - Event handler\n     * @param {boolean} useCapture - Use capture phase\n     */\n    static addEventListener(\n        element: Element,\n        event: string,\n        handler: EventListener,\n        useCapture: boolean = false\n    ): void {\n        try {\n            element.addEventListener(event, handler, useCapture);\n        } catch {\n            // Silently handle event listener errors\n        }\n    }\n\n    /**\n     * Remove event listener with error handling\n     * @param {Element} element - Target element\n     * @param {string} event - Event type\n     * @param {Function} handler - Event handler\n     * @param {boolean} useCapture - Use capture phase\n     */\n    static removeEventListener(\n        element: Element,\n        event: string,\n        handler: EventListener,\n        useCapture: boolean = false\n    ): void {\n        try {\n            element.removeEventListener(event, handler, useCapture);\n        } catch {\n            // Silently handle event listener removal errors\n        }\n    }\n\n    /**\n     * Set CSS styles on element\n     * @param {HTMLElement} element - Target element\n     * @param {object} styles - Style properties\n     */\n    static setStyles(element: HTMLElement, styles: Record<string, string>): void {\n        Object.entries(styles).forEach(([property, value]) => {\n            try {\n                element.style.setProperty(property, value);\n            } catch {\n                // Silently handle style setting errors\n            }\n        });\n    }\n\n    /**\n     * Append element to parent with error handling\n     * @param {Element} parent - Parent element\n     * @param {Element} child - Child element to append\n     */\n    static appendChild(parent: Element, child: Element): void {\n        try {\n            parent.appendChild(child);\n        } catch {\n            // Silently handle append errors\n        }\n    }\n\n    /**\n     * Prepend element to parent with error handling\n     * @param {Element} parent - Parent element\n     * @param {Element} child - Child element to prepend\n     */\n    static prependChild(parent: Element, child: Element): void {\n        try {\n            parent.insertBefore(child, parent.firstChild);\n        } catch {\n            // Silently handle prepend errors\n        }\n    }\n\n    /**\n     * Remove element safely\n     * @param {Element} element - Element to remove\n     */\n    static removeElement(element: Element): void {\n        try {\n            if (element && element.parentNode) {\n                element.parentNode.removeChild(element);\n            }\n        } catch {\n            // Silently handle element removal errors\n        }\n    }\n}\n", "/**\n * Mobile device detection utility\n * Migrated from client1-header-adv extension for independent operation\n */\nexport class MobileDetection {\n    private static isMobile: boolean | null = null;\n\n    /**\n     * Check if the current device is mobile\n     * @returns {boolean} True if mobile device\n     */\n    static isMobileDevice(): boolean {\n        if (this.isMobile !== null) {\n            return this.isMobile;\n        }\n\n        let check = false;\n        const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera;\n        \n        // Mobile detection regex\n        const mobileRegex = /(android|bb\\d+|meego).+mobile|avantgo|bada\\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i;\n        \n        const mobileRegex2 = /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw-(n|u)|c55\\/|capi|ccwa|cdm-|cell|chtm|cldc|cmd-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc-s|devi|dica|dmob|do(c|p)o|ds(12|-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(-|_)|g1 u|g560|gene|gf-5|g-mo|go(\\.w|od)|gr(ad|un)|haie|hcit|hd-(m|p|t)|hei-|hi(pt|ta)|hp( i|ip)|hs-c|ht(c(-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i-(20|go|ma)|i230|iac( |-|\\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\\/)|klon|kpt |kwc-|kyo(c|k)|le(no|xi)|lg( g|\\/(k|l|u)|50|54|-[a-w])|libw|lynx|m1-w|m3ga|m50\\/|ma(te|ui|xo)|mc(01|21|ca)|m-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|-([1-8]|c))|phil|pire|pl(ay|uc)|pn-2|po(ck|rt|se)|prox|psio|pt-g|qa-a|qc(07|12|21|32|60|-[2-7]|i-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h-|oo|p-)|sdk\\/|se(c(-|0|1)|47|mc|nd|ri)|sgh-|shar|sie(-|m)|sk-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h-|v-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl-|tdg-|tel(i|m)|tim-|t-mo|to(pl|sh)|ts(70|m-|m3|m5)|tx-9|up(\\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas-|your|zeto|zte-/i;\n\n        if (mobileRegex.test(userAgent) || mobileRegex2.test(userAgent.substr(0, 4))) {\n            check = true;\n        }\n\n        this.isMobile = check;\n        return check;\n    }\n\n    /**\n     * Get event type based on device\n     * @returns {string} 'touchend' for mobile, 'click' for desktop\n     */\n    static getEventType(): string {\n        return this.isMobileDevice() ? 'touchend' : 'click';\n    }\n\n    /**\n     * Get responsive configuration for Swiper\n     * @returns {object} Configuration object with mobile-specific settings\n     */\n    static getSwiperConfig() {\n        const isMobile = this.isMobileDevice();\n        return {\n            spaceBetween: isMobile ? 90 : 10,\n            slidesPerView: isMobile ? 2 : 7,\n        };\n    }\n\n    /**\n     * Get responsive configuration for tag Swiper\n     * @returns {object} Configuration object with mobile-specific settings\n     */\n    static getTagSwiperConfig() {\n        const isMobile = this.isMobileDevice();\n        return {\n            spaceBetween: isMobile ? 80 : 10,\n            slidesPerView: isMobile ? 4 : 7,\n            breakpoints: {\n                320: {\n                    slidesPerView: 2,\n                    spaceBetween: 60\n                },\n                480: {\n                    slidesPerView: 3,\n                    spaceBetween: 70\n                },\n                768: {\n                    slidesPerView: 4,\n                    spaceBetween: 80\n                },\n                1024: {\n                    slidesPerView: 6,\n                    spaceBetween: 10\n                },\n                1200: {\n                    slidesPerView: 7,\n                    spaceBetween: 10\n                }\n            }\n        };\n    }\n}\n", "import { TronscanDataLoader } from '../services/TronscanDataLoader';\nimport { DOMUtils } from '../utils/DOMUtils';\nimport { MobileDetection } from '../utils/MobileDetection';\nimport defaultConfig from '../../common/config/defaults';\n\n/**\n * TronscanUIManager - Manages Tronscan UI components and interactions\n * Migrated from client1-header-adv extension for independent operation\n */\nexport class TronscanUIManager {\n    private dataLoader: TronscanDataLoader;\n\n    constructor() {\n        this.dataLoader = TronscanDataLoader.getInstance();\n    }\n\n    /**\n     * Initialize Tronscan UI components\n     */\n    async initialize(): Promise<void> {\n        await this.addTronscan();\n    }\n\n    /**\n     * Add Tronscan component\n     */\n    private async addTronscan(): Promise<void> {\n        const tronscanList = await this.dataLoader.loadTronscanList();\n\n        if (DOMUtils.getElementById(defaultConfig.ui.containerIds.tronscanText) || tronscanList.length === 0) {\n            return;\n        }\n\n        this.createTronscanContainer();\n        const swiper = this.createTronscanSwiper();\n        const wrapper = this.createTronscanWrapper(swiper);\n\n        this.populateTronscanSlides(wrapper, tronscanList);\n        this.initializeTronscanSwiper();\n    }\n\n    /**\n     * Create Tronscan container\n     */\n    private createTronscanContainer(): HTMLElement {\n        const container = DOMUtils.createElement('div', {\n            id: defaultConfig.ui.containerIds.tronscanText,\n            className: defaultConfig.ui.classes.tronscanContainer\n        }, `<div class='${defaultConfig.ui.classes.tronscanIcon}'></div>${defaultConfig.text.containerText}`);\n\n        const swiperContainer = DOMUtils.getElementById(defaultConfig.ui.containerIds.swiperTag);\n        if (swiperContainer) {\n            DOMUtils.appendChild(swiperContainer, container);\n        }\n\n        return container;\n    }\n\n    /**\n     * Create Tronscan swiper\n     */\n    private createTronscanSwiper(): HTMLElement {\n        const swiper = DOMUtils.createElement('div', {\n            className: `swiper ${defaultConfig.ui.classes.tronscanSwiper}`\n        });\n\n        const swiperContainer = DOMUtils.getElementById(defaultConfig.ui.containerIds.swiperTag);\n        if (swiperContainer) {\n            DOMUtils.appendChild(swiperContainer, swiper);\n        }\n\n        return swiper;\n    }\n\n    /**\n     * Create Tronscan wrapper\n     */\n    private createTronscanWrapper(swiper: HTMLElement): HTMLElement {\n        const wrapper = DOMUtils.createElement('div', {\n            className: 'swiper-wrapper'\n        });\n        DOMUtils.appendChild(swiper, wrapper);\n        return wrapper;\n    }\n\n    /**\n     * Populate Tronscan slides\n     */\n    private populateTronscanSlides(wrapper: HTMLElement, tronscanList: any[]): void {\n        tronscanList.forEach(tronscanData => {\n            const slide = this.createTronscanSlide(tronscanData);\n            DOMUtils.appendChild(wrapper, slide);\n        });\n    }\n\n    /**\n     * Create individual Tronscan slide\n     */\n    private createTronscanSlide(tronscanData: any): HTMLElement {\n        const slide = DOMUtils.createElement('div', {\n            className: defaultConfig.ui.classes.tronscanSlide\n        });\n\n        const name = tronscanData.name ? tronscanData.name() : 'Unknown';\n        const valueUsd = tronscanData.valueUsd ? tronscanData.valueUsd() : '0';\n        const url = tronscanData.url ? tronscanData.url() : '#';\n        const img = tronscanData.img ? tronscanData.img() : '';\n\n        slide.innerHTML = `\n            <a href=\"${url}\" target=\"_blank\" class=\"tronscan-link\">\n                <div class=\"tronscan-content\">\n                    <img src=\"${img}\" alt=\"${name}\" class=\"tronscan-image\" />\n                    <div class=\"tronscan-info\">\n                        <div class=\"tronscan-name\">${name}</div>\n                        <div class=\"tronscan-value\">$${valueUsd}</div>\n                    </div>\n                </div>\n            </a>\n        `;\n\n        return slide;\n    }\n\n    /**\n     * Initialize Tronscan swiper\n     */\n    private initializeTronscanSwiper(): void {\n        try {\n            const config = MobileDetection.getTagSwiperConfig();\n            const swiperConfig = {\n                ...defaultConfig.swiper,\n                spaceBetween: config.spaceBetween,\n                slidesPerView: config.slidesPerView,\n                breakpoints: config.breakpoints\n            };\n\n            // @ts-ignore - Swiper is loaded globally\n            const swiperInstance = new Swiper(`.${defaultConfig.ui.classes.tronscanSwiper}`, swiperConfig);\n\n            // Store reference for potential cleanup\n            if (swiperInstance) {\n                // Swiper initialized successfully\n            }\n        } catch {\n            // Silently handle swiper initialization errors\n        }\n    }\n}\n", "import app from 'flarum/forum/app';\nimport { extend } from 'flarum/common/extend';\nimport HeaderPrimary from 'flarum/forum/components/HeaderPrimary';\nimport SyncTronscan from './model/SyncTronscan';\nimport { TronscanUIManager } from './components/TronscanUIManager';\n\n/**\n * Initialize the SyncTronscan extension for the forum frontend\n */\napp.initializers.add('wusong8899-client1-sync-tronscan', () => {\n  // Register the SyncTronscan model with the store\n  app.store.models.syncTronscanList = SyncTronscan;\n\n  // Initialize UI manager\n  const tronscanUIManager = new TronscanUIManager();\n\n  // Extend HeaderPrimary to add Tronscan UI\n  extend(HeaderPrimary.prototype, 'view', function (_vnode) {\n    // Check if we're on the tags page or appropriate page\n    if (this.isTagsPage()) {\n      // Initialize Tronscan UI components\n      setTimeout(() => {\n        tronscanUIManager.initialize().catch(() => {\n          // Silently handle initialization errors\n        });\n      }, 100);\n    }\n  });\n\n  // Add helper method to check if we're on tags page\n  HeaderPrimary.prototype.isTagsPage = function () {\n    const currentPath = window.location.pathname;\n    return currentPath.includes('/tags') || currentPath === '/';\n  };\n});\n"], "names": ["SyncTronscan", "Model", "defaultConfig", "Tronscan<PERSON><PERSON><PERSON><PERSON><PERSON>", "results", "app", "resolve", "checkInterval", "DOMUtils", "tagName", "attributes", "innerHTML", "element", "key", "value", "id", "selector", "parent", "event", "handler", "useCapture", "styles", "property", "child", "_MobileDetection", "check", "userAgent", "mobileRegex", "mobileRegex2", "isMobile", "MobileDetection", "TronscanUIManager", "tronscanList", "swiper", "wrapper", "container", "swiper<PERSON><PERSON><PERSON>", "tronscanData", "slide", "name", "valueUsd", "url", "img", "config", "swiperConfig", "swiperInstance", "tronscanUIManager", "extend", "HeaderPrimary", "_vnode", "currentPath"], "mappings": "gCAMA,MAAqBA,UAAqBC,CAAmC,CAS7E,CAEA,OAAO,OAAOD,EAAa,UAAW,CACpC,GAAIC,EAAM,UAAU,IAAI,EACxB,KAAMA,EAAM,UAAU,MAAM,EAC5B,IAAKA,EAAM,UAAU,KAAK,EAC1B,IAAKA,EAAM,UAAU,KAAK,EAC1B,KAAMA,EAAM,UAAU,MAAM,EAC5B,SAAUA,EAAM,UAAU,UAAU,EACpC,KAAMA,EAAM,UAAU,MAAM,EAC5B,WAAYA,EAAM,UAAU,YAAY,CAC1C,CAAC,ECrBD,MAAAC,EAAe,CACb,IAAK,CACH,YAAa,mCACb,KAAM,wBACN,QAAS,OAAA,EAEX,IAAK,CACH,UAAW,CACT,aAAc,wBACd,YAAa,wBACb,eAAgB,6BAChB,eAAgB,6BAChB,aAAc,8BACd,oBAAqB,2BAAA,CACvB,EAEF,KAAM,CACJ,aAAc,CACZ,aAAc,kBAAA,CAChB,EAEF,GAAI,CACF,aAAc,CACZ,aAAc,wBACd,UAAW,oBAAA,EAEb,QAAS,CACP,eAAgB,iBAChB,cAAe,gCACf,aAAc,eACd,aAAc,mBACd,kBAAmB,uBAAA,CACrB,EAEF,OAAQ,CACN,SAAU,CACR,MAAO,IACP,qBAAsB,EAAA,EAExB,MAAO,IACP,OAAQ,QACR,WAAY,GACZ,eAAgB,GAChB,KAAM,EAAA,EAER,KAAM,CACJ,cAAe,uBAAA,CAEnB,EC9CO,MAAMC,CAAmB,CASpB,aAAc,CALtB,KAAQ,oBAAsB,GAG9B,KAAQ,aAA6B,IAEb,CAKxB,OAAO,aAAkC,CACrC,OAAKA,EAAmB,WACpBA,EAAmB,SAAW,IAAIA,GAE/BA,EAAmB,QAC9B,CAMA,MAAM,kBAAmC,CACrC,GAAI,KAAK,oBACL,OAAO,KAAK,oBAAA,EAGhB,GAAI,KAAK,eAAiB,KACtB,OAAO,KAAK,aAGhB,KAAK,oBAAsB,GAE3B,GAAI,CACA,MAAMC,EAAU,MAAMC,EAAI,MAAM,KAAKH,EAAc,KAAK,aAAa,YAAY,EAAE,MAAM,IAAM,CAAA,CAAE,EACjG,YAAK,aAAe,CAAA,EAEhB,MAAM,QAAQE,CAAO,EACrB,KAAK,aAAa,KAAK,GAAGA,CAAO,EAC1BA,GAEP,KAAK,aAAa,KAAKA,CAAO,EAG3B,KAAK,YAChB,MAAQ,CAEJ,YAAK,aAAe,CAAA,EACb,KAAK,YAChB,QAAA,CACI,KAAK,oBAAsB,EAC/B,CACJ,CAKA,iBAAgC,CAC5B,OAAO,KAAK,YAChB,CAKA,YAAmB,CACf,KAAK,aAAe,IACxB,CAKA,MAAM,qBAAsC,CACxC,YAAK,WAAA,EACE,KAAK,iBAAA,CAChB,CAKA,WAAqB,CACjB,OAAO,KAAK,mBAChB,CAKA,SAAmB,CACf,OAAO,KAAK,eAAiB,MAAQ,KAAK,aAAa,OAAS,CACpE,CAKA,cAAuB,CACnB,OAAO,KAAK,aAAe,KAAK,aAAa,OAAS,CAC1D,CAKA,MAAM,aAA8B,CAChC,YAAK,oBAAsB,GAC3B,KAAK,WAAA,EACE,KAAK,iBAAA,CAChB,CAKA,MAAc,qBAAsC,CAChD,OAAO,IAAI,QAASE,GAAY,CAC5B,MAAMC,EAAgB,YAAY,IAAM,CAChC,CAAC,KAAK,qBAAuB,KAAK,eAAiB,OACnD,cAAcA,CAAa,EAC3BD,EAAQ,KAAK,YAAY,EAEjC,EAAG,GAAG,EAGN,WAAW,IAAM,CACb,cAAcC,CAAa,EAC3BD,EAAQ,KAAK,cAAgB,EAAE,CACnC,EAAG,GAAK,CACZ,CAAC,CACL,CACJ,CCnIO,MAAME,CAAS,CAQlB,OAAO,cACHC,EACAC,EAAqC,CAAA,EACrCC,EAAoB,GACT,CACX,MAAMC,EAAU,SAAS,cAAcH,CAAO,EAE9C,cAAO,QAAQC,CAAU,EAAE,QAAQ,CAAC,CAACG,EAAKC,CAAK,IAAM,CAC7CD,IAAQ,YACRD,EAAQ,UAAYE,EACbD,IAAQ,QACfD,EAAQ,aAAa,QAASE,CAAK,EAEnCF,EAAQ,aAAaC,EAAKC,CAAK,CAEvC,CAAC,EAEGH,IACAC,EAAQ,UAAYD,GAGjBC,CACX,CAOA,OAAO,eAAeG,EAAgC,CAClD,OAAO,SAAS,eAAeA,CAAE,CACrC,CAQA,OAAO,cAAcC,EAAkBC,EAA6B,SAA0B,CAC1F,GAAI,CACA,MAAI,CAACA,GAAU,CAACD,EACL,KAEJC,EAAO,cAAcD,CAAQ,CACxC,MAAQ,CACJ,OAAO,IACX,CACJ,CAQA,OAAO,iBAAiBA,EAAkBC,EAA6B,SAA+B,CAClG,GAAI,CACA,MAAI,CAACA,GAAU,CAACD,EACL,SAAS,iBAAiB,EAAE,EAEhCC,EAAO,iBAAiBD,CAAQ,CAC3C,MAAQ,CACJ,OAAO,SAAS,iBAAiB,EAAE,CACvC,CACJ,CASA,OAAO,iBACHJ,EACAM,EACAC,EACAC,EAAsB,GAClB,CACJ,GAAI,CACAR,EAAQ,iBAAiBM,EAAOC,EAASC,CAAU,CACvD,MAAQ,CAER,CACJ,CASA,OAAO,oBACHR,EACAM,EACAC,EACAC,EAAsB,GAClB,CACJ,GAAI,CACAR,EAAQ,oBAAoBM,EAAOC,EAASC,CAAU,CAC1D,MAAQ,CAER,CACJ,CAOA,OAAO,UAAUR,EAAsBS,EAAsC,CACzE,OAAO,QAAQA,CAAM,EAAE,QAAQ,CAAC,CAACC,EAAUR,CAAK,IAAM,CAClD,GAAI,CACAF,EAAQ,MAAM,YAAYU,EAAUR,CAAK,CAC7C,MAAQ,CAER,CACJ,CAAC,CACL,CAOA,OAAO,YAAYG,EAAiBM,EAAsB,CACtD,GAAI,CACAN,EAAO,YAAYM,CAAK,CAC5B,MAAQ,CAER,CACJ,CAOA,OAAO,aAAaN,EAAiBM,EAAsB,CACvD,GAAI,CACAN,EAAO,aAAaM,EAAON,EAAO,UAAU,CAChD,MAAQ,CAER,CACJ,CAMA,OAAO,cAAcL,EAAwB,CACzC,GAAI,CACIA,GAAWA,EAAQ,YACnBA,EAAQ,WAAW,YAAYA,CAAO,CAE9C,MAAQ,CAER,CACJ,CACJ,CCzKO,MAAMY,EAAN,MAAMA,CAAgB,CAOzB,OAAO,gBAA0B,CAC7B,GAAI,KAAK,WAAa,KAClB,OAAO,KAAK,SAGhB,IAAIC,EAAQ,GACZ,MAAMC,EAAY,UAAU,WAAa,UAAU,QAAW,OAAe,MAGvEC,EAAc,2TAEdC,EAAe,4hDAErB,OAAID,EAAY,KAAKD,CAAS,GAAKE,EAAa,KAAKF,EAAU,OAAO,EAAG,CAAC,CAAC,KACvED,EAAQ,IAGZ,KAAK,SAAWA,EACTA,CACX,CAMA,OAAO,cAAuB,CAC1B,OAAO,KAAK,iBAAmB,WAAa,OAChD,CAMA,OAAO,iBAAkB,CACrB,MAAMI,EAAW,KAAK,eAAA,EACtB,MAAO,CACH,aAAcA,EAAW,GAAK,GAC9B,cAAeA,EAAW,EAAI,CAAA,CAEtC,CAMA,OAAO,oBAAqB,CACxB,MAAMA,EAAW,KAAK,eAAA,EACtB,MAAO,CACH,aAAcA,EAAW,GAAK,GAC9B,cAAeA,EAAW,EAAI,EAC9B,YAAa,CACT,IAAK,CACD,cAAe,EACf,aAAc,EAAA,EAElB,IAAK,CACD,cAAe,EACf,aAAc,EAAA,EAElB,IAAK,CACD,cAAe,EACf,aAAc,EAAA,EAElB,KAAM,CACF,cAAe,EACf,aAAc,EAAA,EAElB,KAAM,CACF,cAAe,EACf,aAAc,EAAA,CAClB,CACJ,CAER,CACJ,EAhFIL,EAAe,SAA2B,KADvC,IAAMM,EAANN,ECKA,MAAMO,CAAkB,CAG3B,aAAc,CACV,KAAK,WAAa5B,EAAmB,YAAA,CACzC,CAKA,MAAM,YAA4B,CAC9B,MAAM,KAAK,YAAA,CACf,CAKA,MAAc,aAA6B,CACvC,MAAM6B,EAAe,MAAM,KAAK,WAAW,iBAAA,EAE3C,GAAIxB,EAAS,eAAeN,EAAc,GAAG,aAAa,YAAY,GAAK8B,EAAa,SAAW,EAC/F,OAGJ,KAAK,wBAAA,EACL,MAAMC,EAAS,KAAK,qBAAA,EACdC,EAAU,KAAK,sBAAsBD,CAAM,EAEjD,KAAK,uBAAuBC,EAASF,CAAY,EACjD,KAAK,yBAAA,CACT,CAKQ,yBAAuC,CAC3C,MAAMG,EAAY3B,EAAS,cAAc,MAAO,CAC5C,GAAIN,EAAc,GAAG,aAAa,aAClC,UAAWA,EAAc,GAAG,QAAQ,iBAAA,EACrC,eAAeA,EAAc,GAAG,QAAQ,YAAY,WAAWA,EAAc,KAAK,aAAa,EAAE,EAE9FkC,EAAkB5B,EAAS,eAAeN,EAAc,GAAG,aAAa,SAAS,EACvF,OAAIkC,GACA5B,EAAS,YAAY4B,EAAiBD,CAAS,EAG5CA,CACX,CAKQ,sBAAoC,CACxC,MAAMF,EAASzB,EAAS,cAAc,MAAO,CACzC,UAAW,UAAUN,EAAc,GAAG,QAAQ,cAAc,EAAA,CAC/D,EAEKkC,EAAkB5B,EAAS,eAAeN,EAAc,GAAG,aAAa,SAAS,EACvF,OAAIkC,GACA5B,EAAS,YAAY4B,EAAiBH,CAAM,EAGzCA,CACX,CAKQ,sBAAsBA,EAAkC,CAC5D,MAAMC,EAAU1B,EAAS,cAAc,MAAO,CAC1C,UAAW,gBAAA,CACd,EACD,OAAAA,EAAS,YAAYyB,EAAQC,CAAO,EAC7BA,CACX,CAKQ,uBAAuBA,EAAsBF,EAA2B,CAC5EA,EAAa,QAAQK,GAAgB,CACjC,MAAMC,EAAQ,KAAK,oBAAoBD,CAAY,EACnD7B,EAAS,YAAY0B,EAASI,CAAK,CACvC,CAAC,CACL,CAKQ,oBAAoBD,EAAgC,CACxD,MAAMC,EAAQ9B,EAAS,cAAc,MAAO,CACxC,UAAWN,EAAc,GAAG,QAAQ,aAAA,CACvC,EAEKqC,EAAOF,EAAa,KAAOA,EAAa,OAAS,UACjDG,EAAWH,EAAa,SAAWA,EAAa,WAAa,IAC7DI,EAAMJ,EAAa,IAAMA,EAAa,MAAQ,IAC9CK,EAAML,EAAa,IAAMA,EAAa,MAAQ,GAEpD,OAAAC,EAAM,UAAY;AAAA,uBACHG,CAAG;AAAA;AAAA,gCAEMC,CAAG,UAAUH,CAAI;AAAA;AAAA,qDAEIA,CAAI;AAAA,uDACFC,CAAQ;AAAA;AAAA;AAAA;AAAA,UAMhDF,CACX,CAKQ,0BAAiC,CACrC,GAAI,CACA,MAAMK,EAASb,EAAgB,mBAAA,EACzBc,EAAe,CACjB,GAAG1C,EAAc,OACjB,aAAcyC,EAAO,aACrB,cAAeA,EAAO,cACtB,YAAaA,EAAO,WAAA,EAIlBE,EAAiB,IAAI,OAAO,IAAI3C,EAAc,GAAG,QAAQ,cAAc,GAAI0C,CAAY,CAMjG,MAAQ,CAER,CACJ,CACJ,CC1IAvC,EAAI,aAAa,IAAI,mCAAoC,IAAM,CAE7DA,EAAI,MAAM,OAAO,iBAAmBL,EAGpC,MAAM8C,EAAoB,IAAIf,EAG9BgB,EAAAA,OAAOC,EAAc,UAAW,OAAQ,SAAUC,EAAQ,CAEpD,KAAK,cAEP,WAAW,IAAM,CACfH,EAAkB,aAAa,MAAM,IAAM,CAE3C,CAAC,CACH,EAAG,GAAG,CAEV,CAAC,EAGDE,EAAc,UAAU,WAAa,UAAY,CAC/C,MAAME,EAAc,OAAO,SAAS,SACpC,OAAOA,EAAY,SAAS,OAAO,GAAKA,IAAgB,GAC1D,CACF,CAAC"}