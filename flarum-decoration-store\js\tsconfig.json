{
    // Use Flarum's tsconfig as a starting point
    "extends": "flarum-tsconfig",
    // This will match all .ts, .tsx, .d.ts, .js, .jsx files in your `src` folder
    // and also tells your Typescript server to read core's global typings for
    // access to `dayjs` and `$` in the global namespace.
    "include": ["src/**/*", "../vendor/flarum/core/js/dist-typings/@types/**/*"],
    "compilerOptions": {
        // This will output typings to `dist-typings`
        "declarationDir": "./dist-typings",
        "baseUrl": ".",
        "paths": {
            "flarum/*": ["../vendor/flarum/core/js/dist-typings/*"]
        }
    }
}