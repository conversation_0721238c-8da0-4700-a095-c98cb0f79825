---
type: "agent_requested"
description: "本规则旨在指导 AI 编码代理进行 Flarum 扩展中所有与**数据持久化层**相关的开发工作。这包括定义数据库结构（迁移）和创建与数据库及 API 交互的接口（模型）。"
---

# Flarum 扩展模型与迁移开发规则

## 1. 目的与适用范围

本规则旨在指导 AI 编码代理进行 Flarum 扩展中所有与**数据持久化层**相关的开发工作。这包括定义数据库结构（迁移）和创建与数据库及 API 交互的接口（模型）。

- **主要范围**:
  - **数据库迁移**: 扩展根目录下的 `migrations/` 目录。
  - **后端模型 (PHP)**: `src/` 目录下的 Eloquent 模型文件。
  - **前端模型 (JS/TS)**: `js/src/common/models/` 或 `js/src/{forum|admin}/models/` 目录下的模型文件。
  - **模型扩展与注册**: `extend.php` (用于扩展后端模型) 和 `js/src/.../index.ts` (用于注册/扩展前端模型)。
- **关联性**: 本文档是进行 API 和 UI 开发的基础。API 的序列化器依赖于后端模型，而前端 UI 组件通过前端模型与数据存储（Store）交互。

## 2. 结构与格式

- **代码组织**:
  - 迁移文件放在 `migrations/` 目录下。
  - 后端模型放在 `src/` 下，遵循 PSR-4 自动加载。
  - 前端模型放在 `js/src/.../models/` 下。
- **原子化规则**: 每条规则都应清晰、独立且可验证。

## 3. 工具使用与工作循环

- **代理角色**: 你是一名 Flarum 扩展数据架构师，负责定义数据结构和访问逻辑。
- **环境与工具**:
  - **后端**: PHP, Composer, `php flarum` CLI。
  - **前端**: Node.js, `pnpm`。
  - **代码生成**: 可使用 `flarum-cli make backend model` 和 `flarum-cli make frontend model` 创建模板文件。
- **工作循环**: **迁移优先原则**。
  - **计划**: 1. 定义数据库变更需求 -> 2. 编写迁移文件 -> 3. 创建或修改后端模型 -> 4. 创建或修改前端模型 -> 5. 编写 API 序列化器（参考 API 文档）。
  - **执行**:
    - `php flarum migrate`: 在修改迁移文件后，或在开发环境中启用新扩展时运行。此命令可安全重复执行。
    - `php flarum migrate:reset --extension {vendor-ext-name}`: 在开发中回滚特定扩展的迁移。
  - **验证**: 检查数据库表结构、模型属性和关系是否正确。

## 4. 迁移 (Migrations)

- **命名规范**: **必须**遵循 `YYYY_MM_DD_HHMMSS_snake_case_description.php` 格式。
- **结构**: 迁移文件必须返回一个包含 `up` 和 `down` 键的数组，其值为闭包。
- **助手类**: 强烈建议使用 `Flarum\Database\Migration` 类的静态助手方法，它们会自动处理 `down` 逻辑。
  - **创建表**: `Migration::createTable('table_name', function (Blueprint $table) { ... });`
  - **添加列**: `Migration::addColumns('table_name', ['column' => ['type', 'nullable' => true]]);`
  - **删除列**: `Migration::dropColumns('table_name', ['column' => ['type', ...]]);` (需提供完整的列定义以便回滚)
  - **重命名表/列**: `Migration::renameTable(...)`, `Migration::renameColumns(...)`
- **数据迁移**:
  - **初始设置**: 使用 `Migration::addSettings([...])` 添加默认设置。
  - **初始权限**: 使用 `Migration::addPermissions(['permission.key' => Group::MODERATOR_ID])` 添加默认权限。
  - **注意**: `addSettings` 和 `addPermissions` 只应用于**首次**添加。若已存在，则会覆盖用户自定义的值。

## 5. 后端模型 (PHP)

- **基类**:
  - 创建新模型时，**必须**继承 `Flarum\Database\AbstractModel`，而非直接继承 Eloquent 的 `Model`。
- **扩展核心模型**:
  - 在 `extend.php` 中使用 `(new Flarum\Extend\Model(CoreModel::class))`。
- **属性与类型转换**:
  - 使用 `->cast('attribute_name', 'type')` (例如 `datetime`, `boolean`, `integer`)。
  - 使用 `->default('attribute_name', 'value')` 为新记录设置默认值。
- **关系**:
  - 使用 `->hasOne()`, `->belongsTo()`, `->hasMany()`, `->belongsToMany()` 方法在 `Extend\Model` 上定义关系。
  - 对于更复杂的关系 (如多态)，使用 `->relationship('relationName', function ($model) { return $model->...; });`。

## 6. 前端模型 (JS/TS)

- **数据源**: 前端模型的数据源是 **JSON:API**，其结构由后端的 `Serializer` 定义，而非直接映射数据库表。
- **基类**:
  - 所有前端模型**必须**继承 `flarum/common/Model`。
- **定义属性与关系**:
  - **属性**: `myAttribute = Model.attribute('apiAttributeName');`
  - **日期属性**: `createdAt = Model.attribute('createdAt', Model.transformDate);`
  - **关系**: `user = Model.hasOne('user');`, `posts = Model.hasMany('posts');`
- **模型注册与扩展**:
  - **新模型**: 必须在 `app.initializers` 中使用 `Store` Extender 注册到 `app.store`。
    `new Extend.Store().add('api-resource-type', MyModelClass);`
  - **扩展核心模型**: 使用 `Model` Extender 添加新属性或关系。
    `new Extend.Model(Discussion).attribute<boolean>('isSticky');`
- **数据操作**:
  - **读取**:
    - 从 API 获取: `app.store.find('discussions', 1)` 或 `app.store.find('discussions', { filter: ... })`。
    - 从缓存读取: `app.store.all('discussions')`, `app.store.getById('discussions', 1)`。
  - **创建**: `const newRecord = app.store.createRecord('discussions');`
  - **更新**: `record.save({ title: 'New Title', relationships: { tags: [...] } });`
  - **删除**: `record.delete();`

## 7. 安全与合规

- **禁止敏感数据暴露**: 在后端模型中，使用 `$hidden` 属性或在 API 序列化器中明确控制，防止敏感数据（如密码哈希、个人信息）意外暴露到前端。
- **数据一致性**: `down` 迁移必须能完全、干净地回滚 `up` 迁移的操作，以保证数据一致性。助手类 `Flarum\Database\Migration` 有助于此。

## 8. 非目标与拒绝策略

- **非目标**:
  - 直接通过 SQL 查询修改数据库（应使用 Eloquent 模型）。
  - 在前端模型中实现复杂的业务逻辑（应在后端处理）。
- **拒绝条件**:
  - 迁移文件没有对应的 `down` 方法或 `down` 逻辑不完整。
  - 后端模型包含可能泄露敏感信息的属性，且未在 API 层进行过滤。
  - 前端代码直接向 API 发送原始 `fetch` 请求来操作数据，而未使用 `app.store` 的方法。

## 9. 最小示例

### 示例 1: 创建新表和对应的后端模型

**任务**: 为 "Products" 创建一个新表，包含 `name` 和 `price`，并创建对应的 Eloquent 模型。

**验收标准**:

1.  `migrations/` 目录下有名为 `..._create_products_table.php` 的文件。
2.  运行 `php flarum migrate` 后，`products` 表在数据库中被创建。
3.  `src/Product.php` 文件被创建，并继承自 `AbstractModel`。

**代理工作流**:

1.  **计划**:
    - 创建迁移文件。
    - 在迁移文件中使用 `Migration::createTable` 定义 `products` 表。
    - 创建 `src/Product.php` 模型文件。
2.  **执行 (片段)**:

    - **`migrations/YYYY_MM_DD_...._create_products_table.php`**:

      ```php
      use Flarum\Database\Migration;
      use Illuminate\Database\Schema\Blueprint;

      return Migration::createTable('products', function (Blueprint $table) {
          $table->increments('id');
          $table->string('name');
          $table->decimal('price', 8, 2);
          $table->timestamps();
      });
      ```

    - **`src/Product.php`**:

      ```php
      <?php
      namespace MyExt;
      use Flarum\Database\AbstractModel;

      class Product extends AbstractModel {
          protected $table = 'products';
          protected $casts = [
              'price' => 'float',
              'created_at' => 'datetime',
              'updated_at' => 'datetime',
          ];
      }
      ```

3.  **验证**:
    - 运行 `php flarum migrate`。使用数据库客户端检查 `products` 表是否存在且结构正确。

### 示例 2: 为核心模型添加新关系

**任务**: 给 `Flarum\User\User` 模型添加一个 `last_ip_address` 属性，并添加一个到新模型 `AuditLog` 的 `hasMany` 关系。

**验收标准**:

1.  `users` 表新增 `last_ip_address` 列的迁移已创建并可执行。
2.  `extend.php` 中使用 `Extend\Model` 为 `User` 模型添加了 `auditLogs` 关系。

**代理工作流**:

1.  **计划**:
    - 创建迁移文件为 `users` 表添加 `last_ip_address`。
    - 修改 `extend.php`，找到 `Extend\Model` 部分，为 `User::class` 添加 `hasMany` 关系。
2.  **执行 (片段)**:

    - **`migrations/..._add_last_ip_to_users_table.php`**:

      ```php
      use Flarum\Database\Migration;

      return Migration::addColumns('users', [
          'last_ip_address' => ['string', 'length' => 45, 'nullable' => true]
      ]);
      ```

    - **`extend.php`**:

      ```php
      use Flarum\Extend;
      use Flarum\User\User;
      use MyExt\AuditLog;

      return [
          (new Extend\Model(User::class))
              ->hasMany('auditLogs', AuditLog::class, 'user_id')
      ];
      ```

3.  **验证**:
    - 运行 `php flarum migrate`。在 `tinker` 或代码中，获取一个 User 实例并尝试访问 `$user->auditLogs` 关系，确认其工作正常。
