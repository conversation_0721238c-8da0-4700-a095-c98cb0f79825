.checkinLevelBarOuter {
    display: inline-block;
    color: #fff;
    border-radius: var(--border-radius);
    background: lightgray;
    border: 1px solid var(--border-radius);
    min-width: 100px;
}

.checkinLevelBarInner {
  display: inline-block;
  width: 40%;
  background: darkblue;
  border-radius: var(--border-radius);
}

/* Tronscan UI Styles - Migrated from client1-header-adv */
.TronscanTextIcon{
  background-image:url(data:image/png;base64,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);
  content: "";
  display: inline-block;
  vertical-align: -3px;
  width: 18px;
  height: 18px;
  line-height: 26px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: top;
  margin-right: 6px;
}

.TronscanTextContainer{
  font-size: 14px;
  padding: 20px 0px;
  font-weight: bold;
}

.tronscanSwiper{
  /* Tronscan specific swiper styles */
}

.tronscanMask{
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--shadow-color);
  border-radius: 12px;
  padding: 10px;
  position: relative;
  width: 100px;
  height: 130px;
}

/* Tronscan slide styles */
.tronscan-link {
  display: block;
  text-decoration: none;
  color: inherit;
  width: 100%;
  height: 100%;
}

.tronscan-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 10px;
  background: var(--shadow-color);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.tronscan-content:hover {
  background: var(--primary-color);
  color: white;
}

.tronscan-image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-bottom: 8px;
  object-fit: cover;
}

.tronscan-info {
  text-align: center;
}

.tronscan-name {
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 4px;
  word-break: break-word;
}

.tronscan-value {
  font-size: 11px;
  color: var(--muted-color);
  font-weight: normal;
}

/* Responsive styles for Tronscan */
@media (max-width: 768px) {
  .TronscanTextContainer {
    padding: 15px 0px;
    font-size: 13px;
  }

  .tronscan-content {
    padding: 8px;
  }

  .tronscan-image {
    width: 32px;
    height: 32px;
  }

  .tronscan-name {
    font-size: 11px;
  }

  .tronscan-value {
    font-size: 10px;
  }
}