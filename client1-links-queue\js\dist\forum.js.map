{"version": 3, "file": "forum.js", "sources": ["../src/forum/model/LinksQueue.js", "../src/common/config/defaults.ts", "../src/forum/services/DataLoader.ts", "../src/forum/index.js"], "sourcesContent": ["import Model from \"flarum/Model\";\n\nexport default class LinksQueue extends Model {}\nObject.assign(LinksQueue.prototype, {\n  id: Model.attribute(\"id\"),\n  name: Model.attribute(\"name\"),\n  links: Model.attribute(\"links\"),\n  sort: Model.attribute(\"sort\"),\n});\n", "import type { RootConfig } from './types';\n\nexport const defaultConfig: RootConfig = {\n  env: (process.env.NODE_ENV as any) || 'production',\n  app: {\n    extensionId: 'wusong8899-client1-links-queue',\n    translationPrefix: 'wusong8899-links-queue',\n  },\n  data: {\n    apiResources: {\n      linksQueueList: 'linksQueueList',\n    },\n  },\n};\n", "import app from 'flarum/forum/app';\nimport { defaultConfig } from '../../common/config';\n\n/**\n * Data loading service for links queue data sources\n */\nexport class DataLoader {\n    private static instance: DataLoader;\n\n    // Loading states\n    private linksQueueListLoading = false;\n\n    // Data storage\n    private linksQueueList: any[] | null = null;\n    private linksQueuePointer = 0;\n\n    private constructor() { }\n\n    /**\n     * Get singleton instance\n     */\n    static getInstance(): DataLoader {\n        if (!DataLoader.instance) {\n            DataLoader.instance = new DataLoader();\n        }\n        return DataLoader.instance;\n    }\n\n    /**\n     * Load links queue data\n     * @returns {Promise<any[]>} Promise resolving to links queue data\n     */\n    async loadLinksQueueList(): Promise<any[]> {\n        if (this.linksQueueListLoading) {\n            return this.waitForLinksQueueList();\n        }\n\n        if (this.linksQueueList !== null) {\n            return this.linksQueueList;\n        }\n\n        this.linksQueueListLoading = true;\n\n        try {\n            const results = await app.store.find(defaultConfig.data.apiResources.linksQueueList).catch(() => []);\n            this.linksQueueList = [];\n            if (Array.isArray(results)) {\n                this.linksQueueList.push(...results);\n            }\n            return this.linksQueueList;\n        } catch {\n            this.linksQueueList = [];\n            return this.linksQueueList;\n        } finally {\n            this.linksQueueListLoading = false;\n        }\n    }\n\n    /**\n     * Load all data sources\n     * @returns {Promise<{links: any[]}>} Promise resolving to all data\n     */\n    async loadAllData(): Promise<{ links: any[] }> {\n        const links = await this.loadLinksQueueList();\n        return { links };\n    }\n\n    getLinksQueueList(): any[] | null {\n        return this.linksQueueList;\n    }\n\n    getLinksQueuePointer(): number {\n        return this.linksQueuePointer;\n    }\n\n    setLinksQueuePointer(pointer: number): void {\n        this.linksQueuePointer = Math.max(0, pointer);\n    }\n\n    // Helper methods for waiting\n\n    private async waitForLinksQueueList(): Promise<any[]> {\n        return new Promise((resolve) => {\n            const checkInterval = setInterval(() => {\n                if (!this.linksQueueListLoading && this.linksQueueList !== null) {\n                    clearInterval(checkInterval);\n                    resolve(this.linksQueueList);\n                }\n            }, 100);\n        });\n    }\n}\n", "import app from 'flarum/forum/app';\r\nimport LinksQueue from \"./model/LinksQueue\";\r\nimport { DataLoader } from './services/DataLoader';\r\n\r\napp.initializers.add('wusong8899-client1-links-queue', () => {\r\n  app.store.models.linksQueueList = LinksQueue;\r\n\r\n  // Initialize DataLoader service\r\n  const dataLoader = DataLoader.getInstance();\r\n\r\n  // Make DataLoader available globally for other extensions\r\n  if (typeof window !== 'undefined') {\r\n    window.LinksQueueDataLoader = dataLoader;\r\n  }\r\n});"], "names": ["LinksQueue", "Model", "defaultConfig", "DataLoader", "results", "app", "pointer", "resolve", "checkInterval", "dataLoader"], "mappings": "4BAEe,MAAMA,UAAmBC,CAAM,CAAA,CAC9C,OAAO,OAAOD,EAAW,UAAW,CAClC,GAAIC,EAAM,UAAU,IAAI,EACxB,KAAMA,EAAM,UAAU,MAAM,EAC5B,MAAOA,EAAM,UAAU,OAAO,EAC9B,KAAMA,EAAM,UAAU,MAAM,CAC9B,CAAC,ECNM,MAAMC,EAA4B,CACvC,IAAM,aACN,IAAK,CACH,YAAa,iCACb,kBAAmB,wBAAA,EAErB,KAAM,CACJ,aAAc,CACZ,eAAgB,gBAAA,CAClB,CAEJ,ECPO,MAAMC,CAAW,CAUZ,aAAc,CANtB,KAAQ,sBAAwB,GAGhC,KAAQ,eAA+B,KACvC,KAAQ,kBAAoB,CAEJ,CAKxB,OAAO,aAA0B,CAC7B,OAAKA,EAAW,WACZA,EAAW,SAAW,IAAIA,GAEvBA,EAAW,QACtB,CAMA,MAAM,oBAAqC,CACvC,GAAI,KAAK,sBACL,OAAO,KAAK,sBAAA,EAGhB,GAAI,KAAK,iBAAmB,KACxB,OAAO,KAAK,eAGhB,KAAK,sBAAwB,GAE7B,GAAI,CACA,MAAMC,EAAU,MAAMC,EAAI,MAAM,KAAKH,EAAc,KAAK,aAAa,cAAc,EAAE,MAAM,IAAM,CAAA,CAAE,EACnG,YAAK,eAAiB,CAAA,EAClB,MAAM,QAAQE,CAAO,GACrB,KAAK,eAAe,KAAK,GAAGA,CAAO,EAEhC,KAAK,cAChB,MAAQ,CACJ,YAAK,eAAiB,CAAA,EACf,KAAK,cAChB,QAAA,CACI,KAAK,sBAAwB,EACjC,CACJ,CAMA,MAAM,aAAyC,CAE3C,MAAO,CAAE,MADK,MAAM,KAAK,mBAAA,CAChB,CACb,CAEA,mBAAkC,CAC9B,OAAO,KAAK,cAChB,CAEA,sBAA+B,CAC3B,OAAO,KAAK,iBAChB,CAEA,qBAAqBE,EAAuB,CACxC,KAAK,kBAAoB,KAAK,IAAI,EAAGA,CAAO,CAChD,CAIA,MAAc,uBAAwC,CAClD,OAAO,IAAI,QAASC,GAAY,CAC5B,MAAMC,EAAgB,YAAY,IAAM,CAChC,CAAC,KAAK,uBAAyB,KAAK,iBAAmB,OACvD,cAAcA,CAAa,EAC3BD,EAAQ,KAAK,cAAc,EAEnC,EAAG,GAAG,CACV,CAAC,CACL,CACJ,CCvFAF,EAAI,aAAa,IAAI,iCAAkC,IAAM,CAC3DA,EAAI,MAAM,OAAO,eAAiBL,EAGlC,MAAMS,EAAaN,EAAW,cAG1B,OAAO,OAAW,MACpB,OAAO,qBAAuBM,EAElC,CAAC"}