---
type: "agent_requested"
description: "Flarum 前端页面与路由解析器 (Frontend Pages and Resolvers)"
---

### **主题：Flarum 前端页面与路由解析器 (Frontend Pages and Resolvers)**

#### **一、核心概念**

Flarum 前端利用 Mithril 的路由系统，将不同的 URL 路径映射到不同的前端组件上进行展示。为了保持一致性和利用框架提供的便利功能，推荐所有页面组件都继承自 Flarum 提供的 `Page` 基类。

---

#### **二、`Page` 组件 (`flarum/common/components/Page`)**

这是一个应用于 `admin` 和 `forum` 两个前端环境的通用页面基类。

##### **1. 继承 `Page` 组件的优势**

- **自动状态管理**：在路由切换时，自动更新 `app.current` 和 `app.previous` 这两个 `PageState` 对象。
- **自动 UI 清理**：切换路由时，会自动关闭当前打开的模态框（Modal）和抽屉（Drawer）。
- **动态 CSS 类**：如果组件内定义了 `this.bodyClass` 属性，其值会被自动应用到 `#app` HTML 元素上。
- **滚动行为控制**：
  - `scrollTopOnCreate`: 在 `oninit` 中设置为 `false` 可阻止页面切换时自动滚动到顶部。
  - `useBrowserScrollRestoration`: 在 `oninit` 中设置为 `false` 可禁用浏览器自身的滚动位置恢复功能。
- **代码一致性**：为所有页面使用一个共同的基类，有助于提升代码的规范性和可维护性。

##### **2. `Page` 组件使用示例**

```javascript
import Page from "flarum/common/components/Page";

export default class CustomPage extends Page {
  view() {
    return <p>Hello!</p>;
  }
}
```

---

#### **三、`Page` 组件的高级功能**

##### **1. 将自定义页面设为首页**

要让自定义页面能被管理员在后台设置为论坛首页，需要扩展 `BasicsPage` 的 `homePageItems` 方法。

- **方法**: `extend(BasicsPage.prototype, 'homePageItems', items => { ... });`
- **操作**: 在回调函数中，使用 `items.add()` 方法添加你的页面选项。

- **示例 (来自 Flarum Tags 扩展)**:

  ```javascript
  import { extend } from "flarum/common/extend";
  import BasicsPage from "flarum/common/components/BasicsPage";

  export default function () {
    extend(BasicsPage.prototype, "homePageItems", (items) => {
      items.add("tags", {
        path: "/tags",
        label: app.translator.trans("flarum-tags.admin.basics.tags_label"),
      });
    });
  }
  ```

##### **2. 设置页面标题**

通过 `app.setTitle()` 和 `app.setTitleCount()` 方法可以自定义浏览器标签页的标题。

- **调用时机**: 在页面的 `oncreate` 生命周期钩子中，或者在从 API 异步加载数据完成后调用。
- **注意**: 如果页面被设为首页，`app.setTitle()` 会清空标题以简化显示，但仍应调用它以防止沿用前一个页面的标题。

- **示例**:

  ```javascript
  // 示例1: 静态标题
  import Page from 'flarum/common/components/Page';

  export default class CustomPage extends Page {
    oncreate(vnode) {
      super.oncreate(vnode);

      app.setTitle("Cool Page");
      app.setTitleCount(0); // 清除未读计数
    }

    view() { /* ... */ }
  }

  // 示例2: 动态标题 (数据加载后)
  import Page from 'flarum/common/components/Page';

  export default class CustomPageLoadsData extends Page {
    oninit(vnode) {
      super.oninit(vnode);

      app.store.find("users", 1).then(user => {
        app.setTitle(user.displayName());
        app.setTitleCount(0);
      });
    }

    view() { /* ... */ }
  }
  ```

---

#### **四、页面状态管理 (`PageState`)**

Flarum 通过 `app.current` 和 `app.previous` 两个 `PageState` 实例来存储当前和上一个页面的信息。

- **存储内容**:

  1.  页面的组件类。
  2.  与页面相关的数据集合 (默认包含当前路由名称 `routeName`)。

- **API**:

  - `app.current.set(KEY, DATA)`: 存储数据。
  - `app.current.get(KEY)`: 读取数据。
  - `app.current.matches(ComponentClass, [dataObject])`: 检查当前页面是否为指定的组件类型，并可选择性地匹配数据。

- **使用示例**:

  ```javascript
  import DiscussionPage from "flarum/forum/components/DiscussionPage";
  import IndexPage from "flarum/forum/components/IndexPage";

  // 仅检查页面类型
  const isDiscussionPage = app.current.matches(DiscussionPage);

  // 检查页面类型和特定数据
  const isFollowingPage = app.current.matches(IndexPage, {
    routeName: "following",
  });
  ```

---

#### **五、路由解析器 (Route Resolvers) - 高级**

路由解析器是 Mithril 的一项高级功能，用于在路由匹配时进行更复杂的组件解析逻辑。

##### **1. Flarum 的默认解析器**

Flarum 使用 `flarum/common/resolvers/DefaultResolver` 作为所有路由的默认解析器。

- **功能**:
  1.  向页面组件传递 `routeName` 属性。
  2.  为顶级页面组件分配一个唯一的 `key`。当路由切换但组件类型不变时，不同的 `key` 会强制 Mithril 完整地重新渲染组件，确保页面状态刷新。

##### **2. 使用自定义路由解析器**

在注册路由时，有三种方式指定组件/解析器：

1.  **`resolver`**: 提供一个**解析器实例**。需要手动处理组件返回和路由名称。
2.  **`resolverClass` + `component`**: 提供一个**解析器类**和一个**组件**。Flarum 会用该类（而不是默认类）来实例化一个解析器。该类的构造函数应接受 `(component, routeName)` 两个参数。
3.  **`component`**: 只提供一个组件，使用 Flarum 的默认解析器行为。

- **示例**:

  ```javascript
  import CustomPage from "./components/CustomPage";
  import CustomPageResolver from "./resolvers/CustomPageResolver";

  // 方式1: 使用解析器实例
  app.routes["resolverInstance"] = {
    path: "/custom/path/1",
    resolver: {
      onmatch: function (args) {
        if (!app.session.user) return m.route.SKIP; // 跳转或跳过
        return CustomPage;
      },
    },
  };

  // 方式2: 使用自定义解析器类
  app.routes["resolverClass"] = {
    path: "/custom/path/2",
    resolverClass: CustomPageResolver,
    component: CustomPage,
  };

  // 方式3: 默认行为
  app.routes["default"] = {
    path: "/custom/path/3",
    component: CustomPage,
  };
  ```

##### **3. 创建自定义解析器**

强烈建议自定义解析器继承自 `flarum/common/resolvers/DefaultResolver`。

- **应用场景**: 处理复杂的路由逻辑，如 Flarum 核心中的 `DiscussionPageResolver`，它能识别同一主题下的不同帖子链接，并触发页面内滚动而不是整个页面重新加载。
