import app from 'flarum/forum/app';
import { defaultConfig } from '../../common/config';

const EXTENSION_ID = 'wusong8899-header-advertisement';

/**
 * Safely read a forum attribute if available
 */
const getForumAttribute = (key: string): unknown => {
    try {
        const forum = app && app.forum;
        const attrFn = forum && forum.attribute;
        if (typeof attrFn === 'function') {
            return attrFn.call(forum, key);
        }
        return;
    } catch {
        return;
    }
};

/**
 * Get a setting value with fallback to default
 */
const getSetting = <TValue>(settingKey: string, defaultValue: TValue): TValue => {
    const value = getForumAttribute(`${EXTENSION_ID}.${settingKey}`);
    const BOOLEAN_TRUE_VALUE = 1;

    if (typeof value !== 'undefined' && value !== null) {
        // Handle boolean conversion
        if (typeof defaultValue === 'boolean') {
            return (value === true || value === '1' || value === BOOLEAN_TRUE_VALUE) as TValue;
        }
        // Handle number conversion
        if (typeof defaultValue === 'number') {
            const numValue = Number(value);
            if (Number.isNaN(numValue)) {
                return defaultValue;
            }
            return numValue as TValue;
        }
        // Return as-is for other types
        return value as TValue;
    }
    return defaultValue;
};

/**
 * Get transition time setting
 */
export const getTransitionTime = (): number =>
    getSetting('TransitionTime', defaultConfig.slider.defaultTransitionTime);

/**
 * Get header icon URL setting
 */
export const getHeaderIconUrl = (): string =>
    getSetting('HeaderIconUrl', defaultConfig.ui.headerIconUrl);
