(function(Xe,it,fe,Pt,Ye,gt){"use strict";class he extends fe{static isDismissible=!1;oninit(t){super.oninit(t),this.LinksQueueItemData=this.attrs.LinksQueueItemData,this.settingType="add",this.LinksQueueItemData?(this.settingType="edit",this.itemName=Pt(this.LinksQueueItemData.name()),this.itemUrl=Pt(this.LinksQueueItemData.links())):(this.itemName=Pt(""),this.itemUrl=Pt(""))}className(){return"Modal--Medium"}title(){return this.settingType==="add"?app.translator.trans("wusong8899-links-queue.admin.settings.item-add"):app.translator.trans("wusong8899-links-queue.admin.settings.item-edit")}content(){return m("div",{className:"Modal-body"},m("div",{className:"Form"},m("div",{className:"Form-group",style:"text-align: center;"},m("div",null,m("div",{class:"GuaGuaLeSettingsLabel"},app.translator.trans("wusong8899-links-queue.admin.settings.item-name")),m("input",{maxlength:"255",required:!0,className:"FormControl",bidi:this.itemName}),m("div",{class:"GuaGuaLeSettingsLabel"},app.translator.trans("wusong8899-links-queue.admin.settings.item-url")),m("input",{maxlength:"500",required:!0,className:"FormControl",bidi:this.itemUrl}))),m("div",{className:"Form-group",style:"text-align: center;"},it.component({className:"Button Button--primary",type:"submit",loading:this.loading},this.settingType==="add"?app.translator.trans("wusong8899-links-queue.admin.data-add"):app.translator.trans("wusong8899-links-queue.admin.data-save"))," ",it.component({className:"Button guagualeButton--gray",loading:this.loading,onclick:()=>{this.hide()}},app.translator.trans("wusong8899-links-queue.admin.cancel")))))}onsubmit(t){t.preventDefault(),this.loading=!0,this.settingType==="edit"?this.LinksQueueItemData.save({name:this.itemName(),url:this.itemUrl()}).then(()=>this.hide(),e=>{this.loading=!1,this.handleErrors(e)}):app.store.createRecord("linksQueueList").save({name:this.itemName(),url:this.itemUrl()}).then(()=>{location.reload()}).catch(()=>{this.loading=!1,this.handleErrors()})}}class qe extends fe{static isDismissible=!1;oninit(t){super.oninit(t),this.LinksQueueItemData=this.attrs.LinksQueueItemData,this.loading=!1}className(){return"Modal--small"}title(){return app.translator.trans("wusong8899-links-queue.admin.settings.item-delete-confirmation")}content(){return m("div",{className:"Modal-body"},m("div",{className:"Form-group",style:"text-align: center;"},it.component({className:"Button Button--primary",type:"submit",loading:this.loading},app.translator.trans("wusong8899-links-queue.admin.confirm"))," ",it.component({className:"Button guagualeButton--gray",loading:this.loading,onclick:()=>{this.hide()}},app.translator.trans("wusong8899-links-queue.admin.cancel"))))}onsubmit(t){t.preventDefault(),this.loading=!0,this.LinksQueueItemData.delete().then(()=>{location.reload()})}}class Ge extends Ye{view(){const{LinksQueueItemData:t}=this.attrs,e=t.id(),n=t.name(),o=t.links();return m("div",{style:"border: 1px dotted var(--control-color);padding: 10px;border-radius: 4px;"},m("div",null,m("div",{style:"padding-top: 5px;"},m(it,{className:"Button Button--primary",onclick:()=>this.editItem(t)},app.translator.trans("wusong8899-links-queue.admin.settings.item-edit"))," ",m(it,{style:"font-weight:bold;width:66px;",className:"Button Button--danger",onclick:()=>this.deleteItem(t)},app.translator.trans("wusong8899-links-queue.admin.settings.item-delete")),"  ",m("b",null,app.translator.trans("wusong8899-links-queue.admin.settings.item-id"),": "),e," | ",m("b",null,app.translator.trans("wusong8899-links-queue.admin.settings.item-name"),": "),n," | ",m("b",null,app.translator.trans("wusong8899-links-queue.admin.settings.item-url"),": "),o," ")))}editItem(t){app.modal.show(he,{LinksQueueItemData:t})}deleteItem(t){app.modal.show(qe,{LinksQueueItemData:t})}}/**!
 * Sortable 1.15.6
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function pe(i,t){var e=Object.keys(i);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(i);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(i,o).enumerable})),e.push.apply(e,n)}return e}function Q(i){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?pe(Object(e),!0).forEach(function(n){Qe(i,n,e[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(e)):pe(Object(e)).forEach(function(n){Object.defineProperty(i,n,Object.getOwnPropertyDescriptor(e,n))})}return i}function At(i){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?At=function(t){return typeof t}:At=function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},At(i)}function Qe(i,t,e){return t in i?Object.defineProperty(i,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):i[t]=e,i}function z(){return z=Object.assign||function(i){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(i[n]=e[n])}return i},z.apply(this,arguments)}function He(i,t){if(i==null)return{};var e={},n=Object.keys(i),o,r;for(r=0;r<n.length;r++)o=n[r],!(t.indexOf(o)>=0)&&(e[o]=i[o]);return e}function We(i,t){if(i==null)return{};var e=He(i,t),n,o;if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(i);for(o=0;o<r.length;o++)n=r[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(i,n)&&(e[n]=i[n])}return e}var ze="1.15.6";function j(i){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(i)}var U=j(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),vt=j(/Edge/i),me=j(/firefox/i),bt=j(/safari/i)&&!j(/chrome/i)&&!j(/android/i),$t=j(/iP(ad|od|hone)/i),ge=j(/chrome/i)&&j(/android/i),ve={capture:!1,passive:!1};function b(i,t,e){i.addEventListener(t,e,!U&&ve)}function v(i,t,e){i.removeEventListener(t,e,!U&&ve)}function Mt(i,t){if(t){if(t[0]===">"&&(t=t.substring(1)),i)try{if(i.matches)return i.matches(t);if(i.msMatchesSelector)return i.msMatchesSelector(t);if(i.webkitMatchesSelector)return i.webkitMatchesSelector(t)}catch{return!1}return!1}}function be(i){return i.host&&i!==document&&i.host.nodeType?i.host:i.parentNode}function X(i,t,e,n){if(i){e=e||document;do{if(t!=null&&(t[0]===">"?i.parentNode===e&&Mt(i,t):Mt(i,t))||n&&i===e)return i;if(i===e)break}while(i=be(i))}return null}var ye=/\s+/g;function M(i,t,e){if(i&&t)if(i.classList)i.classList[e?"add":"remove"](t);else{var n=(" "+i.className+" ").replace(ye," ").replace(" "+t+" "," ");i.className=(n+(e?" "+t:"")).replace(ye," ")}}function h(i,t,e){var n=i&&i.style;if(n){if(e===void 0)return document.defaultView&&document.defaultView.getComputedStyle?e=document.defaultView.getComputedStyle(i,""):i.currentStyle&&(e=i.currentStyle),t===void 0?e:e[t];!(t in n)&&t.indexOf("webkit")===-1&&(t="-webkit-"+t),n[t]=e+(typeof e=="string"?"":"px")}}function dt(i,t){var e="";if(typeof i=="string")e=i;else do{var n=h(i,"transform");n&&n!=="none"&&(e=n+" "+e)}while(!t&&(i=i.parentNode));var o=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return o&&new o(e)}function we(i,t,e){if(i){var n=i.getElementsByTagName(t),o=0,r=n.length;if(e)for(;o<r;o++)e(n[o],o);return n}return[]}function H(){var i=document.scrollingElement;return i||document.documentElement}function I(i,t,e,n,o){if(!(!i.getBoundingClientRect&&i!==window)){var r,a,l,s,u,f,c;if(i!==window&&i.parentNode&&i!==H()?(r=i.getBoundingClientRect(),a=r.top,l=r.left,s=r.bottom,u=r.right,f=r.height,c=r.width):(a=0,l=0,s=window.innerHeight,u=window.innerWidth,f=window.innerHeight,c=window.innerWidth),(t||e)&&i!==window&&(o=o||i.parentNode,!U))do if(o&&o.getBoundingClientRect&&(h(o,"transform")!=="none"||e&&h(o,"position")!=="static")){var y=o.getBoundingClientRect();a-=y.top+parseInt(h(o,"border-top-width")),l-=y.left+parseInt(h(o,"border-left-width")),s=a+r.height,u=l+r.width;break}while(o=o.parentNode);if(n&&i!==window){var D=dt(o||i),w=D&&D.a,E=D&&D.d;D&&(a/=E,l/=w,c/=w,f/=E,s=a+f,u=l+c)}return{top:a,left:l,bottom:s,right:u,width:c,height:f}}}function Ee(i,t,e){for(var n=Z(i,!0),o=I(i)[t];n;){var r=I(n)[e],a=void 0;if(a=o>=r,!a)return n;if(n===H())break;n=Z(n,!1)}return!1}function ct(i,t,e,n){for(var o=0,r=0,a=i.children;r<a.length;){if(a[r].style.display!=="none"&&a[r]!==p.ghost&&(n||a[r]!==p.dragged)&&X(a[r],e.draggable,i,!1)){if(o===t)return a[r];o++}r++}return null}function Vt(i,t){for(var e=i.lastElementChild;e&&(e===p.ghost||h(e,"display")==="none"||t&&!Mt(e,t));)e=e.previousElementSibling;return e||null}function B(i,t){var e=0;if(!i||!i.parentNode)return-1;for(;i=i.previousElementSibling;)i.nodeName.toUpperCase()!=="TEMPLATE"&&i!==p.clone&&(!t||Mt(i,t))&&e++;return e}function De(i){var t=0,e=0,n=H();if(i)do{var o=dt(i),r=o.a,a=o.d;t+=i.scrollLeft*r,e+=i.scrollTop*a}while(i!==n&&(i=i.parentNode));return[t,e]}function je(i,t){for(var e in i)if(i.hasOwnProperty(e)){for(var n in t)if(t.hasOwnProperty(n)&&t[n]===i[e][n])return Number(e)}return-1}function Z(i,t){if(!i||!i.getBoundingClientRect)return H();var e=i,n=!1;do if(e.clientWidth<e.scrollWidth||e.clientHeight<e.scrollHeight){var o=h(e);if(e.clientWidth<e.scrollWidth&&(o.overflowX=="auto"||o.overflowX=="scroll")||e.clientHeight<e.scrollHeight&&(o.overflowY=="auto"||o.overflowY=="scroll")){if(!e.getBoundingClientRect||e===document.body)return H();if(n||t)return e;n=!0}}while(e=e.parentNode);return H()}function Ue(i,t){if(i&&t)for(var e in t)t.hasOwnProperty(e)&&(i[e]=t[e]);return i}function Kt(i,t){return Math.round(i.top)===Math.round(t.top)&&Math.round(i.left)===Math.round(t.left)&&Math.round(i.height)===Math.round(t.height)&&Math.round(i.width)===Math.round(t.width)}var yt;function _e(i,t){return function(){if(!yt){var e=arguments,n=this;e.length===1?i.call(n,e[0]):i.apply(n,e),yt=setTimeout(function(){yt=void 0},t)}}}function $e(){clearTimeout(yt),yt=void 0}function Se(i,t,e){i.scrollLeft+=t,i.scrollTop+=e}function Te(i){var t=window.Polymer,e=window.jQuery||window.Zepto;return t&&t.dom?t.dom(i).cloneNode(!0):e?e(i).clone(!0)[0]:i.cloneNode(!0)}function Ie(i,t,e){var n={};return Array.from(i.children).forEach(function(o){var r,a,l,s;if(!(!X(o,t.draggable,i,!1)||o.animated||o===e)){var u=I(o);n.left=Math.min((r=n.left)!==null&&r!==void 0?r:1/0,u.left),n.top=Math.min((a=n.top)!==null&&a!==void 0?a:1/0,u.top),n.right=Math.max((l=n.right)!==null&&l!==void 0?l:-1/0,u.right),n.bottom=Math.max((s=n.bottom)!==null&&s!==void 0?s:-1/0,u.bottom)}}),n.width=n.right-n.left,n.height=n.bottom-n.top,n.x=n.left,n.y=n.top,n}var P="Sortable"+new Date().getTime();function Ve(){var i=[],t;return{captureAnimationState:function(){if(i=[],!!this.options.animation){var n=[].slice.call(this.el.children);n.forEach(function(o){if(!(h(o,"display")==="none"||o===p.ghost)){i.push({target:o,rect:I(o)});var r=Q({},i[i.length-1].rect);if(o.thisAnimationDuration){var a=dt(o,!0);a&&(r.top-=a.f,r.left-=a.e)}o.fromRect=r}})}},addAnimationState:function(n){i.push(n)},removeAnimationState:function(n){i.splice(je(i,{target:n}),1)},animateAll:function(n){var o=this;if(!this.options.animation){clearTimeout(t),typeof n=="function"&&n();return}var r=!1,a=0;i.forEach(function(l){var s=0,u=l.target,f=u.fromRect,c=I(u),y=u.prevFromRect,D=u.prevToRect,w=l.rect,E=dt(u,!0);E&&(c.top-=E.f,c.left-=E.e),u.toRect=c,u.thisAnimationDuration&&Kt(y,c)&&!Kt(f,c)&&(w.top-c.top)/(w.left-c.left)===(f.top-c.top)/(f.left-c.left)&&(s=Ze(w,y,D,o.options)),Kt(c,f)||(u.prevFromRect=f,u.prevToRect=c,s||(s=o.options.animation),o.animate(u,w,c,s)),s&&(r=!0,a=Math.max(a,s),clearTimeout(u.animationResetTimer),u.animationResetTimer=setTimeout(function(){u.animationTime=0,u.prevFromRect=null,u.fromRect=null,u.prevToRect=null,u.thisAnimationDuration=null},s),u.thisAnimationDuration=s)}),clearTimeout(t),r?t=setTimeout(function(){typeof n=="function"&&n()},a):typeof n=="function"&&n(),i=[]},animate:function(n,o,r,a){if(a){h(n,"transition",""),h(n,"transform","");var l=dt(this.el),s=l&&l.a,u=l&&l.d,f=(o.left-r.left)/(s||1),c=(o.top-r.top)/(u||1);n.animatingX=!!f,n.animatingY=!!c,h(n,"transform","translate3d("+f+"px,"+c+"px,0)"),this.forRepaintDummy=Ke(n),h(n,"transition","transform "+a+"ms"+(this.options.easing?" "+this.options.easing:"")),h(n,"transform","translate3d(0,0,0)"),typeof n.animated=="number"&&clearTimeout(n.animated),n.animated=setTimeout(function(){h(n,"transition",""),h(n,"transform",""),n.animated=!1,n.animatingX=!1,n.animatingY=!1},a)}}}}function Ke(i){return i.offsetWidth}function Ze(i,t,e,n){return Math.sqrt(Math.pow(t.top-i.top,2)+Math.pow(t.left-i.left,2))/Math.sqrt(Math.pow(t.top-e.top,2)+Math.pow(t.left-e.left,2))*n.animation}var ft=[],Zt={initializeByDefault:!0},wt={mount:function(t){for(var e in Zt)Zt.hasOwnProperty(e)&&!(e in t)&&(t[e]=Zt[e]);ft.forEach(function(n){if(n.pluginName===t.pluginName)throw"Sortable: Cannot mount plugin ".concat(t.pluginName," more than once")}),ft.push(t)},pluginEvent:function(t,e,n){var o=this;this.eventCanceled=!1,n.cancel=function(){o.eventCanceled=!0};var r=t+"Global";ft.forEach(function(a){e[a.pluginName]&&(e[a.pluginName][r]&&e[a.pluginName][r](Q({sortable:e},n)),e.options[a.pluginName]&&e[a.pluginName][t]&&e[a.pluginName][t](Q({sortable:e},n)))})},initializePlugins:function(t,e,n,o){ft.forEach(function(l){var s=l.pluginName;if(!(!t.options[s]&&!l.initializeByDefault)){var u=new l(t,e,t.options);u.sortable=t,u.options=t.options,t[s]=u,z(n,u.defaults)}});for(var r in t.options)if(t.options.hasOwnProperty(r)){var a=this.modifyOption(t,r,t.options[r]);typeof a<"u"&&(t.options[r]=a)}},getEventProperties:function(t,e){var n={};return ft.forEach(function(o){typeof o.eventProperties=="function"&&z(n,o.eventProperties.call(e[o.pluginName],t))}),n},modifyOption:function(t,e,n){var o;return ft.forEach(function(r){t[r.pluginName]&&r.optionListeners&&typeof r.optionListeners[e]=="function"&&(o=r.optionListeners[e].call(t[r.pluginName],n))}),o}};function Je(i){var t=i.sortable,e=i.rootEl,n=i.name,o=i.targetEl,r=i.cloneEl,a=i.toEl,l=i.fromEl,s=i.oldIndex,u=i.newIndex,f=i.oldDraggableIndex,c=i.newDraggableIndex,y=i.originalEvent,D=i.putSortable,w=i.extraEventProperties;if(t=t||e&&e[P],!!t){var E,q=t.options,V="on"+n.charAt(0).toUpperCase()+n.substr(1);window.CustomEvent&&!U&&!vt?E=new CustomEvent(n,{bubbles:!0,cancelable:!0}):(E=document.createEvent("Event"),E.initEvent(n,!0,!0)),E.to=a||e,E.from=l||e,E.item=o||e,E.clone=r,E.oldIndex=s,E.newIndex=u,E.oldDraggableIndex=f,E.newDraggableIndex=c,E.originalEvent=y,E.pullMode=D?D.lastPutMode:void 0;var k=Q(Q({},w),wt.getEventProperties(n,t));for(var G in k)E[G]=k[G];e&&e.dispatchEvent(E),q[V]&&q[V].call(t,E)}}var tn=["evt"],A=function(t,e){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},o=n.evt,r=We(n,tn);wt.pluginEvent.bind(p)(t,e,Q({dragEl:d,parentEl:T,ghostEl:g,rootEl:_,nextEl:ot,lastDownEl:Ft,cloneEl:S,cloneHidden:J,dragStarted:Dt,putSortable:O,activeSortable:p.active,originalEvent:o,oldIndex:ht,oldDraggableIndex:Et,newIndex:F,newDraggableIndex:tt,hideGhostForTarget:Ae,unhideGhostForTarget:Me,cloneNowHidden:function(){J=!0},cloneNowShown:function(){J=!1},dispatchSortableEvent:function(l){N({sortable:e,name:l,originalEvent:o})}},r))};function N(i){Je(Q({putSortable:O,cloneEl:S,targetEl:d,rootEl:_,oldIndex:ht,oldDraggableIndex:Et,newIndex:F,newDraggableIndex:tt},i))}var d,T,g,_,ot,Ft,S,J,ht,F,Et,tt,Lt,O,pt=!1,Rt=!1,Bt=[],rt,Y,Jt,te,Ce,Oe,Dt,mt,_t,St=!1,Xt=!1,Yt,x,ee=[],ne=!1,qt=[],Gt=typeof document<"u",Qt=$t,xe=vt||U?"cssFloat":"float",en=Gt&&!ge&&!$t&&"draggable"in document.createElement("div"),Ne=(function(){if(Gt){if(U)return!1;var i=document.createElement("x");return i.style.cssText="pointer-events:auto",i.style.pointerEvents==="auto"}})(),ke=function(t,e){var n=h(t),o=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),r=ct(t,0,e),a=ct(t,1,e),l=r&&h(r),s=a&&h(a),u=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+I(r).width,f=s&&parseInt(s.marginLeft)+parseInt(s.marginRight)+I(a).width;if(n.display==="flex")return n.flexDirection==="column"||n.flexDirection==="column-reverse"?"vertical":"horizontal";if(n.display==="grid")return n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(r&&l.float&&l.float!=="none"){var c=l.float==="left"?"left":"right";return a&&(s.clear==="both"||s.clear===c)?"vertical":"horizontal"}return r&&(l.display==="block"||l.display==="flex"||l.display==="table"||l.display==="grid"||u>=o&&n[xe]==="none"||a&&n[xe]==="none"&&u+f>o)?"vertical":"horizontal"},nn=function(t,e,n){var o=n?t.left:t.top,r=n?t.right:t.bottom,a=n?t.width:t.height,l=n?e.left:e.top,s=n?e.right:e.bottom,u=n?e.width:e.height;return o===l||r===s||o+a/2===l+u/2},on=function(t,e){var n;return Bt.some(function(o){var r=o[P].options.emptyInsertThreshold;if(!(!r||Vt(o))){var a=I(o),l=t>=a.left-r&&t<=a.right+r,s=e>=a.top-r&&e<=a.bottom+r;if(l&&s)return n=o}}),n},Pe=function(t){function e(r,a){return function(l,s,u,f){var c=l.options.group.name&&s.options.group.name&&l.options.group.name===s.options.group.name;if(r==null&&(a||c))return!0;if(r==null||r===!1)return!1;if(a&&r==="clone")return r;if(typeof r=="function")return e(r(l,s,u,f),a)(l,s,u,f);var y=(a?l:s).options.group.name;return r===!0||typeof r=="string"&&r===y||r.join&&r.indexOf(y)>-1}}var n={},o=t.group;(!o||At(o)!="object")&&(o={name:o}),n.name=o.name,n.checkPull=e(o.pull,!0),n.checkPut=e(o.put),n.revertClone=o.revertClone,t.group=n},Ae=function(){!Ne&&g&&h(g,"display","none")},Me=function(){!Ne&&g&&h(g,"display","")};Gt&&!ge&&document.addEventListener("click",function(i){if(Rt)return i.preventDefault(),i.stopPropagation&&i.stopPropagation(),i.stopImmediatePropagation&&i.stopImmediatePropagation(),Rt=!1,!1},!0);var at=function(t){if(d){t=t.touches?t.touches[0]:t;var e=on(t.clientX,t.clientY);if(e){var n={};for(var o in t)t.hasOwnProperty(o)&&(n[o]=t[o]);n.target=n.rootEl=e,n.preventDefault=void 0,n.stopPropagation=void 0,e[P]._onDragOver(n)}}},rn=function(t){d&&d.parentNode[P]._isOutsideThisEl(t.target)};function p(i,t){if(!(i&&i.nodeType&&i.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(i));this.el=i,this.options=t=z({},t),i[P]=this;var e={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(i.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return ke(i,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(a,l){a.setData("Text",l.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:p.supportPointer!==!1&&"PointerEvent"in window&&(!bt||$t),emptyInsertThreshold:5};wt.initializePlugins(this,i,e);for(var n in e)!(n in t)&&(t[n]=e[n]);Pe(t);for(var o in this)o.charAt(0)==="_"&&typeof this[o]=="function"&&(this[o]=this[o].bind(this));this.nativeDraggable=t.forceFallback?!1:en,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?b(i,"pointerdown",this._onTapStart):(b(i,"mousedown",this._onTapStart),b(i,"touchstart",this._onTapStart)),this.nativeDraggable&&(b(i,"dragover",this),b(i,"dragenter",this)),Bt.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),z(this,Ve())}p.prototype={constructor:p,_isOutsideThisEl:function(t){!this.el.contains(t)&&t!==this.el&&(mt=null)},_getDirection:function(t,e){return typeof this.options.direction=="function"?this.options.direction.call(this,t,e,d):this.options.direction},_onTapStart:function(t){if(t.cancelable){var e=this,n=this.el,o=this.options,r=o.preventOnFilter,a=t.type,l=t.touches&&t.touches[0]||t.pointerType&&t.pointerType==="touch"&&t,s=(l||t).target,u=t.target.shadowRoot&&(t.path&&t.path[0]||t.composedPath&&t.composedPath()[0])||s,f=o.filter;if(hn(n),!d&&!(/mousedown|pointerdown/.test(a)&&t.button!==0||o.disabled)&&!u.isContentEditable&&!(!this.nativeDraggable&&bt&&s&&s.tagName.toUpperCase()==="SELECT")&&(s=X(s,o.draggable,n,!1),!(s&&s.animated)&&Ft!==s)){if(ht=B(s),Et=B(s,o.draggable),typeof f=="function"){if(f.call(this,t,s,this)){N({sortable:e,rootEl:u,name:"filter",targetEl:s,toEl:n,fromEl:n}),A("filter",e,{evt:t}),r&&t.preventDefault();return}}else if(f&&(f=f.split(",").some(function(c){if(c=X(u,c.trim(),n,!1),c)return N({sortable:e,rootEl:c,name:"filter",targetEl:s,fromEl:n,toEl:n}),A("filter",e,{evt:t}),!0}),f)){r&&t.preventDefault();return}o.handle&&!X(u,o.handle,n,!1)||this._prepareDragStart(t,l,s)}}},_prepareDragStart:function(t,e,n){var o=this,r=o.el,a=o.options,l=r.ownerDocument,s;if(n&&!d&&n.parentNode===r){var u=I(n);if(_=r,d=n,T=d.parentNode,ot=d.nextSibling,Ft=n,Lt=a.group,p.dragged=d,rt={target:d,clientX:(e||t).clientX,clientY:(e||t).clientY},Ce=rt.clientX-u.left,Oe=rt.clientY-u.top,this._lastX=(e||t).clientX,this._lastY=(e||t).clientY,d.style["will-change"]="all",s=function(){if(A("delayEnded",o,{evt:t}),p.eventCanceled){o._onDrop();return}o._disableDelayedDragEvents(),!me&&o.nativeDraggable&&(d.draggable=!0),o._triggerDragStart(t,e),N({sortable:o,name:"choose",originalEvent:t}),M(d,a.chosenClass,!0)},a.ignore.split(",").forEach(function(f){we(d,f.trim(),ie)}),b(l,"dragover",at),b(l,"mousemove",at),b(l,"touchmove",at),a.supportPointer?(b(l,"pointerup",o._onDrop),!this.nativeDraggable&&b(l,"pointercancel",o._onDrop)):(b(l,"mouseup",o._onDrop),b(l,"touchend",o._onDrop),b(l,"touchcancel",o._onDrop)),me&&this.nativeDraggable&&(this.options.touchStartThreshold=4,d.draggable=!0),A("delayStart",this,{evt:t}),a.delay&&(!a.delayOnTouchOnly||e)&&(!this.nativeDraggable||!(vt||U))){if(p.eventCanceled){this._onDrop();return}a.supportPointer?(b(l,"pointerup",o._disableDelayedDrag),b(l,"pointercancel",o._disableDelayedDrag)):(b(l,"mouseup",o._disableDelayedDrag),b(l,"touchend",o._disableDelayedDrag),b(l,"touchcancel",o._disableDelayedDrag)),b(l,"mousemove",o._delayedDragTouchMoveHandler),b(l,"touchmove",o._delayedDragTouchMoveHandler),a.supportPointer&&b(l,"pointermove",o._delayedDragTouchMoveHandler),o._dragStartTimer=setTimeout(s,a.delay)}else s()}},_delayedDragTouchMoveHandler:function(t){var e=t.touches?t.touches[0]:t;Math.max(Math.abs(e.clientX-this._lastX),Math.abs(e.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){d&&ie(d),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var t=this.el.ownerDocument;v(t,"mouseup",this._disableDelayedDrag),v(t,"touchend",this._disableDelayedDrag),v(t,"touchcancel",this._disableDelayedDrag),v(t,"pointerup",this._disableDelayedDrag),v(t,"pointercancel",this._disableDelayedDrag),v(t,"mousemove",this._delayedDragTouchMoveHandler),v(t,"touchmove",this._delayedDragTouchMoveHandler),v(t,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(t,e){e=e||t.pointerType=="touch"&&t,!this.nativeDraggable||e?this.options.supportPointer?b(document,"pointermove",this._onTouchMove):e?b(document,"touchmove",this._onTouchMove):b(document,"mousemove",this._onTouchMove):(b(d,"dragend",this),b(_,"dragstart",this._onDragStart));try{document.selection?Wt(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(t,e){if(pt=!1,_&&d){A("dragStarted",this,{evt:e}),this.nativeDraggable&&b(document,"dragover",rn);var n=this.options;!t&&M(d,n.dragClass,!1),M(d,n.ghostClass,!0),p.active=this,t&&this._appendGhost(),N({sortable:this,name:"start",originalEvent:e})}else this._nulling()},_emulateDragOver:function(){if(Y){this._lastX=Y.clientX,this._lastY=Y.clientY,Ae();for(var t=document.elementFromPoint(Y.clientX,Y.clientY),e=t;t&&t.shadowRoot&&(t=t.shadowRoot.elementFromPoint(Y.clientX,Y.clientY),t!==e);)e=t;if(d.parentNode[P]._isOutsideThisEl(t),e)do{if(e[P]){var n=void 0;if(n=e[P]._onDragOver({clientX:Y.clientX,clientY:Y.clientY,target:t,rootEl:e}),n&&!this.options.dragoverBubble)break}t=e}while(e=be(e));Me()}},_onTouchMove:function(t){if(rt){var e=this.options,n=e.fallbackTolerance,o=e.fallbackOffset,r=t.touches?t.touches[0]:t,a=g&&dt(g,!0),l=g&&a&&a.a,s=g&&a&&a.d,u=Qt&&x&&De(x),f=(r.clientX-rt.clientX+o.x)/(l||1)+(u?u[0]-ee[0]:0)/(l||1),c=(r.clientY-rt.clientY+o.y)/(s||1)+(u?u[1]-ee[1]:0)/(s||1);if(!p.active&&!pt){if(n&&Math.max(Math.abs(r.clientX-this._lastX),Math.abs(r.clientY-this._lastY))<n)return;this._onDragStart(t,!0)}if(g){a?(a.e+=f-(Jt||0),a.f+=c-(te||0)):a={a:1,b:0,c:0,d:1,e:f,f:c};var y="matrix(".concat(a.a,",").concat(a.b,",").concat(a.c,",").concat(a.d,",").concat(a.e,",").concat(a.f,")");h(g,"webkitTransform",y),h(g,"mozTransform",y),h(g,"msTransform",y),h(g,"transform",y),Jt=f,te=c,Y=r}t.cancelable&&t.preventDefault()}},_appendGhost:function(){if(!g){var t=this.options.fallbackOnBody?document.body:_,e=I(d,!0,Qt,!0,t),n=this.options;if(Qt){for(x=t;h(x,"position")==="static"&&h(x,"transform")==="none"&&x!==document;)x=x.parentNode;x!==document.body&&x!==document.documentElement?(x===document&&(x=H()),e.top+=x.scrollTop,e.left+=x.scrollLeft):x=H(),ee=De(x)}g=d.cloneNode(!0),M(g,n.ghostClass,!1),M(g,n.fallbackClass,!0),M(g,n.dragClass,!0),h(g,"transition",""),h(g,"transform",""),h(g,"box-sizing","border-box"),h(g,"margin",0),h(g,"top",e.top),h(g,"left",e.left),h(g,"width",e.width),h(g,"height",e.height),h(g,"opacity","0.8"),h(g,"position",Qt?"absolute":"fixed"),h(g,"zIndex","100000"),h(g,"pointerEvents","none"),p.ghost=g,t.appendChild(g),h(g,"transform-origin",Ce/parseInt(g.style.width)*100+"% "+Oe/parseInt(g.style.height)*100+"%")}},_onDragStart:function(t,e){var n=this,o=t.dataTransfer,r=n.options;if(A("dragStart",this,{evt:t}),p.eventCanceled){this._onDrop();return}A("setupClone",this),p.eventCanceled||(S=Te(d),S.removeAttribute("id"),S.draggable=!1,S.style["will-change"]="",this._hideClone(),M(S,this.options.chosenClass,!1),p.clone=S),n.cloneId=Wt(function(){A("clone",n),!p.eventCanceled&&(n.options.removeCloneOnHide||_.insertBefore(S,d),n._hideClone(),N({sortable:n,name:"clone"}))}),!e&&M(d,r.dragClass,!0),e?(Rt=!0,n._loopId=setInterval(n._emulateDragOver,50)):(v(document,"mouseup",n._onDrop),v(document,"touchend",n._onDrop),v(document,"touchcancel",n._onDrop),o&&(o.effectAllowed="move",r.setData&&r.setData.call(n,o,d)),b(document,"drop",n),h(d,"transform","translateZ(0)")),pt=!0,n._dragStartId=Wt(n._dragStarted.bind(n,e,t)),b(document,"selectstart",n),Dt=!0,window.getSelection().removeAllRanges(),bt&&h(document.body,"user-select","none")},_onDragOver:function(t){var e=this.el,n=t.target,o,r,a,l=this.options,s=l.group,u=p.active,f=Lt===s,c=l.sort,y=O||u,D,w=this,E=!1;if(ne)return;function q(kt,gn){A(kt,w,Q({evt:t,isOwner:f,axis:D?"vertical":"horizontal",revert:a,dragRect:o,targetRect:r,canSort:c,fromSortable:y,target:n,completed:k,onMove:function(Be,vn){return Ht(_,e,d,o,Be,I(Be),t,vn)},changed:G},gn))}function V(){q("dragOverAnimationCapture"),w.captureAnimationState(),w!==y&&y.captureAnimationState()}function k(kt){return q("dragOverCompleted",{insertion:kt}),kt&&(f?u._hideClone():u._showClone(w),w!==y&&(M(d,O?O.options.ghostClass:u.options.ghostClass,!1),M(d,l.ghostClass,!0)),O!==w&&w!==p.active?O=w:w===p.active&&O&&(O=null),y===w&&(w._ignoreWhileAnimating=n),w.animateAll(function(){q("dragOverAnimationComplete"),w._ignoreWhileAnimating=null}),w!==y&&(y.animateAll(),y._ignoreWhileAnimating=null)),(n===d&&!d.animated||n===e&&!n.animated)&&(mt=null),!l.dragoverBubble&&!t.rootEl&&n!==document&&(d.parentNode[P]._isOutsideThisEl(t.target),!kt&&at(t)),!l.dragoverBubble&&t.stopPropagation&&t.stopPropagation(),E=!0}function G(){F=B(d),tt=B(d,l.draggable),N({sortable:w,name:"change",toEl:e,newIndex:F,newDraggableIndex:tt,originalEvent:t})}if(t.preventDefault!==void 0&&t.cancelable&&t.preventDefault(),n=X(n,l.draggable,e,!0),q("dragOver"),p.eventCanceled)return E;if(d.contains(t.target)||n.animated&&n.animatingX&&n.animatingY||w._ignoreWhileAnimating===n)return k(!1);if(Rt=!1,u&&!l.disabled&&(f?c||(a=T!==_):O===this||(this.lastPutMode=Lt.checkPull(this,u,d,t))&&s.checkPut(this,u,d,t))){if(D=this._getDirection(t,n)==="vertical",o=I(d),q("dragOverValid"),p.eventCanceled)return E;if(a)return T=_,V(),this._hideClone(),q("revert"),p.eventCanceled||(ot?_.insertBefore(d,ot):_.appendChild(d)),k(!0);var L=Vt(e,l.draggable);if(!L||un(t,D,this)&&!L.animated){if(L===d)return k(!1);if(L&&e===t.target&&(n=L),n&&(r=I(n)),Ht(_,e,d,o,n,r,t,!!n)!==!1)return V(),L&&L.nextSibling?e.insertBefore(d,L.nextSibling):e.appendChild(d),T=e,G(),k(!0)}else if(L&&sn(t,D,this)){var lt=ct(e,0,l,!0);if(lt===d)return k(!1);if(n=lt,r=I(n),Ht(_,e,d,o,n,r,t,!1)!==!1)return V(),e.insertBefore(d,lt),T=e,G(),k(!0)}else if(n.parentNode===e){r=I(n);var W=0,st,Ct=d.parentNode!==e,R=!nn(d.animated&&d.toRect||o,n.animated&&n.toRect||r,D),Ot=D?"top":"left",et=Ee(n,"top","top")||Ee(d,"top","top"),xt=et?et.scrollTop:void 0;mt!==n&&(st=r[Ot],St=!1,Xt=!R&&l.invertSwap||Ct),W=dn(t,n,r,D,R?1:l.swapThreshold,l.invertedSwapThreshold==null?l.swapThreshold:l.invertedSwapThreshold,Xt,mt===n);var K;if(W!==0){var ut=B(d);do ut-=W,K=T.children[ut];while(K&&(h(K,"display")==="none"||K===g))}if(W===0||K===n)return k(!1);mt=n,_t=W;var Nt=n.nextElementSibling,nt=!1;nt=W===1;var Ut=Ht(_,e,d,o,n,r,t,nt);if(Ut!==!1)return(Ut===1||Ut===-1)&&(nt=Ut===1),ne=!0,setTimeout(ln,30),V(),nt&&!Nt?e.appendChild(d):n.parentNode.insertBefore(d,nt?Nt:n),et&&Se(et,0,xt-et.scrollTop),T=d.parentNode,st!==void 0&&!Xt&&(Yt=Math.abs(st-I(n)[Ot])),G(),k(!0)}if(e.contains(d))return k(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){v(document,"mousemove",this._onTouchMove),v(document,"touchmove",this._onTouchMove),v(document,"pointermove",this._onTouchMove),v(document,"dragover",at),v(document,"mousemove",at),v(document,"touchmove",at)},_offUpEvents:function(){var t=this.el.ownerDocument;v(t,"mouseup",this._onDrop),v(t,"touchend",this._onDrop),v(t,"pointerup",this._onDrop),v(t,"pointercancel",this._onDrop),v(t,"touchcancel",this._onDrop),v(document,"selectstart",this)},_onDrop:function(t){var e=this.el,n=this.options;if(F=B(d),tt=B(d,n.draggable),A("drop",this,{evt:t}),T=d&&d.parentNode,F=B(d),tt=B(d,n.draggable),p.eventCanceled){this._nulling();return}pt=!1,Xt=!1,St=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),oe(this.cloneId),oe(this._dragStartId),this.nativeDraggable&&(v(document,"drop",this),v(e,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),bt&&h(document.body,"user-select",""),h(d,"transform",""),t&&(Dt&&(t.cancelable&&t.preventDefault(),!n.dropBubble&&t.stopPropagation()),g&&g.parentNode&&g.parentNode.removeChild(g),(_===T||O&&O.lastPutMode!=="clone")&&S&&S.parentNode&&S.parentNode.removeChild(S),d&&(this.nativeDraggable&&v(d,"dragend",this),ie(d),d.style["will-change"]="",Dt&&!pt&&M(d,O?O.options.ghostClass:this.options.ghostClass,!1),M(d,this.options.chosenClass,!1),N({sortable:this,name:"unchoose",toEl:T,newIndex:null,newDraggableIndex:null,originalEvent:t}),_!==T?(F>=0&&(N({rootEl:T,name:"add",toEl:T,fromEl:_,originalEvent:t}),N({sortable:this,name:"remove",toEl:T,originalEvent:t}),N({rootEl:T,name:"sort",toEl:T,fromEl:_,originalEvent:t}),N({sortable:this,name:"sort",toEl:T,originalEvent:t})),O&&O.save()):F!==ht&&F>=0&&(N({sortable:this,name:"update",toEl:T,originalEvent:t}),N({sortable:this,name:"sort",toEl:T,originalEvent:t})),p.active&&((F==null||F===-1)&&(F=ht,tt=Et),N({sortable:this,name:"end",toEl:T,originalEvent:t}),this.save()))),this._nulling()},_nulling:function(){A("nulling",this),_=d=T=g=ot=S=Ft=J=rt=Y=Dt=F=tt=ht=Et=mt=_t=O=Lt=p.dragged=p.ghost=p.clone=p.active=null,qt.forEach(function(t){t.checked=!0}),qt.length=Jt=te=0},handleEvent:function(t){switch(t.type){case"drop":case"dragend":this._onDrop(t);break;case"dragenter":case"dragover":d&&(this._onDragOver(t),an(t));break;case"selectstart":t.preventDefault();break}},toArray:function(){for(var t=[],e,n=this.el.children,o=0,r=n.length,a=this.options;o<r;o++)e=n[o],X(e,a.draggable,this.el,!1)&&t.push(e.getAttribute(a.dataIdAttr)||fn(e));return t},sort:function(t,e){var n={},o=this.el;this.toArray().forEach(function(r,a){var l=o.children[a];X(l,this.options.draggable,o,!1)&&(n[r]=l)},this),e&&this.captureAnimationState(),t.forEach(function(r){n[r]&&(o.removeChild(n[r]),o.appendChild(n[r]))}),e&&this.animateAll()},save:function(){var t=this.options.store;t&&t.set&&t.set(this)},closest:function(t,e){return X(t,e||this.options.draggable,this.el,!1)},option:function(t,e){var n=this.options;if(e===void 0)return n[t];var o=wt.modifyOption(this,t,e);typeof o<"u"?n[t]=o:n[t]=e,t==="group"&&Pe(n)},destroy:function(){A("destroy",this);var t=this.el;t[P]=null,v(t,"mousedown",this._onTapStart),v(t,"touchstart",this._onTapStart),v(t,"pointerdown",this._onTapStart),this.nativeDraggable&&(v(t,"dragover",this),v(t,"dragenter",this)),Array.prototype.forEach.call(t.querySelectorAll("[draggable]"),function(e){e.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Bt.splice(Bt.indexOf(this.el),1),this.el=t=null},_hideClone:function(){if(!J){if(A("hideClone",this),p.eventCanceled)return;h(S,"display","none"),this.options.removeCloneOnHide&&S.parentNode&&S.parentNode.removeChild(S),J=!0}},_showClone:function(t){if(t.lastPutMode!=="clone"){this._hideClone();return}if(J){if(A("showClone",this),p.eventCanceled)return;d.parentNode==_&&!this.options.group.revertClone?_.insertBefore(S,d):ot?_.insertBefore(S,ot):_.appendChild(S),this.options.group.revertClone&&this.animate(d,S),h(S,"display",""),J=!1}}};function an(i){i.dataTransfer&&(i.dataTransfer.dropEffect="move"),i.cancelable&&i.preventDefault()}function Ht(i,t,e,n,o,r,a,l){var s,u=i[P],f=u.options.onMove,c;return window.CustomEvent&&!U&&!vt?s=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(s=document.createEvent("Event"),s.initEvent("move",!0,!0)),s.to=t,s.from=i,s.dragged=e,s.draggedRect=n,s.related=o||t,s.relatedRect=r||I(t),s.willInsertAfter=l,s.originalEvent=a,i.dispatchEvent(s),f&&(c=f.call(u,s,a)),c}function ie(i){i.draggable=!1}function ln(){ne=!1}function sn(i,t,e){var n=I(ct(e.el,0,e.options,!0)),o=Ie(e.el,e.options,g),r=10;return t?i.clientX<o.left-r||i.clientY<n.top&&i.clientX<n.right:i.clientY<o.top-r||i.clientY<n.bottom&&i.clientX<n.left}function un(i,t,e){var n=I(Vt(e.el,e.options.draggable)),o=Ie(e.el,e.options,g),r=10;return t?i.clientX>o.right+r||i.clientY>n.bottom&&i.clientX>n.left:i.clientY>o.bottom+r||i.clientX>n.right&&i.clientY>n.top}function dn(i,t,e,n,o,r,a,l){var s=n?i.clientY:i.clientX,u=n?e.height:e.width,f=n?e.top:e.left,c=n?e.bottom:e.right,y=!1;if(!a){if(l&&Yt<u*o){if(!St&&(_t===1?s>f+u*r/2:s<c-u*r/2)&&(St=!0),St)y=!0;else if(_t===1?s<f+Yt:s>c-Yt)return-_t}else if(s>f+u*(1-o)/2&&s<c-u*(1-o)/2)return cn(t)}return y=y||a,y&&(s<f+u*r/2||s>c-u*r/2)?s>f+u/2?1:-1:0}function cn(i){return B(d)<B(i)?1:-1}function fn(i){for(var t=i.tagName+i.className+i.src+i.href+i.textContent,e=t.length,n=0;e--;)n+=t.charCodeAt(e);return n.toString(36)}function hn(i){qt.length=0;for(var t=i.getElementsByTagName("input"),e=t.length;e--;){var n=t[e];n.checked&&qt.push(n)}}function Wt(i){return setTimeout(i,0)}function oe(i){return clearTimeout(i)}Gt&&b(document,"touchmove",function(i){(p.active||pt)&&i.cancelable&&i.preventDefault()}),p.utils={on:b,off:v,css:h,find:we,is:function(t,e){return!!X(t,e,t,!1)},extend:Ue,throttle:_e,closest:X,toggleClass:M,clone:Te,index:B,nextTick:Wt,cancelNextTick:oe,detectDirection:ke,getChild:ct,expando:P},p.get=function(i){return i[P]},p.mount=function(){for(var i=arguments.length,t=new Array(i),e=0;e<i;e++)t[e]=arguments[e];t[0].constructor===Array&&(t=t[0]),t.forEach(function(n){if(!n.prototype||!n.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(n));n.utils&&(p.utils=Q(Q({},p.utils),n.utils)),wt.mount(n)})},p.create=function(i,t){return new p(i,t)},p.version=ze;var C=[],Tt,re,ae=!1,le,se,zt,It;function pn(){function i(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var t in this)t.charAt(0)==="_"&&typeof this[t]=="function"&&(this[t]=this[t].bind(this))}return i.prototype={dragStarted:function(e){var n=e.originalEvent;this.sortable.nativeDraggable?b(document,"dragover",this._handleAutoScroll):this.options.supportPointer?b(document,"pointermove",this._handleFallbackAutoScroll):n.touches?b(document,"touchmove",this._handleFallbackAutoScroll):b(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(e){var n=e.originalEvent;!this.options.dragOverBubble&&!n.rootEl&&this._handleAutoScroll(n)},drop:function(){this.sortable.nativeDraggable?v(document,"dragover",this._handleAutoScroll):(v(document,"pointermove",this._handleFallbackAutoScroll),v(document,"touchmove",this._handleFallbackAutoScroll),v(document,"mousemove",this._handleFallbackAutoScroll)),Fe(),jt(),$e()},nulling:function(){zt=re=Tt=ae=It=le=se=null,C.length=0},_handleFallbackAutoScroll:function(e){this._handleAutoScroll(e,!0)},_handleAutoScroll:function(e,n){var o=this,r=(e.touches?e.touches[0]:e).clientX,a=(e.touches?e.touches[0]:e).clientY,l=document.elementFromPoint(r,a);if(zt=e,n||this.options.forceAutoScrollFallback||vt||U||bt){ue(e,this.options,l,n);var s=Z(l,!0);ae&&(!It||r!==le||a!==se)&&(It&&Fe(),It=setInterval(function(){var u=Z(document.elementFromPoint(r,a),!0);u!==s&&(s=u,jt()),ue(e,o.options,u,n)},10),le=r,se=a)}else{if(!this.options.bubbleScroll||Z(l,!0)===H()){jt();return}ue(e,this.options,Z(l,!1),!1)}}},z(i,{pluginName:"scroll",initializeByDefault:!0})}function jt(){C.forEach(function(i){clearInterval(i.pid)}),C=[]}function Fe(){clearInterval(It)}var ue=_e(function(i,t,e,n){if(t.scroll){var o=(i.touches?i.touches[0]:i).clientX,r=(i.touches?i.touches[0]:i).clientY,a=t.scrollSensitivity,l=t.scrollSpeed,s=H(),u=!1,f;re!==e&&(re=e,jt(),Tt=t.scroll,f=t.scrollFn,Tt===!0&&(Tt=Z(e,!0)));var c=0,y=Tt;do{var D=y,w=I(D),E=w.top,q=w.bottom,V=w.left,k=w.right,G=w.width,L=w.height,lt=void 0,W=void 0,st=D.scrollWidth,Ct=D.scrollHeight,R=h(D),Ot=D.scrollLeft,et=D.scrollTop;D===s?(lt=G<st&&(R.overflowX==="auto"||R.overflowX==="scroll"||R.overflowX==="visible"),W=L<Ct&&(R.overflowY==="auto"||R.overflowY==="scroll"||R.overflowY==="visible")):(lt=G<st&&(R.overflowX==="auto"||R.overflowX==="scroll"),W=L<Ct&&(R.overflowY==="auto"||R.overflowY==="scroll"));var xt=lt&&(Math.abs(k-o)<=a&&Ot+G<st)-(Math.abs(V-o)<=a&&!!Ot),K=W&&(Math.abs(q-r)<=a&&et+L<Ct)-(Math.abs(E-r)<=a&&!!et);if(!C[c])for(var ut=0;ut<=c;ut++)C[ut]||(C[ut]={});(C[c].vx!=xt||C[c].vy!=K||C[c].el!==D)&&(C[c].el=D,C[c].vx=xt,C[c].vy=K,clearInterval(C[c].pid),(xt!=0||K!=0)&&(u=!0,C[c].pid=setInterval((function(){n&&this.layer===0&&p.active._onTouchMove(zt);var Nt=C[this.layer].vy?C[this.layer].vy*l:0,nt=C[this.layer].vx?C[this.layer].vx*l:0;typeof f=="function"&&f.call(p.dragged.parentNode[P],nt,Nt,i,zt,C[this.layer].el)!=="continue"||Se(C[this.layer].el,nt,Nt)}).bind({layer:c}),24))),c++}while(t.bubbleScroll&&y!==s&&(y=Z(y,!1)));ae=u}},30),Le=function(t){var e=t.originalEvent,n=t.putSortable,o=t.dragEl,r=t.activeSortable,a=t.dispatchSortableEvent,l=t.hideGhostForTarget,s=t.unhideGhostForTarget;if(e){var u=n||r;l();var f=e.changedTouches&&e.changedTouches.length?e.changedTouches[0]:e,c=document.elementFromPoint(f.clientX,f.clientY);s(),u&&!u.el.contains(c)&&(a("spill"),this.onSpill({dragEl:o,putSortable:n}))}};function de(){}de.prototype={startIndex:null,dragStart:function(t){var e=t.oldDraggableIndex;this.startIndex=e},onSpill:function(t){var e=t.dragEl,n=t.putSortable;this.sortable.captureAnimationState(),n&&n.captureAnimationState();var o=ct(this.sortable.el,this.startIndex,this.options);o?this.sortable.el.insertBefore(e,o):this.sortable.el.appendChild(e),this.sortable.animateAll(),n&&n.animateAll()},drop:Le},z(de,{pluginName:"revertOnSpill"});function ce(){}ce.prototype={onSpill:function(t){var e=t.dragEl,n=t.putSortable,o=n||this.sortable;o.captureAnimationState(),e.parentNode&&e.parentNode.removeChild(e),o.animateAll()},drop:Le},z(ce,{pluginName:"removeOnSpill"}),p.mount(new pn),p.mount(ce,de);class mn extends Xe{oninit(t){super.oninit(t),this.loading=!1,this.linksQueueList=[],this.loadResults()}initSort(){let t=document.getElementById("linksQueueSortableItems");p.create(t,{animation:150,swapThreshold:.65,onEnd:e=>this.updateSort(e)})}content(){return m("div",{className:"ExtensionPage-settings FlarumBadgesPage"},m("div",{className:"container"},m("div",{style:{paddingBottom:"10px"}},m(it,{className:"Button",onclick:()=>app.modal.show(he)},app.translator.trans("wusong8899-links-queue.admin.link-add"))),m("ul",{id:"linksQueueSortableItems",style:{padding:"0px",listStyleType:"none"},oncreate:this.initSort.bind(this)},this.linksQueueList.map(t=>m("li",{itemID:t.id(),style:{marginTop:"5px",background:"var(--body-bg)"}},Ge.component({LinksQueueItemData:t}))))))}updateSort(t){const e=t.newIndex,n=t.oldIndex;if(e!==n){const o=t.from.children,r={};for(let a=0;a<o.length;a++){const l=o[a],s=$(l).attr("itemID");r[s]=a}app.request({url:`${app.forum.attribute("apiUrl")}/linksQueueList/order`,method:"POST",body:{linkQueueOrder:r}})}}parseResults(t){return[].push.apply(this.linksQueueList,t),m.redraw(),t}loadResults(){return app.store.find("linksQueueList").catch(()=>{}).then(this.parseResults.bind(this))}}class Re extends gt{}Object.assign(Re.prototype,{id:gt.attribute("id"),name:gt.attribute("name"),links:gt.attribute("links"),sort:gt.attribute("sort")}),app.initializers.add("wusong8899-client1-links-queue",()=>{app.store.models.linksQueueList=Re,app.extensionData.for("wusong8899-client1-links-queue").registerPage(mn)})})(flarum.core.compat["components/ExtensionPage"],flarum.core.compat["components/Button"],flarum.core.compat["components/Modal"],flarum.core.compat["utils/Stream"],flarum.core.compat.Component,flarum.core.compat.Model);
//# sourceMappingURL=admin.js.map

module.exports={};