{"version": 3, "file": "forum.js", "mappings": "kCAAAA,EAAOC,QAAUC,OAAOC,WAAW,6BCC/BC,EAA2B,GAG/B,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaN,QAGrB,IAAID,EAASI,EAAyBE,GAAY,CAGjDL,QAAS,IAOV,OAHAQ,EAAoBH,GAAUN,EAAQA,EAAOC,QAASI,GAG/CL,EAAOC,QCpBfI,EAAoBK,EAAKV,IACxB,IAAIW,EAASX,GAAUA,EAAOY,WAC7B,IAAOZ,EAAiB,QACxB,IAAM,EAEP,OADAK,EAAoBQ,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GCLRN,EAAoBQ,EAAI,CAACZ,EAASc,KACjC,IAAI,IAAIC,KAAOD,EACXV,EAAoBY,EAAEF,EAAYC,KAASX,EAAoBY,EAAEhB,EAASe,IAC5EE,OAAOC,eAAelB,EAASe,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3EX,EAAoBY,EAAI,CAACK,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFlB,EAAoBsB,EAAK1B,IACH,oBAAX2B,QAA0BA,OAAOC,aAC1CX,OAAOC,eAAelB,EAAS2B,OAAOC,YAAa,CAAEC,MAAO,WAE7DZ,OAAOC,eAAelB,EAAS,aAAc,CAAE6B,OAAO,K,mCCLlB5B,OAAO6B,KAAKC,OAAe,OAAhE,MCAM,EAA+B9B,OAAO6B,KAAKC,OAAO,a,aCAxD,MAAM,EAA+B9B,OAAO6B,KAAKC,OAAO,iBCAlD,EAA+B9B,OAAO6B,KAAKC,OAAO,oC,aCAzC,SAASC,EAAgBhB,EAAGiB,GAMzC,OALAD,EAAkBf,OAAOiB,gBAAkB,SAAyBlB,EAAGiB,GAErE,OADAjB,EAAEmB,UAAYF,EACPjB,GAGFgB,EAAgBhB,EAAGiB,GCLb,SAASG,EAAeC,EAAUC,GAC/CD,EAASd,UAAYN,OAAOsB,OAAOD,EAAWf,WAC9Cc,EAASd,UAAUiB,YAAcH,EACjCH,EAAeG,EAAUC,GCJ3B,MAAM,EAA+BrC,OAAO6B,KAAKC,OAAO,2C,aCAxD,MAAM,EAA+B9B,OAAO6B,KAAKC,OAAO,oB,aCAxD,MAAM,EAA+B9B,OAAO6B,KAAKC,OAAO,sC,aCAxD,MAAM,EAA+B9B,OAAO6B,KAAKC,OAAO,4B,aCAnB9B,OAAO6B,KAAKC,OAAO,6BAAxD,MCAM,EAA+B9B,OAAO6B,KAAKC,OAAO,4B,aCAxD,MAAM,EAA+B9B,OAAO6B,KAAKC,OAAO,2B,aCAxD,MAAM,EAA+B9B,OAAO6B,KAAKC,OAAO,yB,aCAxD,MAAM,EAA+B9B,OAAO6B,KAAKC,OAAO,0B,aCAxD,MAAM,EAA+B9B,OAAO6B,KAAKC,OAAO,uB,aCWnCU,EAAAA,SAAAA,G,oFACnBC,SAAA,SAASC,GACP,YAAMC,OAAN,UAAaD,GACbE,KAAKC,MAAQD,KAAKE,MAAMD,O,EAG1BE,UAAA,SAAUC,GACHC,QAAQC,IAAAA,WAAAA,MAAqB,mEAEZA,IAAAA,MAAAA,IAAc,eAEjBC,OAAO,GACxBD,IAAAA,QAAY,CACRE,OAAQ,SACRC,IAAKH,IAAAA,MAAAA,UAAoB,UAAY,2BAEtCI,MAAK,WACJJ,IAAAA,MAAAA,KAAAA,YAA6B,GAC7BK,EAAEC,a,EAKVC,KAAA,WACE,GAAIb,KAAKC,MAAT,CAEA,IAGIa,EAAgBC,EAHhBC,EAAkBV,IAAAA,MAAAA,IAAc,eAIpC,GAHAU,EAAgBC,MAAK,SAACjD,EAAGkD,GAAJ,OAAU,IAAIC,KAAKD,EAAEE,cAAgB,IAAID,KAAKnD,EAAEoD,iBAGlE,4BAA6BhE,OAAOC,WAAW,CAChD,IAAQgE,EAAeC,EAAQ,KAAvBD,WACRP,EAAkBO,EAAWP,gBAC7BC,EAAoBM,EAAWN,kBAGjC,OACE,SAAKQ,UAAU,oBACb,SAAKA,UAAU,2BACb,QAAIA,UAAU,2CAA2CjB,IAAAA,WAAAA,MAAqB,0CAC9E,SAAKkB,MAAM,sBACT,EAAC,IAAD,CACI,iBAAe,OACfC,KAAK,mBACLF,UAAU,iDACVG,QAAS1B,KAAKG,UAAUwB,KAAK3B,UAIrC,SAAKuB,UAAU,4BACb,QAAIA,UAAU,6BACXP,EAAgBT,OACfS,EAAgBY,KAAI,SAACC,GACnB,IAAMC,EAAOD,EAAYC,OACnBC,EAAOD,EAAKC,OAElB,OACE,YACE,EAAC,IAAD,CACEC,KAAM1B,IAAAA,MAAAA,KAAewB,GACrBP,UAAU,eACVG,QAAS,SAACtB,GACRA,EAAEQ,QAAS,IAGZE,EAAgBA,EAAgBiB,GAAME,GAAAA,CAAOF,GAC7CN,GAAAA,CAAK,MAAO,CAAEF,UAAW,sBAC1B,UAAMA,UAAU,wBACbjB,IAAAA,WAAAA,MAAqB,6CAA8C,CAClE4B,SAAUnB,EAAkBA,EAAkBe,EAAKC,QAAQG,GAAAA,CAASJ,EAAKC,QACzEI,GAAI,aACJC,WAAYN,EAAKM,aAAaC,WAGjCC,GAAAA,CAAUT,EAAYT,cACvB,SAAKG,UAAU,wBAAwBO,EAAKS,qBAKjDvC,KAAKC,MAAMuC,QAGdC,IAAAA,UAA2B,CAAElB,UAAW,4BAFxC,SAAKA,UAAU,0BAA0BjB,IAAAA,WAAAA,MAAqB,qD,EAjFvDV,CAAwB8C,KCRxBC,EAAAA,SAAAA,G,2DACZC,UAAP,SAAiB1C,GACfA,EAAM2C,MAAQvC,IAAIwC,WAAWC,MAAM,yCACnC7C,EAAMuB,KAAO,iBACb,EAAMmB,UAAN,UAAgB1C,I,2BAGlB8C,QAAA,WACE,OACE,SAAKzB,UAAW,iBAAmBvB,KAAKE,MAAM+C,cAAevB,QAAS1B,KAAKkD,UAAUvB,KAAK3B,OACvFA,KAAKmD,QAAUvD,EAAgBwD,UAAU,CAAEnD,MAAOD,KAAKE,MAAMD,QAAW,K,EAK/EoD,UAAA,WACE1C,EAAE2C,MAAMC,IAAIjD,IAAIgD,MAAM,iB,EAGxBE,eAAA,WACE,OAAO,G,EAGTC,YAAA,WACE,OAAOzD,KAAKwD,kB,EAxBKb,CAA4Be,KCHjD,MAAM,EAA+BtG,OAAO6B,KAAKC,OAAO,8B,aCAxD,MAAM,EAA+B9B,OAAO6B,KAAKC,OAAO,kC,aCAxD,MAAM,EAA+B9B,OAAO6B,KAAKC,OAAO,4B,aCAxD,MAAM,EAA+B9B,OAAO6B,KAAKC,OAAO,yB,aCAnCyE,EAAAA,WACnB,WAAYrD,GACVN,KAAKM,IAAMA,EACXN,KAAKwC,SAAU,E,mBAGjBoB,KAAA,WAAO,WACL5D,KAAKwC,SAAU,EACf7B,EAAEC,SAEFZ,KAAKM,IAAIuD,MACNC,KAAK,eACLpD,MAAK,SAACmB,GACL,EAAKW,SAAU,EACf7B,EAAEC,YAJN,OAMS,gB,EAhBQ+C,GCArB,MAAM,EAA+BvG,OAAO6B,KAAKC,OAAO,gB,aCKnC6E,EAAAA,SAAAA,G,oFACnB3C,WAAA,WACE,OAAO4C,IAAAA,UAAwB,eAAepF,KAAKoB,O,EAErDoC,WAAA,WACE,OAAO4B,IAAAA,OAAyB,cAAcpF,KAAKoB,O,EAErD8B,KAAA,WACE,OAAOkC,IAAAA,OAAmB,QAAQpF,KAAKoB,O,EAEzC+B,KAAA,WACE,OAAOiC,IAAAA,OAA0B,QAAQpF,KAAKoB,O,EAX7B+D,CAAoBC,KCLzC,MAAM,EAA+B5G,OAAO6B,KAAKC,OAAO,0B,ICGnC+E,EAAAA,SAAAA,G,oFACnBlE,OAAA,SAAOD,GACL,YAAMC,OAAN,UAAaD,GAEbQ,IAAI4D,QAAQC,KAAK,eACjB7D,IAAIuB,YAAY+B,OAChB5D,KAAKoE,UAAY,oB,EAGnBvD,KAAA,WACE,OACE,SAAKU,UAAU,mBACb,EAAC3B,EAAD,CAAiBK,MAAOK,IAAIuB,gB,EAZfoC,C,MAAwBI,ICK7C/D,IAAAA,aAAAA,IAAqB,sBAAsB,WACzCA,IAAAA,MAAAA,OAAAA,YAA+ByD,EAC/BzD,IAAAA,OAAAA,YAAyB,CAAEgE,KAAM,eAAgBlB,UAAWa,GAC5D3D,IAAAA,YAAkB,IAAIqD,EAAqBrD,MCL3CiE,EAAAA,EAAAA,QAAOC,IAAAA,UAA2B,SAAS,SAAUC,GAC/CnE,IAAAA,QAAAA,MAIDA,IAAAA,QAAAA,KAAAA,cAA+BoE,mBAChCD,EAAME,IAAI,cAAe,EAAChC,EAAD,CAAqB1C,MAAOK,IAAAA,cAAqB,QCL9EiE,EAAAA,EAAAA,QAAOK,IAAAA,UAAwB,iBAAiB,SAAUH,GACxDA,EAAME,IACJ,cACAE,IAAAA,UACE,CACEhC,MAAOvC,IAAIwC,WAAWC,MAAM,yCAC5BxB,UAAW,wBAEbvB,KAAK8E,mBAAmBC,eAK9BH,IAAAA,UAAAA,iBAA6C,WAAY,WACjDH,EAAQ,IAAIO,KAqBlB,OAnBAP,EAAME,IACJ,qBACAM,IAAAA,UACE,CACEhF,MAAOD,KAAK+B,KAAKmD,cAAcR,kBAC/BS,SAAU,SAACnG,GACT,EAAKoG,0BAA2B,EAEhC,EAAKrD,KAAKsD,gBAAgB,CAAEX,kBAAmB1F,IAAS0B,MAAK,WAC3D,EAAK0E,0BAA2B,EAChCzE,EAAEC,aAGN4B,QAASxC,KAAKoF,0BAEhB9E,IAAIwC,WAAWC,MAAM,kDAIlB0B,O", "sources": ["webpack://@wusong8899/flarum-daily-check-in/external assign \"flarum.extensions['wusong8899-decoration-store']\"", "webpack://@wusong8899/flarum-daily-check-in/webpack/bootstrap", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/compat get default export", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/define property getters", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/hasOwnProperty shorthand", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/make namespace object", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['extend']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['forum/app']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/extend']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['forum/components/HeaderSecondary']\"", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "webpack://@wusong8899/flarum-daily-check-in/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/components/NotificationsDropdown']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/Component']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/components/LoadingIndicator']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/components/Button']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/components/Tooltip']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/helpers/humanTime']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/helpers/username']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/helpers/avatar']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/components/Link']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/helpers/icon']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/ViewHistoryList.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/ViewHistoryDropdown.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/components/FieldSet']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/components/SettingsPage']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/components/Switch']\"", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/utils/ItemList']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/ViewHistoryListState.js", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/Model']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/models/ViewHistory.ts", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['common/components/Page']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/components/ViewHistoryPage.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/index.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/addHeaderMenu.js", "webpack://@wusong8899/flarum-daily-check-in/./src/forum/addPreferences.js"], "sourcesContent": ["module.exports = flarum.extensions['wusong8899-decoration-store'];", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['extend'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['forum/app'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/extend'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['forum/components/HeaderSecondary'];", "export default function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}", "import setPrototypeOf from \"./setPrototypeOf.js\";\nexport default function _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  setPrototypeOf(subClass, superClass);\n}", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/NotificationsDropdown'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/Component'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/LoadingIndicator'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/Button'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/Tooltip'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/helpers/humanTime'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/helpers/username'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/helpers/avatar'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/Link'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/helpers/icon'];", "import app from 'flarum/forum/app';\nimport Component from 'flarum/common/Component';\nimport LoadingIndicator from 'flarum/common/components/LoadingIndicator';\nimport Button from 'flarum/common/components/Button';\nimport Tooltip from 'flarum/common/components/Tooltip';\nimport humanTime from 'flarum/common/helpers/humanTime';\nimport username from 'flarum/common/helpers/username';\nimport avatar from 'flarum/common/helpers/avatar';\nimport Link from 'flarum/common/components/Link';\nimport icon from 'flarum/common/helpers/icon';\n\nexport default class ViewHistoryList extends Component {\n  oncreate(vnode){\n    super.oninit(vnode);\n    this.state = this.attrs.state;\n  }\n\n  deleteAll(e) {\n    if (!confirm(app.translator.trans('wusong8899-view-history.forum.view-history-delete-all-confirmation'))){ return; }\n\n    let viewHistoryList = app.store.all('viewHistory');\n\n    if(viewHistoryList.length>0){\n      app.request({\n          method: 'DELETE',\n          url: app.forum.attribute('apiUrl') + '/viewHistory/deleteAll',\n        })\n        .then(() => {\n          app.store.data.viewHistory = [];\n          m.redraw();\n        });\n    }\n  }\n\n  view(){\n    if(!this.state){ return; }\n\n    let viewHistoryList = app.store.all('viewHistory');\n    viewHistoryList.sort((a, b) => new Date(b.assignedAt()) - new Date(a.assignedAt()));\n\n    let avatarWithFrame,usernameWithColor;\n    if('wusong8899-decoration-store' in flarum.extensions){\n      const { components } = require('@wusong8899-decoration-store');\n      avatarWithFrame = components.avatarWithFrame;\n      usernameWithColor = components.usernameWithColor;\n    }\n\n    return (\n      <div className=\"NotificationList\">\n        <div className=\"NotificationList-header\">\n          <h4 className=\"App-titleControl App-titleControl--text\">{app.translator.trans('wusong8899-view-history.forum.view-history')}</h4>\n          <div class=\"App-primaryControl\">\n            <Button\n                data-container=\"body\"\n                icon=\"fas fa-trash-alt\"\n                className=\"Button Button--link Button--icon Alert-dismiss\"\n                onclick={this.deleteAll.bind(this)}\n              />\n          </div>\n        </div>\n        <div className=\"NotificationList-content\">\n          <ul className=\"NotificationGroup-content\">\n            {viewHistoryList.length ? (\n              viewHistoryList.map((viewHistory) => {\n                const post = viewHistory.post();\n                const user = post.user();\n                \n                return (\n                  <li>\n                    <Link\n                      href={app.route.post(post)}\n                      className=\"Notification\"\n                      onclick={(e) => {\n                        e.redraw = false;\n                      }}\n                    >\n                      {avatarWithFrame?avatarWithFrame(user):avatar(user)}\n                      {icon('fas', { className: 'Notification-icon' })}\n                      <span className=\"Notification-content\">\n                        {app.translator.trans('flarum-flags.forum.flagged_posts.item_text', {\n                          username: usernameWithColor?usernameWithColor(post.user()):username(post.user()),\n                          em: <em />,\n                          discussion: post.discussion().title(),\n                        })}\n                      </span>\n                      {humanTime(viewHistory.assignedAt())}\n                      <div className=\"Notification-excerpt\">{post.contentPlain()}</div>\n                    </Link>\n                  </li>\n                );\n              })\n            ) : !this.state.loading ? (\n              <div className=\"NotificationList-empty\">{app.translator.trans('wusong8899-view-history.forum.view-history-empty')}</div>\n            ) : (\n              LoadingIndicator.component({ className: 'LoadingIndicator--block' })\n            )}\n          </ul>\n        </div>\n      </div>\n    );\n  }\n}\n", "import NotificationsDropdown from 'flarum/common/components/NotificationsDropdown';\nimport ViewHistoryList from './ViewHistoryList';\n\nexport default class ViewHistoryDropdown extends NotificationsDropdown {\n  static initAttrs(attrs) {\n    attrs.label = app.translator.trans('wusong8899-view-history.forum.view-history');\n    attrs.icon = 'fas fa-history';\n    super.initAttrs(attrs);\n  }\n\n  getMenu() {\n    return (\n      <div className={'Dropdown-menu ' + this.attrs.menuClassName} onclick={this.menuClick.bind(this)}>\n        {this.showing ? ViewHistoryList.component({ state: this.attrs.state }) : ''}\n      </div>\n    );\n  }\n\n  goToRoute() {\n    m.route.set(app.route('viewHistory'));\n  }\n\n  getUnreadCount() {\n    return 0;\n  }\n\n  getNewCount() {\n    return this.getUnreadCount();\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/FieldSet'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/SettingsPage'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/Switch'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/utils/ItemList'];", "export default class ViewHistoryListState {\n  constructor(app) {\n    this.app = app;\n    this.loading = false;\n  }\n\n  load() {\n    this.loading = true;\n    m.redraw();\n\n    this.app.store\n      .find('viewHistory')\n      .then((viewHistory) => {\n        this.loading = false;\n        m.redraw();\n      })\n      .catch(() => {});\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/Model'];", "import Model from 'flarum/common/Model';\nimport type Discussion from 'flarum/common/models/Discussion';\nimport type User from 'flarum/common/models/User';\nimport type Post from 'flarum/common/models/Post';\n\nexport default class ViewHistory extends Model {\n  assignedAt() {\n    return Model.attribute<string>('assigned_at').call(this);\n  }\n  discussion() {\n    return Model.hasOne<Discussion>('discussion').call(this);\n  }\n  post() {\n    return Model.hasOne<Post>('post').call(this);\n  }\n  user() {\n    return Model.hasOne<User | null>('user').call(this);\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/components/Page'];", "import Page from 'flarum/common/components/Page';\nimport ViewHistoryList from './ViewHistoryList';\n\nexport default class ViewHistoryPage extends Page {\n  oninit(vnode) {\n    super.oninit(vnode);\n\n    app.history.push('viewHistory');\n    app.viewHistory.load();\n    this.bodyClass = 'App--viewHistory';\n  }\n\n  view() {\n    return (\n      <div className=\"ViewHistoryPage\">\n        <ViewHistoryList state={app.viewHistory}></ViewHistoryList>\n      </div>\n    );\n  }\n}\n", "import { extend } from 'flarum/extend';\r\nimport app from 'flarum/forum/app';\r\nimport addHeaderMenu from './addHeaderMenu';\r\nimport addPreferences from './addPreferences';\r\nimport ViewHistoryListState from './ViewHistoryListState';\r\nimport ViewHistory from './models/ViewHistory';\r\nimport ViewHistoryPage from './components/ViewHistoryPage';\r\n\r\napp.initializers.add('wusong8899-view-history', () => {\r\n  app.store.models.viewHistory = ViewHistory;\r\n  app.routes.viewHistory = { path: '/viewHistory', component: ViewHistoryPage };\r\n  app.viewHistory = new ViewHistoryListState(app);\r\n  addHeaderMenu();\r\n  addPreferences();\r\n});", "import app from 'flarum/forum/app';\nimport { extend } from 'flarum/common/extend';\nimport HeaderSecondary from 'flarum/forum/components/HeaderSecondary';\nimport ViewHistoryDropdown from './components/ViewHistoryDropdown';\n\nexport default function () {\n  extend(HeaderSecondary.prototype, 'items', function (items) {\n    if(!app.session.user){\n      return;\n    }\n\n    if(app.session.user.preferences().viewHistoryEnable){\n      items.add('viewHistory', <ViewHistoryDropdown state={app.viewHistory} />, 20);\n    }\n  });\n}\n", "import { extend, override } from 'flarum/common/extend';\nimport FieldSet from 'flarum/common/components/FieldSet';\nimport SettingsPage from 'flarum/common/components/SettingsPage';\nimport Switch from 'flarum/common/components/Switch';\nimport ItemList from 'flarum/common/utils/ItemList';\n\nexport default function () {\n  extend(SettingsPage.prototype, 'settingsItems', function (items) {\n    items.add(\n      'viewHistory',\n      FieldSet.component(\n        {\n          label: app.translator.trans('wusong8899-view-history.forum.view-history'),\n          className: 'Settings-viewHistory',\n        },\n        this.viewHistoryItems().toArray()\n      )\n    );\n  });\n\n  SettingsPage.prototype['viewHistoryItems'] = function () {\n    const items = new ItemList();\n\n    items.add(\n      'viewHistory-enable',\n      Switch.component(\n        {\n          state: this.user.preferences().viewHistoryEnable,\n          onchange: (value) => {\n            this.viewHistoryEnableLoading = true;\n\n            this.user.savePreferences({ viewHistoryEnable: value }).then(() => {\n              this.viewHistoryEnableLoading = false;\n              m.redraw();\n            });\n          },\n          loading: this.viewHistoryEnableLoading,\n        },\n        app.translator.trans('wusong8899-view-history.forum.view-history-enable')\n      )\n    );\n\n    return items;\n  };\n}\n"], "names": ["module", "exports", "flarum", "extensions", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "__webpack_modules__", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "r", "Symbol", "toStringTag", "value", "core", "compat", "_setPrototypeOf", "p", "setPrototypeOf", "__proto__", "_inherits<PERSON><PERSON>e", "subClass", "superClass", "create", "constructor", "ViewHistoryList", "oncreate", "vnode", "oninit", "this", "state", "attrs", "deleteAll", "e", "confirm", "app", "length", "method", "url", "then", "m", "redraw", "view", "avatar<PERSON><PERSON><PERSON><PERSON><PERSON>", "usernameWithColor", "viewHistoryList", "sort", "b", "Date", "assignedAt", "components", "require", "className", "class", "icon", "onclick", "bind", "map", "viewHistory", "post", "user", "href", "avatar", "username", "em", "discussion", "title", "humanTime", "contentPlain", "loading", "LoadingIndicator", "Component", "ViewHistoryDropdown", "initAttrs", "label", "translator", "trans", "getMenu", "menuClassName", "menuClick", "showing", "component", "goToRoute", "route", "set", "getUnreadCount", "getNewCount", "NotificationsDropdown", "ViewHistoryListState", "load", "store", "find", "ViewHistory", "Model", "ViewHistoryPage", "history", "push", "bodyClass", "Page", "path", "extend", "HeaderSecondary", "items", "viewHistoryEnable", "add", "SettingsPage", "FieldSet", "viewHistoryItems", "toArray", "ItemList", "Switch", "preferences", "onchange", "viewHistoryEnableLoading", "savePreferences"], "sourceRoot": ""}