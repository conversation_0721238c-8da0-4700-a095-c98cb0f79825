{"version": 3, "file": "forum.js", "sources": ["../src/forum/components/BiddingRankContentDetailsModal.jsx", "../src/forum/components/BiddingRankContentSubmitSuccessModal.jsx", "../src/forum/components/BiddingRankContentEditSuccessModal.jsx", "../src/forum/components/BiddingRankContentSubmitModal.jsx", "../src/forum/components/BiddingRankContentListItem.jsx", "../src/forum/components/BiddingRankContentIndexPage.jsx", "../src/forum/model/BiddingRank.js", "../src/forum/index.jsx"], "sourcesContent": ["import app from 'flarum/forum/app';\nimport Modal from 'flarum/components/Modal';\nimport avatar from \"flarum/helpers/avatar\";\n\nexport default class BiddingRankContentDetailsModal extends Modal {\n  static isDismissibleViaBackdropClick = false;\n  static isDismissibleViaCloseButton = true;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.biddingRankListItem = this.attrs.biddingRankListItem;\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    return app.translator.trans('wusong8899-bidding-rank.forum.bidding-rank-details-title');\n  }\n\n  content() {\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n    const bidValue = this.biddingRankListItem.attribute(\"bid\");\n    const bidUser = this.biddingRankListItem.fromUser();\n    const bidURL = this.biddingRankListItem.url();\n    const bidContent = this.biddingRankListItem.content();\n    const _bidText = moneyName.replace('[money]', bidValue);\n\n    // Use standard Flarum helpers as fallbacks for decoration-store components\n    const userAvatar = avatar(bidUser);\n\n    return (\n      <div className=\"Modal-body\">\n        <div style=\"min-height: 50px;\" onclick={() => this.openURL(bidURL) }>\n          <div style=\"float:left\">{userAvatar}</div>\n          <div style=\"padding-left: 70px;\">\n            <div>{bidContent}</div>\n            <div style=\"padding-top: 10px;\">{bidURL}</div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  openURL(bidURL) {\n    window.open(bidURL, '_blank').focus();\n  }\n}\n", "import app from 'flarum/forum/app';\nimport Modal from 'flarum/components/Modal';\nimport Button from 'flarum/components/Button';\n\nexport default class BiddingRankContentSubmitSuccessModal extends Modal {\n  static isDismissibleViaBackdropClick = false;\n  static isDismissibleViaCloseButton = true;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    return app.translator.trans('wusong8899-bidding-rank.forum.submit-content-success');\n  }\n\n  content() {\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form\">\n          <div className=\"Form-group\" style=\"text-align: center;\">\n            {Button.component(\n              {\n                className: 'Button Button--primary',\n                loading: this.loading,\n                onclick: () => {\n                  location.reload();\n                },\n              },\n              app.translator.trans('wusong8899-bidding-rank.forum.ok')\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  }\n}\n", "import app from 'flarum/forum/app';\nimport Modal from 'flarum/components/Modal';\nimport Button from 'flarum/components/Button';\n\nexport default class BiddingRankContentEditSuccessModal extends Modal {\n  static isDismissibleViaBackdropClick = false;\n  static isDismissibleViaCloseButton = true;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    return app.translator.trans('wusong8899-bidding-rank.forum.edit-content-success');\n  }\n\n  content() {\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form\">\n          <div className=\"Form-group\" style=\"text-align: center;\">\n            {Button.component(\n              {\n                className: 'Button Button--primary',\n                loading: this.loading,\n                onclick: () => {\n                  location.reload();\n                },\n              },\n              app.translator.trans('wusong8899-bidding-rank.forum.ok')\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  }\n}\n", "import app from 'flarum/forum/app';\nimport Modal from 'flarum/components/Modal';\nimport Stream from 'flarum/utils/Stream';\nimport Button from 'flarum/components/Button';\nimport Alert from 'flarum/common/components/Alert';\nimport BiddingRankContentSubmitSuccessModal from './BiddingRankContentSubmitSuccessModal';\nimport BiddingRankContentEditSuccessModal from './BiddingRankContentEditSuccessModal';\n\nexport default class BiddingRankContentSubmitModal extends Modal {\n  static isDismissibleViaBackdropClick = false;\n  static isDismissibleViaCloseButton = true;\n\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.itemData = this.attrs.itemData;\n    this.settingType = \"add\";\n    this.loading = false;\n\n    if(this.itemData){\n      this.settingType = \"edit\";\n      this.bid = Stream(this.itemData.bid());\n      this.url = Stream(this.itemData.url());\n      this.bidContent = Stream(this.itemData.content());\n      this.bidTitle = Stream(this.itemData.title());\n      this.bidRaise = Stream(0);\n    }else{\n      this.bid = Stream(10000);\n      this.url = Stream(\"\");\n      this.bidTitle = Stream(\"\");\n      this.bidContent = Stream(\"\");\n    }\n  }\n\n  className() {\n    return 'Modal--small';\n  }\n\n  title() {\n    return app.translator.trans(this.settingType===\"add\"?'wusong8899-bidding-rank.forum.submit-content':'wusong8899-bidding-rank.forum.edit-content');\n  }\n\n  content() {\n    return (\n      <div className=\"Modal-body\">\n        <div className=\"Form\">\n          <div className=\"Form-group\" style=\"text-align: center;\">\n\n              {this.settingType===\"add\" && (\n                <div>\n                  <div className=\"BiddingRankDataLabel\">{app.translator.trans('wusong8899-bidding-rank.forum.bidding-rank-list-bid')}</div>\n                  <input type=\"number\" min=\"10000\" disabled={this.loading} required className=\"FormControl\" bidi={this.bid} />\n                </div>\n              )}\n\n              {this.settingType===\"edit\" && (\n                <div>\n                  <div className=\"BiddingRankDataLabel\">{app.translator.trans('wusong8899-bidding-rank.forum.bidding-rank-list-bid')}: {this.bid()}</div>\n                  <div className=\"BiddingRankDataLabel\">{app.translator.trans('wusong8899-bidding-rank.forum.bidding-rank-list-bid-raise')}</div>\n                  <input type=\"number\" min=\"0\" disabled={this.loading} required className=\"FormControl\" bidi={this.bidRaise} />\n                </div>\n              )}\n\n              <div className=\"BiddingRankDataLabel\">{app.translator.trans('wusong8899-bidding-rank.forum.bidding-rank-list-title')}</div>\n              <input maxlength=\"6\" disabled={this.loading} required className=\"FormControl\" bidi={this.bidTitle} />\n\n              <div className=\"BiddingRankDataLabel\">{app.translator.trans('wusong8899-bidding-rank.forum.bidding-rank-list-url')}</div>\n              <input maxlength=\"500\" disabled={this.loading} required className=\"FormControl\" bidi={this.url} />\n\n              <div className=\"BiddingRankDataLabel\">{app.translator.trans('wusong8899-bidding-rank.forum.bidding-rank-list-content')}</div>\n              <textarea maxlength=\"500\" disabled={this.loading} required className=\"FormControl\" bidi={this.bidContent} />\n          </div>\n\n          <div className=\"Form-group\" style=\"text-align: center;\">\n            {Button.component(\n              {\n                className: 'Button Button--primary',\n                type: 'submit',\n                loading: this.loading,\n              },\n              app.translator.trans('wusong8899-guaguale.forum.guaguale-purchase-confirm')\n            )}&nbsp;\n            {Button.component(\n              {\n                className: 'Button',\n                loading: this.loading,\n                onclick: () => {\n                  this.hide();\n                }\n              },\n              app.translator.trans('wusong8899-guaguale.forum.guaguale-purchase-cancel')\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n\n  onsubmit(e) {\n    e.preventDefault();\n\n    const userMoney = app.session.user.attribute(\"money\");\n\n    if(this.settingType===\"add\" && userMoney<this.bid()){\n      app.alerts.show(Alert, {type: 'error'}, app.translator.trans('wusong8899-bidding-rank.forum.insufficient-fund'));\n      return;\n    }\n\n    if(this.settingType===\"edit\" && userMoney<this.bidRaise()){\n      app.alerts.show(Alert, {type: 'error'}, app.translator.trans('wusong8899-bidding-rank.forum.insufficient-fund'));\n      return;\n    }\n\n    this.loading = true;\n\n    if(this.settingType===\"edit\"){\n      this.itemData.save({\n        bidRaise:this.bidRaise(),\n        url:this.url(),\n        content:this.bidContent(),\n        title:this.bidTitle()\n      })\n      .then(\n        () => {\n          this.loading = false;\n          app.modal.show(BiddingRankContentEditSuccessModal);\n        }\n      )\n      .catch(() => {\n        this.loading = false;\n      });\n    }else{\n      app.store\n      .createRecord(\"biddingRankList\")\n      .save({\n        bid:this.bid(),\n        url:this.url(),\n        content:this.bidContent(),\n        title:this.bidTitle()\n      })\n      .then(\n        (result) => {\n          app.store.pushPayload(result);\n          app.session.user.data.attributes.money-=this.bid();\n          this.loading = false;\n          app.modal.show(BiddingRankContentSubmitSuccessModal);\n        }\n      )\n      .catch(() => {\n        this.loading = false;\n      });\n    }\n  }\n}\n", "import Component from \"flarum/Component\";\nimport BiddingRankContentDetailsModal from './BiddingRankContentDetailsModal';\nimport BiddingRankContentSubmitModal from './BiddingRankContentSubmitModal';\n\nexport default class BiddingRankContentListItem extends Component {\n  view() {\n    const {biddingRankListItem,rankID} = this.attrs;\n    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';\n    const bidValue = biddingRankListItem.attribute(\"bid\");\n    const bidUser = biddingRankListItem.fromUser();\n    const _bidURL = biddingRankListItem.url();\n    const _bidContent = biddingRankListItem.content();\n    const bidTitle = biddingRankListItem.title();\n    const _bidText = moneyName.replace('[money]', bidValue);\n\n    let userBidText = \"\";\n\n    if(app.session.user){\n      let userID = app.session.user.id();\n\n      if(userID==bidUser.id()){\n        userBidText = app.translator.trans('wusong8899-money-leaderboard.forum.user-own-bid');\n      }\n    }\n\n    let listItemStyle = rankID%2===1?\"background: #1b2132;\":\"\";\n\n    if(userBidText!==\"\"){\n      listItemStyle = \"background: royalblue;\";\n    }\n\n    return (\n      <div className=\"BiddingRankListItemContainer\" style={listItemStyle} onclick={() => this.itemClicked(biddingRankListItem,bidUser.id())}>\n        <div style=\"width: 40% !important;\" className=\"BiddingRankListHeaderContent\">\n          <div>{bidTitle}</div>\n        </div>\n        <div style=\"width: 60% !important;;text-align: right !important;\" className=\"BiddingRankListHeaderMoney\">\n          {bidValue}\n          <i className=\"u-sprites-money u-sprites-currency u-sprites-currency-cny\" style=\"margin-left: 4px;\"></i>\n        </div>\n      </div>\n    );\n  }\n\n  itemClicked(biddingRankListItem,bidUserID){\n    if(app.session.user){\n      let userID = app.session.user.id();\n\n      if(userID==bidUserID){\n        this.editContent(biddingRankListItem);\n      }else{\n        this.showDetails(biddingRankListItem);\n      }\n    }else{\n        this.showDetails(biddingRankListItem);\n    }\n  }\n\n  showDetails(biddingRankListItem){\n    app.modal.show(BiddingRankContentDetailsModal, {biddingRankListItem});\n  }\n\n  editContent(itemData){\n    app.modal.show(BiddingRankContentSubmitModal,{itemData});\n  }\n}\n", "import Page from 'flarum/components/Page';\nimport IndexPage from 'flarum/components/IndexPage';\nimport listItems from 'flarum/common/helpers/listItems';\nimport LoadingIndicator from \"flarum/components/LoadingIndicator\";\nimport Button from 'flarum/components/Button';\n\nimport BiddingRankContentListItem from \"./BiddingRankContentListItem\";\nimport BiddingRankContentSubmitModal from './BiddingRankContentSubmitModal';\nimport LogInModal from \"flarum/components/LogInModal\";\n\nexport default class BiddingRankContentIndexPage extends Page {\n  oninit(vnode) {\n    super.oninit(vnode);\n    this.loading = true;\n    this.moreResults = false;\n    this.biddingRankList = [];\n    this.hasBid = false;\n    this.biddingRankHistoryData = {};\n\n    this.loadResults();\n  }\n\n  view() {\n    let loading;\n    let rankID = 0;\n\n    if(this.loading){\n      loading = LoadingIndicator.component({size: \"large\"});\n    }\n\n    return (\n      <div className=\"MoneyLeaderboardPage\">\n        {IndexPage.prototype.hero()}\n\n        <div className=\"container\">\n          <div className=\"sideNavContainer\">\n            <nav className=\"IndexPage-nav sideNav\">\n              <ul>{listItems(IndexPage.prototype.sidebarItems().toArray())}</ul>\n            </nav>\n\n            <div className=\"BiddingRangContainer\">\n              <div className=\"BiddingRankListTitle selectTitle\" style=\"height:40px;padding-top: 0px!important;;padding-bottom: 0px !important;\">\n                <div class=\"switch-btns\"><div class=\"btns-container\"><button style=\"width: 106px;\" type=\"button\" class=\"u-btn\"><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAC4AAAAuCAYAAABXuSs3AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyBpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBXaW5kb3dzIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjExNzJCMjFGQ0I0QzExRUM5QTI1RDZDNkEyRTk2MDREIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjExNzJCMjIwQ0I0QzExRUM5QTI1RDZDNkEyRTk2MDREIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6MTE3MkIyMURDQjRDMTFFQzlBMjVENkM2QTJFOTYwNEQiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6MTE3MkIyMUVDQjRDMTFFQzlBMjVENkM2QTJFOTYwNEQiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6TWgg5AAAL4UlEQVR42tRZC3QU5RW+M7PPZDfvkJAN2SUJhKACSuEICIQDYhIhWl8Qiu0BSwstCAI5oh49Po5WqVoVimLE1II25ZEiLwEVSgTloQEU5ZEnSUiyCQl573Nm+v0zk2blUIg1HsOcczOzu/PP/93vfv+9959wsizT9XjwdJ0ePwnwTVs2Gd5a8+Twysoy83UDvOlSCx06tGVksPHNHRWlH9/i90vXB/BdH+3hbXEXFzz40PD4M6eeXOpy+fo+8ItNrbRv38ZbZj4w7E72eeKEpqwPt668tc8D37t3j27EMPcfByRERZAok90h6iz6jcsqyit0fRr4oUObR2VNT7qHiCMG3BRMZIs/Na347M4xkiT3TeClZedpyqSobMfAqBCgVr+EvEeNIlNZyQsLm5pahD4J/MCBD+2OgZ33E+m7v5TUGdLG1961d3fuDX0OeHFJObW3HVwwYkRqrPoNAw+CmTr8RI4k2Vhbs/5+p7OhbwEvLNyRePuUhNkcZ8WnYNVkgwrcS2TELIPjv/n1rp0bEvsMcKezkTzuE79LHTrEBoga8CDoG4mkDZftsGaiyaMoobbk7XnnKy/0DeAbN71rnzghcQ5RGD6Zus0N4C24bFUtCOoZlXJm9qHCrYN+duBHj32lcwxofX5I6pB+SgqUAVg2K8Blj44kMC63d7M+cTgXf/bIipxVa/J+HuDV1Y20f9/W+MqyvHfvyBj5K0GwqKAJoLkgRS6Sx0Byqwpc7sBXMANyec6DHfOaih9++623XnEcPnr2/wbOXd6Pi0hhPq+POI4jnueopKSUcte9a4i3WWLt8boh9gRdistVPEQUC9PS0kYPJf4BPMWi5T5SswlLJqfXkXT278TjIw/V8Ez+WK8UAUdgn3xBxZXVyZ8PvSG9tKLKeLy0UijSGaMbli9d7mOQJPxh2HieJ4P+CsCLjn9Ner3BZA3Wh4eG6CMOH/08cv/+3fbkQf1stv7GpKhI72AdV5cYHcVZTCZPkNHsNlgtfuL0KDIuxJ9DK2K+LyBxM+Ay+U7kkv+bPBIwqWBSgXOsNGEJE5KPYFALVPMlos5O8re3URsaSx+nH1pdXRdaVOM0nyk776sJi0gtWzB/UVNLO9fY2u5vHTFsmJ8Rys2be+OiO6bemGlPmuyI7TfAHhqmN1stbszUoYkTK0zuxIw4S/hO7AATnSSLLk0HPuLN2WBzrJr7mPrAmPfYWvIWvUN6gBagHqYmSkAkoklNk13MMcMfjrcgOuG4gOkREjGcPL4wcnnM1NAgX6yvaq48+cXJc53WaU8tf+ylYt1LK7ftOnNq6ehbUl5I583jwMJNeDpbazoAFEmWwKwMQOwaE3ISm4r9BiolthBFEl27EdJ4DElUKw7j3seTBN8kBEBgj0DSkZhM2DM04DLH9BOCOEXBgVjItD9ujsNUobiHJ72/nYytdRTWeS4qtq00amuZZcv4WeOcbKwuPHJgqX3w6jmbN09tzhi3YaFVCWEMvE5CckiGDGyoJUZ10UkMPCsskmoKeWBYbCep8yMSrHNxn1UBL4s8yX6NUjY0RnWC/ZG5YLAcAYvBNUDrHMQZhhEH0JwbuJxfEVf1JXHOMiJPI9W2UMeybf0XZOfkrp+ekfb9xVlfX2/4dMu0F7NGH3skmIHv1MjjQZMhluQQB1iLI8nI4i6pURDdAIgzB0olD/Gm20kIzlCi4/7sdfIVrSE9AAup+CrOhGiBdgAloR/JAjTDA7QUSbw/mHjkTq7jW6SsY0Q11UoWYuUAoL2Pfmj7w31L3l+XlTHxylmlpqaeL9yS8Wr6TUWLw0waeI8mXdbwCWaSLfEkR8Ii4IwllGQ9UoYkAYCoREUIng4GkwH6FfKfeZ2EpBDiEuNUAhTQ0LAPEWx1E9/USHxzLXGecjzjkjqXR5sL5NU0U8fj2+OX3Pfw+nempaddPR3W1NTq9v3jrpVZqcceCTGp+VdJFj7toW7twbogRADMxdhJjLUjzfVHuGMQ7sEkGEeTVP0yic61xCWMIckEWbhYJXWT4HRCApXENaPhgoaVSqLXKgqngUBQKxup44kC2+LsZR+sy5w64dp5nB0Xai/Snrw7n79nyNHHFebdGvAu5r3aZ58mJ7YGovuRlDKSKPFu4kIz0adsIqllD1Kmkbi6C8RVA2wLwPo8KkhddxOpAO46Y72XN1D7YwX2ebMW5+VnZU7qWQHqOqqqa7mduXc/dW/K0aejWd/k0oCKGlgxwLpqTyQsFVJwjIfMLCSfP0lccSnRJbcKzKABFjSG+QCmBZXp4lq+6bHNtvkP5XywKWPqbT2vnN9j3nmJCt7IeGLm4CPPRVvxeJcGWgoAz2nMsR4L2YxYnq7ALahNZA8i/iIodOHGTgz2+9RsxAcw3HWN276r4ptWbE6Y89vleduyMtN+WMm//KioqqMtq+/OmXPTkRcjjJjG272zYZMpXWyIWg0Vq4O9B5+G4ZyOtqEMqQ9Zg3Twzg8HPJCKDxHwu1UnGGgo7WwtX7/k/YTfLHpi/e7MqzDdY+DKgq1vpBPrb92RObhEee2gtN0m7dylU6NWEfNhRwD8l/AvC/4hLQsVuFmP0mkwqQ6wOUWw70faQpVye73yUwUDnh4/671npwekvB/dHcb1i6RPiuPWdnRJwqiFWQyQDtPuIdbrBvyO6EihsHCw24EM4gJQF659fnWAgBAFR9HJSlPd8Mk563sKmrTprnmIIKi6XlfdhHYlWBcAtOsJjP3zsE+7FxkDzrnVM2oM8Y2QSAceZIS3AjwTuqe+2Cy0tRmF+h/S1vb4RQ2P2s51FaPA9GXUCtUOdbNAod2ykd3dna4YLpNwHoPZu0QDfmRtI6e2wJxPRJ8lc70OnEk3KsQXZaWuChoAmtk+ttUPAK3XBrk1B1g7E8zaWnhfD217AV5vUHtbWaAQnWiympTR7b2+AxoQ7k226JU2hM2lbOKVqYrVxajIowu0QXVQdmn5X2sdRKwP1lRSG0uPblXzSJOJkcbQD/725o3fnSnr3a2bDuw6ovwpLLp+plmDuothWzOFbfEy0Arj8M4lKOAVQ+sgYU36wrXGsg2h6AD7HR4K4b0hBl/9HR6vt3elsurNdRTVcnoUu5sxxjpcDpNzH7NcqYHWBRiLjIR+vJPX+m5R2Sww9TDZsLyvwziZVX+jREbBS45Qv5ntbHoVuPNCpWm4udPBmNVzKmj6Nyb+SpOMBphjpleB69ie0QPw6L9FdQeiOQHWkYUktp1rQrC8LL1L5Ahyh1uCzb0rFVskNzDOjKTHOle2MzuFyT9TZeMHWFHbEHNwoh6yOInO7nRzzMXTFbrWxgYvBfl5dIe4r1Pd/flx7Q3W+rQWZR9C8SYx4Y2/vBrZq8CHxtEvQliA2YKrRbI4qDZ5PoMaej2kwpJFfq31wPbYuTMKoh8dmLyiNP7bhNeSckvTs1fv9e9xYT0Yvdq2FUC90LsHxZTJ2gPmYzjfmIpTx4bJvSWVr78rpW8/3zl8bAqcZMXvOAA3qRVcB6abAaCwRjjsGTXnr7FjZ22cOWnSf1dY9uy5nua7Zufv3rV985+3/emhsSEnF4+M9KeGgm2PqDLOtp1cI9p59DG2ECFUDmjLfxTwmtoaai0tStCnYLJzYLsKmQ+L04mwlwSFHT8dM3FV+rMv58clJLuCrvC0MGxiZ8641z9pytS1hZ/s+OeG/WseTCw7uGhMHA0KQsTY/sIPtDpIJsYixfC9JRWrWQga1I+LJhRk91mEFnTsbKBTX4+dt1CY/6/bFr62NS858cqgA4+YSCvdPyO7ecmaz1a5b88dk98ybsWBc1Tua4N89OwtFxyoqbTt3ru/Z8hl7Y3R/7KS01/av5xPle5skgunWUvXLpw+v7zkdJT7GuOuZVV1TbRne0H/52bcvHzrdKqpTCf56UH8+aXLHtf3ZPxVf/R4RXpnZc6E/ClUnjPG9kzRkUMR8o8EfLmxV20bclfbC34/Zt1SG514ZsWK2J6Mu2o/7hclOnH4YLg1iA/rNMaW3zw0+Sf7F3d94yVynj05IDzOUR/vcHh6ZSPRF4//CDAATDWlSJt4fasAAAAASUVORK5CYII=\" style=\"width: 15px;margin-right: 4px;vertical-align: -2px;\" /><div class=\"u-btn-text\">{app.translator.trans(\"wusong8899-bidding-rank.forum.bidding-rank-content\")}</div></button><div id=\"buttonSelectedBackground\" class=\"selected-bg\" style=\"left: 0px; top: 0px; opacity: 1;\"></div></div></div>\n              </div>\n              <div style=\"display: flex;position: fixed;bottom: 50px;z-index: 1;width: 100%;\">\n                {!this.loading && this.hasBid===false && (\n                  <Button style=\"position: absolute;right: 25px;font-weight: normal !important;background: #e6601b !important;\" className={'Button Button--primary'} onclick={() => this.submitContent()}>\n                    {app.translator.trans(\"wusong8899-bidding-rank.forum.submit-content\")}\n                  </Button>\n                )}\n\n                {!this.loading && this.hasBid===true && (\n                  <Button style=\"position: absolute;right: 25px;font-weight: normal !important;background: #e6601b !important;\" className={'Button Button--primary'} onclick={() => this.editContent()}>\n                    {app.translator.trans(\"wusong8899-bidding-rank.forum.edit-content\")}\n                  </Button>\n                )}\n              </div>\n\n              <div class=\"BiddingRankListHeader\">\n                <div class=\"BiddingRankListHeaderContent\" style=\"padding-left: 8px;\">{app.translator.trans(\"wusong8899-bidding-rank.forum.bidding-rank-list-title\")}</div>\n                <div class=\"BiddingRankListHeaderMoney\" style=\"margin-left: -5px;color:white !important\">{app.translator.trans(\"wusong8899-bidding-rank.forum.bidding-rank-list-bid\")}</div>\n              </div>\n\n              <div className=\"BiddingRankList\">\n                {this.biddingRankList.map((biddingRankListItem) => {\n                  rankID++;\n\n                  return (\n                    <div className=\"BiddingRankListItems\">\n                      {BiddingRankContentListItem.component({ biddingRankListItem, rankID })}\n                    </div>\n                  );\n                })}\n              </div>\n\n              {!this.loading && this.biddingRankList.length===0 && (\n                <div>\n                  <div style=\"font-size:1.4em;color: var(--muted-more-color);text-align: center;height: 300px;line-height: 100px;\">{app.translator.trans(\"wusong8899-bidding-rank.forum.list-empty\")}</div>\n                </div>\n              )}\n\n              {!loading && this.hasMoreResults() && (\n                <div style=\"text-align:center;padding:20px\">\n                  <Button className={'Button Button--primary'} disabled={this.loading} loading={this.loading} onclick={() => this.loadMore()}>\n                    {app.translator.trans('wusong8899-money-leaderboard.forum.leaderboard-load-more')}\n                  </Button>\n                </div>\n              )}\n\n              {loading && <div className=\"MoneyLeaderboard-loadMore\">{loading}</div>}\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  submitContent(){\n    if(app.session.user){\n      app.modal.show(BiddingRankContentSubmitModal);\n    }else{\n      app.modal.show(LogInModal);\n    }\n  }\n\n  editContent(){\n    const itemData = this.biddingRankHistoryData;\n    app.modal.show(BiddingRankContentSubmitModal,{itemData});\n  }\n\n  hasMoreResults() {\n    return this.moreResults;\n  }\n\n  loadMore() {\n    this.loading = true;\n    this.loadResults(this.biddingRankList.length);\n  }\n\n  parseResults(results) {\n    this.moreResults = !!results.payload.links && !!results.payload.links.next;\n    [].push.apply(this.biddingRankList, results);\n    this.loading = false;\n    m.redraw();\n\n    return results;\n\n    // return app.store\n    //   .find(\"biddingRankHistory\")\n    //   .catch(() => {})\n    //   .then((result) => {\n    //     this.hasBid = result.length>0;\n        \n    //     if(this.hasBid){\n    //       this.biddingRankHistoryData = result[0];\n    //     }\n\n    //     this.loading = false;\n    //     m.redraw();\n\n    //     return results;\n    //   });\n    \n  }\n\n  loadResults(offset = 0) {\n    const limit = 10;\n    return app.store\n      .find(\"biddingRankList\", {\n        page: {\n          offset,\n          limit\n        },\n      })\n      .catch(() => {})\n      .then(this.parseResults.bind(this));\n  }\n}\n", "import Model from \"flarum/Model\";\n\nexport default class BiddingRank extends Model {}\nObject.assign(BiddingRank.prototype, {\n  id: Model.attribute(\"id\"),\n  title: Model.attribute(\"title\"),\n  content: Model.attribute(\"content\"),\n  user_id: Model.attribute(\"user_id\"),\n  url: Model.attribute(\"url\"),\n  bid: Model.attribute(\"bid\"),\n  fromUser: Model.hasOne(\"fromUser\"),\n});\n", "import app from 'flarum/forum/app';\r\nimport { extend,override } from 'flarum/extend';\r\nimport BiddingRankContentIndexPage from './components/BiddingRankContentIndexPage';\r\nimport BiddingRank from \"./model/BiddingRank\";\r\nimport LoadingIndicator from \"flarum/components/LoadingIndicator\";\r\nimport Button from 'flarum/components/Button';\r\nimport IndexPage from 'flarum/components/IndexPage';\r\nimport listItems from 'flarum/common/helpers/listItems';\r\nimport humanTime from 'flarum/common/helpers/humanTime';\r\nimport textContrastClass from 'flarum/common/helpers/textContrastClass';\r\nimport Link from 'flarum/common/components/Link';\r\nimport classList from 'flarum/common/utils/classList';\r\n\r\nimport BiddingRankContentListItem from \"./components/BiddingRankContentListItem\";\r\nimport BiddingRankContentSubmitModal from './components/BiddingRankContentSubmitModal';\r\nimport LogInModal from \"flarum/components/LogInModal\";\r\n\r\nimport TagsPage from 'flarum/tags/components/TagsPage';\r\nimport sortTags from 'flarum/tags/utils/sortTags';\r\nimport tagIcon from 'flarum/tags/helpers/tagIcon';\r\nimport tagLabel from 'flarum/tags/helpers/tagLabel';\r\n\r\napp.initializers.add('wusong8899-client1-bidding-rank-content', () => {\r\n  app.routes['biddingRank'] = {\r\n    path: '/biddingRank',\r\n    component: BiddingRankContentIndexPage,\r\n  };\r\n\r\n  app.store.models.biddingRankList = BiddingRank;\r\n\r\n  let _this = TagsPage.prototype;\r\n  extend(TagsPage.prototype, 'oninit', function () {\r\n\r\n    _this.biddingRankLoading = true;\r\n    _this.moreResults = false;\r\n    _this.biddingRankList = [];\r\n    _this.hasBid = false;\r\n    _this.biddingRankHistoryData = {};\r\n\r\n    _this.loadResults();\r\n    \r\n  });\r\n\r\n  TagsPage.prototype.loadResults = function(offset = 0){\r\n    const limit = 10;\r\n    return app.store\r\n      .find(\"biddingRankList\", {\r\n        page: {\r\n          offset,\r\n          limit\r\n        },\r\n      })\r\n      .catch(() => {})\r\n      .then(_this.parseResults.bind(_this));\r\n  }\r\n\r\n  TagsPage.prototype.parseResults = function(results){\r\n    _this.moreResults = !!results.payload.links && !!results.payload.links.next;\r\n    [].push.apply(_this.biddingRankList, results);\r\n    _this.biddingRankLoading = false;\r\n    m.redraw();\r\n\r\n    return results;\r\n  }\r\n\r\n  TagsPage.prototype.submitContent = function(){\r\n    if(app.session.user){\r\n      app.modal.show(BiddingRankContentSubmitModal);\r\n    }else{\r\n      app.modal.show(LogInModal);\r\n    }\r\n  }\r\n\r\n  TagsPage.prototype.editContent = function(){\r\n    const itemData = _this.biddingRankHistoryData;\r\n    app.modal.show(BiddingRankContentSubmitModal,{itemData});\r\n  }\r\n\r\n  TagsPage.prototype.hasMoreResults = function() {\r\n    return _this.moreResults;\r\n  }\r\n\r\n  TagsPage.prototype.loadMore = function() {\r\n    _this.biddingRankLoading = true;\r\n    _this.loadResults(_this.biddingRankList.length);\r\n  }\r\n\r\n  override(TagsPage.prototype, 'view', function () {\r\n    let rankID = 0;\r\n    let biddingRankLoading;\r\n\r\n    if(_this.biddingRankLoading){\r\n      biddingRankLoading = LoadingIndicator.component({size: \"large\"});\r\n    }\r\n\r\n    if (this.loading) {\r\n      return <LoadingIndicator />;\r\n    }\r\n\r\n    const pinned = this.tags.filter((tag) => tag.position() !== null);\r\n    const cloud = this.tags.filter((tag) => tag.position() === null);\r\n\r\n    return (\r\n      <div className=\"TagsPage\">\r\n        {IndexPage.prototype.hero()}\r\n        <div className=\"container\">\r\n          <nav className=\"TagsPage-nav IndexPage-nav sideNav\">\r\n            <ul>{listItems(IndexPage.prototype.sidebarItems().toArray())}</ul>\r\n          </nav>\r\n\r\n          <div className=\"TagsPage-content sideNavOffset\">\r\n            <ul className=\"TagTiles\">\r\n              {pinned.map((tag) => {\r\n                const lastPostedDiscussion = tag.lastPostedDiscussion();\r\n                const children = sortTags(tag.children() || []);\r\n\r\n\r\n                return (\r\n                  <li className={classList('TagTile', { colored: tag.color() }, textContrastClass(tag.color()))}>\r\n                    <Link className=\"TagTile-info\" href={app.route.tag(tag)}>\r\n                      {tag.icon() && tagIcon(tag, {}, { useColor: false })}\r\n                      <h3 className=\"TagTile-name\">{tag.name()}</h3>\r\n                      <p className=\"TagTile-description\">{tag.description()}</p>\r\n                      {children ? (\r\n                        <div className=\"TagTile-children\">\r\n                          {children.map((child) => [<Link href={app.route.tag(child)}>{child.name()}</Link>, ' '])}\r\n                        </div>\r\n                      ) : (\r\n                        ''\r\n                      )}\r\n                    </Link>\r\n                    {lastPostedDiscussion ? (\r\n                      <Link\r\n                        className=\"TagTile-lastPostedDiscussion\"\r\n                        href={app.route.discussion(lastPostedDiscussion, lastPostedDiscussion.lastPostNumber())}\r\n                      >\r\n                        <span className=\"TagTile-lastPostedDiscussion-title\">{lastPostedDiscussion.title()}</span>\r\n                        {humanTime(lastPostedDiscussion.lastPostedAt())}\r\n                      </Link>\r\n                    ) : (\r\n                      <span className=\"TagTile-lastPostedDiscussion\" />\r\n                    )}\r\n                  </li>\r\n                );\r\n              })}\r\n            </ul>\r\n\r\n            {cloud.length ? <div className=\"TagCloud\">{cloud.map((tag) => [tagLabel(tag, { link: true }), ' '])}</div> : ''}\r\n          </div>\r\n\r\n\r\n          <div className=\"BiddingRangContainer\">\r\n            <div className=\"BiddingRankListTitle selectTitle\" style=\"height:40px;padding-top: 0px!important;;padding-bottom: 0px !important;\">\r\n              <div class=\"switch-btns\"><div class=\"btns-container\"><button style=\"width: 106px;\" type=\"button\" class=\"u-btn\"><img src=\"data:image/png;base64,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\" style=\"width: 15px;margin-right: 4px;vertical-align: -2px;\" /><div class=\"u-btn-text\">{app.translator.trans(\"wusong8899-bidding-rank.forum.bidding-rank-content\")}</div></button><div id=\"buttonSelectedBackground\" class=\"selected-bg\" style=\"left: 0px; top: 0px; opacity: 1;\"></div></div></div>\r\n            </div>\r\n            <div style=\"bottom: 50px;z-index: 1;width: 100%;height:10px;position: absolute;top: 13px;right: -25px;\">\r\n              {!_this.biddingRankLoading && _this.hasBid===false && (\r\n                <Button style=\"position: absolute;right: 25px;font-weight: normal !important;background: #e6601b !important;\" className={'Button Button--primary'} onclick={() => this.submitContent()}>\r\n                  {app.translator.trans(\"wusong8899-bidding-rank.forum.submit-content\")}\r\n                </Button>\r\n              )}\r\n\r\n              {!_this.biddingRankLoading && _this.hasBid===true && (\r\n                <Button style=\"position: absolute;right: 25px;font-weight: normal !important;background: #e6601b !important;\" className={'Button Button--primary'} onclick={() => this.editContent()}>\r\n                  {app.translator.trans(\"wusong8899-bidding-rank.forum.edit-content\")}\r\n                </Button>\r\n              )}\r\n            </div>\r\n\r\n            <div class=\"BiddingRankListHeader\">\r\n              <div class=\"BiddingRankListHeaderContent\" style=\"padding-left: 8px;\">{app.translator.trans(\"wusong8899-bidding-rank.forum.bidding-rank-list-title\")}</div>\r\n              <div class=\"BiddingRankListHeaderMoney\" style=\"margin-left: -5px;color:white !important\">{app.translator.trans(\"wusong8899-bidding-rank.forum.bidding-rank-list-bid\")}</div>\r\n            </div>\r\n\r\n            <div className=\"BiddingRankList\">\r\n              {_this.biddingRankList.map((biddingRankListItem) => {\r\n                rankID++;\r\n\r\n                return (\r\n                  <div className=\"BiddingRankListItems\">\r\n                    {BiddingRankContentListItem.component({ biddingRankListItem, rankID })}\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n\r\n            {!_this.biddingRankLoading && _this.biddingRankList.length===0 && (\r\n              <div>\r\n                <div style=\"font-size:1.4em;color: var(--muted-more-color);text-align: center;height: 300px;line-height: 100px;\">{app.translator.trans(\"wusong8899-bidding-rank.forum.list-empty\")}</div>\r\n              </div>\r\n            )}\r\n\r\n            {_this.hasMoreResults() && (\r\n              <div style=\"text-align:center;padding:20px\">\r\n                <Button className={'Button Button--primary'} disabled={_this.biddingRankLoading} loading={_this.biddingRankLoading} onclick={() => _this.loadMore()}>\r\n                  {app.translator.trans('wusong8899-money-leaderboard.forum.leaderboard-load-more')}\r\n                </Button>\r\n              </div>\r\n            )}\r\n\r\n            {biddingRankLoading && <div className=\"MoneyLeaderboard-loadMore\">{biddingRankLoading}</div>}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n\r\n  });\r\n\r\n  // addSidebarMenu();\r\n});"], "names": ["BiddingRankContentDetailsModal", "Modal", "vnode", "app", "moneyName", "bidValue", "bidUser", "bidURL", "bidContent", "userAvatar", "avatar", "BiddingRankContentSubmitSuccessModal", "<PERSON><PERSON>", "BiddingRankContentEditSuccessModal", "BiddingRankContentSubmitModal", "Stream", "e", "userMoney", "<PERSON><PERSON>", "result", "BiddingRankContentListItem", "Component", "biddingRankListItem", "rankID", "bidTitle", "userBidText", "listItemStyle", "bidUserID", "itemData", "BiddingRankContentIndexPage", "Page", "loading", "LoadingIndicator", "IndexPage", "listItems", "LogInModal", "results", "offset", "BiddingRank", "Model", "_this", "TagsPage", "extend", "override", "biddingRankLoading", "pinned", "tag", "cloud", "lastPostedDiscussion", "children", "sortTags", "classList", "textContrastClass", "Link", "tagIcon", "child", "humanTime", "tagLabel"], "mappings": "oEAIA,MAAqBA,UAAuCC,CAAM,CAChE,OAAO,8BAAgC,GACvC,OAAO,4BAA8B,GAErC,OAAOC,EAAO,CACZ,MAAM,OAAOA,CAAK,EAClB,KAAK,oBAAsB,KAAK,MAAM,mBACxC,CAEA,WAAY,CACV,MAAO,cACT,CAEA,OAAQ,CACN,OAAOC,EAAI,WAAW,MAAM,0DAA0D,CACxF,CAEA,SAAU,CACR,MAAMC,EAAYD,EAAI,MAAM,UAAU,2BAA2B,GAAK,UAChEE,EAAW,KAAK,oBAAoB,UAAU,KAAK,EACnDC,EAAU,KAAK,oBAAoB,SAAA,EACnCC,EAAS,KAAK,oBAAoB,IAAA,EAClCC,EAAa,KAAK,oBAAoB,QAAA,EAC3BJ,EAAU,QAAQ,UAAWC,CAAQ,EAGtD,MAAMI,EAAaC,EAAOJ,CAAO,EAEjC,OACE,EAAC,MAAA,CAAI,UAAU,YAAA,IACZ,MAAA,CAAI,MAAM,oBAAoB,QAAS,IAAM,KAAK,QAAQC,CAAM,CAAA,IAC9D,MAAA,CAAI,MAAM,YAAA,EAAcE,CAAW,EACpC,EAAC,MAAA,CAAI,MAAM,qBAAA,IACR,MAAA,KAAKD,CAAW,EACjB,EAAC,OAAI,MAAM,oBAAA,EAAsBD,CAAO,CAC1C,CACF,CACF,CAEJ,CAEA,QAAQA,EAAQ,CACd,OAAO,KAAKA,EAAQ,QAAQ,EAAE,MAAA,CAChC,CACF,CC5CA,MAAqBI,UAA6CV,CAAM,CACtE,OAAO,8BAAgC,GACvC,OAAO,4BAA8B,GAErC,OAAOC,EAAO,CACZ,MAAM,OAAOA,CAAK,CACpB,CAEA,WAAY,CACV,MAAO,cACT,CAEA,OAAQ,CACN,OAAOC,EAAI,WAAW,MAAM,sDAAsD,CACpF,CAEA,SAAU,CACR,OACE,EAAC,MAAA,CAAI,UAAU,YAAA,IACZ,MAAA,CAAI,UAAU,MAAA,EACb,EAAC,MAAA,CAAI,UAAU,aAAa,MAAM,uBAC/BS,EAAO,UACN,CACE,UAAW,yBACX,QAAS,KAAK,QACd,QAAS,IAAM,CACb,SAAS,OAAA,CACX,CAAA,EAEFT,EAAI,WAAW,MAAM,kCAAkC,CAAA,CAE3D,CACF,CACF,CAEJ,CACF,CCpCA,MAAqBU,UAA2CZ,CAAM,CACpE,OAAO,8BAAgC,GACvC,OAAO,4BAA8B,GAErC,OAAOC,EAAO,CACZ,MAAM,OAAOA,CAAK,CACpB,CAEA,WAAY,CACV,MAAO,cACT,CAEA,OAAQ,CACN,OAAOC,EAAI,WAAW,MAAM,oDAAoD,CAClF,CAEA,SAAU,CACR,OACE,EAAC,MAAA,CAAI,UAAU,YAAA,IACZ,MAAA,CAAI,UAAU,MAAA,EACb,EAAC,MAAA,CAAI,UAAU,aAAa,MAAM,uBAC/BS,EAAO,UACN,CACE,UAAW,yBACX,QAAS,KAAK,QACd,QAAS,IAAM,CACb,SAAS,OAAA,CACX,CAAA,EAEFT,EAAI,WAAW,MAAM,kCAAkC,CAAA,CAE3D,CACF,CACF,CAEJ,CACF,CChCA,MAAqBW,UAAsCb,CAAM,CAC/D,OAAO,8BAAgC,GACvC,OAAO,4BAA8B,GAErC,OAAOC,EAAO,CACZ,MAAM,OAAOA,CAAK,EAClB,KAAK,SAAW,KAAK,MAAM,SAC3B,KAAK,YAAc,MACnB,KAAK,QAAU,GAEZ,KAAK,UACN,KAAK,YAAc,OACnB,KAAK,IAAMa,EAAO,KAAK,SAAS,KAAK,EACrC,KAAK,IAAMA,EAAO,KAAK,SAAS,KAAK,EACrC,KAAK,WAAaA,EAAO,KAAK,SAAS,SAAS,EAChD,KAAK,SAAWA,EAAO,KAAK,SAAS,OAAO,EAC5C,KAAK,SAAWA,EAAO,CAAC,IAExB,KAAK,IAAMA,EAAO,GAAK,EACvB,KAAK,IAAMA,EAAO,EAAE,EACpB,KAAK,SAAWA,EAAO,EAAE,EACzB,KAAK,WAAaA,EAAO,EAAE,EAE/B,CAEA,WAAY,CACV,MAAO,cACT,CAEA,OAAQ,CACN,OAAOZ,EAAI,WAAW,MAAM,KAAK,cAAc,MAAM,+CAA+C,4CAA4C,CAClJ,CAEA,SAAU,CACR,SACG,MAAA,CAAI,UAAU,cACb,EAAC,MAAA,CAAI,UAAU,MAAA,EACb,EAAC,MAAA,CAAI,UAAU,aAAa,MAAM,qBAAA,EAE7B,KAAK,cAAc,SACjB,MAAA,KACC,EAAC,MAAA,CAAI,UAAU,wBAAwBA,EAAI,WAAW,MAAM,qDAAqD,CAAE,EACnH,EAAC,QAAA,CAAM,KAAK,SAAS,IAAI,QAAQ,SAAU,KAAK,QAAS,SAAQ,GAAC,UAAU,cAAc,KAAM,KAAK,IAAK,CAC5G,EAGD,KAAK,cAAc,QAClB,EAAC,WACC,EAAC,MAAA,CAAI,UAAU,sBAAA,EAAwBA,EAAI,WAAW,MAAM,qDAAqD,EAAE,KAAG,KAAK,KAAM,EACjI,EAAC,OAAI,UAAU,sBAAA,EAAwBA,EAAI,WAAW,MAAM,2DAA2D,CAAE,IACxH,QAAA,CAAM,KAAK,SAAS,IAAI,IAAI,SAAU,KAAK,QAAS,SAAQ,GAAC,UAAU,cAAc,KAAM,KAAK,QAAA,CAAU,CAC7G,EAGF,EAAC,MAAA,CAAI,UAAU,sBAAA,EAAwBA,EAAI,WAAW,MAAM,uDAAuD,CAAE,IACpH,QAAA,CAAM,UAAU,IAAI,SAAU,KAAK,QAAS,SAAQ,GAAC,UAAU,cAAc,KAAM,KAAK,QAAA,CAAU,EAEnG,EAAC,MAAA,CAAI,UAAU,sBAAA,EAAwBA,EAAI,WAAW,MAAM,qDAAqD,CAAE,EACnH,EAAC,SAAM,UAAU,MAAM,SAAU,KAAK,QAAS,SAAQ,GAAC,UAAU,cAAc,KAAM,KAAK,IAAK,EAEhG,EAAC,MAAA,CAAI,UAAU,wBAAwBA,EAAI,WAAW,MAAM,yDAAyD,CAAE,EACvH,EAAC,WAAA,CAAS,UAAU,MAAM,SAAU,KAAK,QAAS,SAAQ,GAAC,UAAU,cAAc,KAAM,KAAK,UAAA,CAAY,CAC9G,EAEA,EAAC,MAAA,CAAI,UAAU,aAAa,MAAM,uBAC/BS,EAAO,UACN,CACE,UAAW,yBACX,KAAM,SACN,QAAS,KAAK,OAAA,EAEhBT,EAAI,WAAW,MAAM,qDAAqD,CAAA,EAC1E,IACDS,EAAO,UACN,CACE,UAAW,SACX,QAAS,KAAK,QACd,QAAS,IAAM,CACb,KAAK,KAAA,CACP,CAAA,EAEFT,EAAI,WAAW,MAAM,oDAAoD,CAAA,CAE7E,CACF,CACF,CAEJ,CAGA,SAASa,EAAG,CACVA,EAAE,eAAA,EAEF,MAAMC,EAAYd,EAAI,QAAQ,KAAK,UAAU,OAAO,EAEpD,GAAG,KAAK,cAAc,OAASc,EAAU,KAAK,MAAM,CAClDd,EAAI,OAAO,KAAKe,EAAO,CAAC,KAAM,OAAA,EAAUf,EAAI,WAAW,MAAM,iDAAiD,CAAC,EAC/G,MACF,CAEA,GAAG,KAAK,cAAc,QAAUc,EAAU,KAAK,WAAW,CACxDd,EAAI,OAAO,KAAKe,EAAO,CAAC,KAAM,OAAA,EAAUf,EAAI,WAAW,MAAM,iDAAiD,CAAC,EAC/G,MACF,CAEA,KAAK,QAAU,GAEZ,KAAK,cAAc,OACpB,KAAK,SAAS,KAAK,CACjB,SAAS,KAAK,SAAA,EACd,IAAI,KAAK,IAAA,EACT,QAAQ,KAAK,WAAA,EACb,MAAM,KAAK,SAAA,CAAS,CACrB,EACA,KACC,IAAM,CACJ,KAAK,QAAU,GACfA,EAAI,MAAM,KAAKU,CAAkC,CACnD,CAAA,EAED,MAAM,IAAM,CACX,KAAK,QAAU,EACjB,CAAC,EAEDV,EAAI,MACH,aAAa,iBAAiB,EAC9B,KAAK,CACJ,IAAI,KAAK,IAAA,EACT,IAAI,KAAK,IAAA,EACT,QAAQ,KAAK,WAAA,EACb,MAAM,KAAK,SAAA,CAAS,CACrB,EACA,KACEgB,GAAW,CACVhB,EAAI,MAAM,YAAYgB,CAAM,EAC5BhB,EAAI,QAAQ,KAAK,KAAK,WAAW,OAAO,KAAK,IAAA,EAC7C,KAAK,QAAU,GACfA,EAAI,MAAM,KAAKQ,CAAoC,CACrD,CAAA,EAED,MAAM,IAAM,CACX,KAAK,QAAU,EACjB,CAAC,CAEL,CACF,CCrJA,MAAqBS,UAAmCC,CAAU,CAChE,MAAO,CACL,KAAM,CAAC,oBAAAC,EAAoB,OAAAC,CAAA,EAAU,KAAK,MACpCnB,EAAY,IAAI,MAAM,UAAU,2BAA2B,GAAK,UAChEC,EAAWiB,EAAoB,UAAU,KAAK,EAC9ChB,EAAUgB,EAAoB,SAAA,EACpBA,EAAoB,IAAA,EAChBA,EAAoB,QAAA,EACxC,MAAME,EAAWF,EAAoB,MAAA,EACpBlB,EAAU,QAAQ,UAAWC,CAAQ,EAEtD,IAAIoB,EAAc,GAEf,IAAI,QAAQ,MACA,IAAI,QAAQ,KAAK,GAAA,GAEnBnB,EAAQ,OACjBmB,EAAc,IAAI,WAAW,MAAM,iDAAiD,GAIxF,IAAIC,EAAgBH,EAAO,IAAI,EAAE,uBAAuB,GAExD,OAAGE,IAAc,KACfC,EAAgB,4BAIf,MAAA,CAAI,UAAU,+BAA+B,MAAOA,EAAe,QAAS,IAAM,KAAK,YAAYJ,EAAoBhB,EAAQ,GAAA,CAAI,GAClI,EAAC,MAAA,CAAI,MAAM,yBAAyB,UAAU,8BAAA,EAC5C,EAAC,WAAKkB,CAAS,CACjB,EACA,EAAC,MAAA,CAAI,MAAM,uDAAuD,UAAU,8BACzEnB,IACA,IAAA,CAAE,UAAU,4DAA4D,MAAM,mBAAA,CAAoB,CACrG,CACF,CAEJ,CAEA,YAAYiB,EAAoBK,EAAU,CACrC,IAAI,QAAQ,KACA,IAAI,QAAQ,KAAK,GAAA,GAEnBA,EACT,KAAK,YAAYL,CAAmB,EAEpC,KAAK,YAAYA,CAAmB,EAGpC,KAAK,YAAYA,CAAmB,CAE1C,CAEA,YAAYA,EAAoB,CAC9B,IAAI,MAAM,KAAKtB,EAAgC,CAAC,oBAAAsB,EAAoB,CACtE,CAEA,YAAYM,EAAS,CACnB,IAAI,MAAM,KAAKd,EAA8B,CAAC,SAAAc,EAAS,CACzD,CACF,CCvDA,MAAqBC,UAAoCC,CAAK,CAC5D,OAAO5B,EAAO,CACZ,MAAM,OAAOA,CAAK,EAClB,KAAK,QAAU,GACf,KAAK,YAAc,GACnB,KAAK,gBAAkB,CAAA,EACvB,KAAK,OAAS,GACd,KAAK,uBAAyB,CAAA,EAE9B,KAAK,YAAA,CACP,CAEA,MAAO,CACL,IAAI6B,EACAR,EAAS,EAEb,OAAG,KAAK,UACNQ,EAAUC,EAAiB,UAAU,CAAC,KAAM,QAAQ,KAInD,MAAA,CAAI,UAAU,wBACZC,EAAU,UAAU,OAErB,EAAC,MAAA,CAAI,UAAU,aACb,EAAC,MAAA,CAAI,UAAU,kBAAA,EACb,EAAC,OAAI,UAAU,uBAAA,EACb,EAAC,KAAA,KAAIC,EAAUD,EAAU,UAAU,eAAe,QAAA,CAAS,CAAE,CAC/D,EAEA,EAAC,MAAA,CAAI,UAAU,wBACb,EAAC,OAAI,UAAU,mCAAmC,MAAM,2EACtD,EAAC,MAAA,CAAI,MAAM,eAAc,EAAC,MAAA,CAAI,MAAM,gBAAA,EAAiB,EAAC,UAAO,MAAM,gBAAgB,KAAK,SAAS,MAAM,SAAQ,EAAC,OAAI,IAAI,yqKAAyqK,MAAM,sDAAsD,EAAE,EAAC,MAAA,CAAI,MAAM,YAAA,EAAc,IAAI,WAAW,MAAM,oDAAoD,CAAE,CAAM,IAAU,MAAA,CAAI,GAAG,2BAA2B,MAAM,cAAc,MAAM,mCAAmC,CAAM,CAAM,CAChkL,EACA,EAAC,MAAA,CAAI,MAAM,oEAAA,EACR,CAAC,KAAK,SAAW,KAAK,SAAS,IAC9B,EAACrB,EAAA,CAAO,MAAM,gGAAgG,UAAW,yBAA0B,QAAS,IAAM,KAAK,eAAc,EAClL,IAAI,WAAW,MAAM,8CAA8C,CACtE,EAGD,CAAC,KAAK,SAAW,KAAK,SAAS,MAC7BA,EAAA,CAAO,MAAM,gGAAgG,UAAW,yBAA0B,QAAS,IAAM,KAAK,aAAY,EAChL,IAAI,WAAW,MAAM,4CAA4C,CACpE,CAEJ,EAEA,EAAC,MAAA,CAAI,MAAM,uBAAA,EACT,EAAC,MAAA,CAAI,MAAM,+BAA+B,MAAM,oBAAA,EAAsB,IAAI,WAAW,MAAM,uDAAuD,CAAE,EACpJ,EAAC,MAAA,CAAI,MAAM,6BAA6B,MAAM,4CAA4C,IAAI,WAAW,MAAM,qDAAqD,CAAE,CACxK,EAEA,EAAC,OAAI,UAAU,mBACZ,KAAK,gBAAgB,IAAKU,IACzBC,IAGE,EAAC,MAAA,CAAI,UAAU,sBAAA,EACZH,EAA2B,UAAU,CAAE,oBAAAE,EAAqB,OAAAC,CAAA,CAAQ,CACvE,EAEH,CACH,EAEC,CAAC,KAAK,SAAW,KAAK,gBAAgB,SAAS,GAC9C,EAAC,MAAA,KACC,EAAC,MAAA,CAAI,MAAM,qGAAA,EAAuG,IAAI,WAAW,MAAM,0CAA0C,CAAE,CACrL,EAGD,CAACQ,GAAW,KAAK,eAAA,KACf,MAAA,CAAI,MAAM,kCACT,EAACnB,EAAA,CAAO,UAAW,yBAA0B,SAAU,KAAK,QAAS,QAAS,KAAK,QAAS,QAAS,IAAM,KAAK,UAAS,EACtH,IAAI,WAAW,MAAM,0DAA0D,CAClF,CACF,EAGDmB,GAAW,EAAC,MAAA,CAAI,UAAU,2BAAA,EAA6BA,CAAQ,CAClE,CACF,CACF,CACF,CAEJ,CAEA,eAAe,CACV,IAAI,QAAQ,KACb,IAAI,MAAM,KAAKjB,CAA6B,EAE5C,IAAI,MAAM,KAAKqB,CAAU,CAE7B,CAEA,aAAa,CACX,MAAMP,EAAW,KAAK,uBACtB,IAAI,MAAM,KAAKd,EAA8B,CAAC,SAAAc,EAAS,CACzD,CAEA,gBAAiB,CACf,OAAO,KAAK,WACd,CAEA,UAAW,CACT,KAAK,QAAU,GACf,KAAK,YAAY,KAAK,gBAAgB,MAAM,CAC9C,CAEA,aAAaQ,EAAS,CACpB,YAAK,YAAc,CAAC,CAACA,EAAQ,QAAQ,OAAS,CAAC,CAACA,EAAQ,QAAQ,MAAM,KACtE,CAAA,EAAG,KAAK,MAAM,KAAK,gBAAiBA,CAAO,EAC3C,KAAK,QAAU,GACf,EAAE,OAAA,EAEKA,CAkBT,CAEA,YAAYC,EAAS,EAAG,CAEtB,OAAO,IAAI,MACR,KAAK,kBAAmB,CACvB,KAAM,CACJ,OAAAA,EACA,QAAA,CACF,CACD,EACA,MAAM,IAAM,CAAC,CAAC,EACd,KAAK,KAAK,aAAa,KAAK,IAAI,CAAC,CACtC,CACF,CC3Je,MAAMC,UAAoBC,CAAM,CAAA,CAC/C,OAAO,OAAOD,EAAY,UAAW,CACnC,GAAIC,EAAM,UAAU,IAAI,EACxB,MAAOA,EAAM,UAAU,OAAO,EAC9B,QAASA,EAAM,UAAU,SAAS,EAClC,QAASA,EAAM,UAAU,SAAS,EAClC,IAAKA,EAAM,UAAU,KAAK,EAC1B,IAAKA,EAAM,UAAU,KAAK,EAC1B,SAAUA,EAAM,OAAO,UAAU,CACnC,CAAC,ECWDpC,EAAI,aAAa,IAAI,0CAA2C,IAAM,CACpEA,EAAI,OAAO,YAAiB,CAC1B,KAAM,eACN,UAAW0B,CAAA,EAGb1B,EAAI,MAAM,OAAO,gBAAkBmC,EAEnC,IAAIE,EAAQC,EAAS,UACrBC,EAAAA,OAAOD,EAAS,UAAW,SAAU,UAAY,CAE/CD,EAAM,mBAAqB,GAC3BA,EAAM,YAAc,GACpBA,EAAM,gBAAkB,CAAA,EACxBA,EAAM,OAAS,GACfA,EAAM,uBAAyB,CAAA,EAE/BA,EAAM,YAAA,CAER,CAAC,EAEDC,EAAS,UAAU,YAAc,SAASJ,EAAS,EAAE,CAEnD,OAAOlC,EAAI,MACR,KAAK,kBAAmB,CACvB,KAAM,CACJ,OAAAkC,EACA,QAAA,CACF,CACD,EACA,MAAM,IAAM,CAAC,CAAC,EACd,KAAKG,EAAM,aAAa,KAAKA,CAAK,CAAC,CACxC,EAEAC,EAAS,UAAU,aAAe,SAASL,EAAQ,CACjD,OAAAI,EAAM,YAAc,CAAC,CAACJ,EAAQ,QAAQ,OAAS,CAAC,CAACA,EAAQ,QAAQ,MAAM,KACvE,CAAA,EAAG,KAAK,MAAMI,EAAM,gBAAiBJ,CAAO,EAC5CI,EAAM,mBAAqB,GAC3B,EAAE,OAAA,EAEKJ,CACT,EAEAK,EAAS,UAAU,cAAgB,UAAU,CACxCtC,EAAI,QAAQ,KACbA,EAAI,MAAM,KAAKW,CAA6B,EAE5CX,EAAI,MAAM,KAAKgC,CAAU,CAE7B,EAEAM,EAAS,UAAU,YAAc,UAAU,CACzC,MAAMb,EAAWY,EAAM,uBACvBrC,EAAI,MAAM,KAAKW,EAA8B,CAAC,SAAAc,EAAS,CACzD,EAEAa,EAAS,UAAU,eAAiB,UAAW,CAC7C,OAAOD,EAAM,WACf,EAEAC,EAAS,UAAU,SAAW,UAAW,CACvCD,EAAM,mBAAqB,GAC3BA,EAAM,YAAYA,EAAM,gBAAgB,MAAM,CAChD,EAEAG,EAAAA,SAASF,EAAS,UAAW,OAAQ,UAAY,CAC/C,IAAIlB,EAAS,EACTqB,EAMJ,GAJGJ,EAAM,qBACPI,EAAqBZ,EAAiB,UAAU,CAAC,KAAM,QAAQ,GAG7D,KAAK,QACP,SAAQA,EAAA,IAAiB,EAG3B,MAAMa,EAAS,KAAK,KAAK,OAAQC,GAAQA,EAAI,SAAA,IAAe,IAAI,EAC1DC,EAAQ,KAAK,KAAK,OAAQD,GAAQA,EAAI,SAAA,IAAe,IAAI,EAE/D,SACG,MAAA,CAAI,UAAU,UAAA,EACZb,EAAU,UAAU,KAAA,EACrB,EAAC,MAAA,CAAI,UAAU,WAAA,EACb,EAAC,OAAI,UAAU,wCACZ,KAAA,KAAIC,EAAUD,EAAU,UAAU,eAAe,QAAA,CAAS,CAAE,CAC/D,EAEA,EAAC,MAAA,CAAI,UAAU,gCAAA,IACZ,KAAA,CAAG,UAAU,YACXY,EAAO,IAAKC,GAAQ,CACnB,MAAME,EAAuBF,EAAI,qBAAA,EAC3BG,EAAWC,EAASJ,EAAI,SAAA,GAAc,CAAA,CAAE,EAG9C,SACG,KAAA,CAAG,UAAWK,EAAU,UAAW,CAAE,QAASL,EAAI,MAAA,GAAWM,EAAkBN,EAAI,OAAO,CAAC,GAC1F,EAACO,EAAA,CAAK,UAAU,eAAe,KAAMlD,EAAI,MAAM,IAAI2C,CAAG,CAAA,EACnDA,EAAI,KAAA,GAAUQ,EAAQR,EAAK,CAAA,EAAI,CAAE,SAAU,EAAA,CAAO,EACnD,EAAC,KAAA,CAAG,UAAU,cAAA,EAAgBA,EAAI,KAAA,CAAO,EACzC,EAAC,IAAA,CAAE,UAAU,uBAAuBA,EAAI,aAAc,EACrDG,EACC,EAAC,OAAI,UAAU,kBAAA,EACZA,EAAS,IAAKM,GAAU,GAAEF,EAAA,CAAK,KAAMlD,EAAI,MAAM,IAAIoD,CAAK,CAAA,EAAIA,EAAM,KAAA,CAAO,EAAS,GAAG,CAAC,CACzF,EAEA,EAEJ,EACCP,EACC,EAACK,EAAA,CACC,UAAU,+BACV,KAAMlD,EAAI,MAAM,WAAW6C,EAAsBA,EAAqB,gBAAgB,CAAA,IAErF,OAAA,CAAK,UAAU,oCAAA,EAAsCA,EAAqB,OAAQ,EAClFQ,EAAUR,EAAqB,aAAA,CAAc,CAAA,EAGhD,EAAC,OAAA,CAAK,UAAU,+BAA+B,CAEnD,CAEJ,CAAC,CACH,EAECD,EAAM,SAAU,MAAA,CAAI,UAAU,YAAYA,EAAM,IAAKD,GAAQ,CAACW,EAASX,EAAK,CAAE,KAAM,GAAM,EAAG,GAAG,CAAC,CAAE,EAAS,EAC/G,IAGC,MAAA,CAAI,UAAU,sBAAA,EACb,EAAC,OAAI,UAAU,mCAAmC,MAAM,yEAAA,EACtD,EAAC,OAAI,MAAM,eAAc,EAAC,MAAA,CAAI,MAAM,gBAAA,EAAiB,EAAC,UAAO,MAAM,gBAAgB,KAAK,SAAS,MAAM,OAAA,EAAQ,EAAC,OAAI,IAAI,yqKAAyqK,MAAM,qDAAA,CAAsD,IAAG,MAAA,CAAI,MAAM,YAAA,EAAc3C,EAAI,WAAW,MAAM,oDAAoD,CAAE,CAAM,IAAU,MAAA,CAAI,GAAG,2BAA2B,MAAM,cAAc,MAAM,kCAAA,CAAmC,CAAM,CAAM,CAChkL,EACA,EAAC,MAAA,CAAI,MAAM,4FAAA,EACR,CAACqC,EAAM,oBAAsBA,EAAM,SAAS,MAC1C5B,EAAA,CAAO,MAAM,gGAAgG,UAAW,yBAA0B,QAAS,IAAM,KAAK,cAAA,GACpKT,EAAI,WAAW,MAAM,8CAA8C,CACtE,EAGD,CAACqC,EAAM,oBAAsBA,EAAM,SAAS,IAC3C,EAAC5B,EAAA,CAAO,MAAM,gGAAgG,UAAW,yBAA0B,QAAS,IAAM,KAAK,YAAA,GACpKT,EAAI,WAAW,MAAM,4CAA4C,CACpE,CAEJ,IAEC,MAAA,CAAI,MAAM,yBACT,EAAC,MAAA,CAAI,MAAM,+BAA+B,MAAM,sBAAsBA,EAAI,WAAW,MAAM,uDAAuD,CAAE,EACpJ,EAAC,MAAA,CAAI,MAAM,6BAA6B,MAAM,0CAAA,EAA4CA,EAAI,WAAW,MAAM,qDAAqD,CAAE,CACxK,EAEA,EAAC,OAAI,UAAU,iBAAA,EACZqC,EAAM,gBAAgB,IAAKlB,IAC1BC,IAGE,EAAC,MAAA,CAAI,UAAU,sBAAA,EACZH,EAA2B,UAAU,CAAE,oBAAAE,EAAqB,OAAAC,CAAA,CAAQ,CACvE,EAEH,CACH,EAEC,CAACiB,EAAM,oBAAsBA,EAAM,gBAAgB,SAAS,GAC3D,EAAC,MAAA,KACC,EAAC,MAAA,CAAI,MAAM,qGAAA,EAAuGrC,EAAI,WAAW,MAAM,0CAA0C,CAAE,CACrL,EAGDqC,EAAM,eAAA,GACL,EAAC,MAAA,CAAI,MAAM,gCAAA,EACT,EAAC5B,EAAA,CAAO,UAAW,yBAA0B,SAAU4B,EAAM,mBAAoB,QAASA,EAAM,mBAAoB,QAAS,IAAMA,EAAM,SAAA,CAAS,EAC/IrC,EAAI,WAAW,MAAM,0DAA0D,CAClF,CACF,EAGDyC,GAAsB,EAAC,MAAA,CAAI,UAAU,2BAAA,EAA6BA,CAAmB,CACxF,CACF,CACF,CAGJ,CAAC,CAGH,CAAC"}