import app from 'flarum/admin/app';
import Component from 'flarum/common/Component';
import Button from 'flarum/common/components/Button';
// oxlint-disable-next-line id-length
import m from 'mithril';
import { ADMIN_CONSTANTS } from '../../common/config/constants';
import type {
  DynamicSlideSettingsComponentAttrs,
  SlideDataInternal,
  FlarumVnode
} from '../../common/config/types';

// Constants
const INITIAL_SLIDE_ID = 1; // Initial slide ID
const MINIMUM_SLIDES = 1; // Minimum number of slides required
const SLIDE_NOT_FOUND = -1; // Return value when slide is not found
const SLIDE_INCREMENT = 1; // Increment value for slide operations

/**
 * Dynamic component for managing advertisement slide settings with add/delete functionality
 */
export default class DynamicSlideSettingsComponent extends Component<DynamicSlideSettingsComponentAttrs> {
  private slides: SlideDataInternal[] = [];
  private loading = false;
  private nextId = INITIAL_SLIDE_ID;
  private timeouts: Record<string, NodeJS.Timeout> = {};

  oninit(vnode: FlarumVnode): void {
    super.oninit(vnode);
    this.loadExistingSlides();
  }

  /**
   * Load existing slides from settings
   */
  private loadExistingSlides(): void {
    const { extensionId, maxSlides = ADMIN_CONSTANTS.DEFAULT_MAX_SLIDES } = this.attrs;
    const slides: SlideDataInternal[] = [];

    // Load existing slides from settings
    for (let slideIndex = INITIAL_SLIDE_ID; slideIndex <= maxSlides; slideIndex += SLIDE_INCREMENT) {
      const linkKey = `${extensionId}.Link${slideIndex}`;
      const imageKey = `${extensionId}.Image${slideIndex}`;
      const link = app.data.settings[linkKey] || '';
      const image = app.data.settings[imageKey] || '';

      // Only include slides that have at least one field filled
      if (link || image) {
        slides.push({
          id: slideIndex,
          link,
          image
        });
        this.nextId = Math.max(this.nextId, slideIndex + SLIDE_INCREMENT);
      }
    }

    this.slides = slides;

    // If no slides exist, add one empty slide to start with
    if (slides.length === ADMIN_CONSTANTS.EMPTY_SLIDES_COUNT) {
      this.addSlide();
    }
  }

  /**
   * Add a new slide
   */
  private addSlide(): void {
    const newSlide: SlideDataInternal = {
      id: this.nextId,
      link: '',
      image: ''
    };

    this.nextId += 1;
    this.slides.push(newSlide);
    m.redraw();
  }

  /**
   * Remove a slide
   */
  private removeSlide(slideId: number): void {
    const { extensionId } = this.attrs;
    const slideIndex = this.slides.findIndex(slide => slide.id === slideId);
    
    if (slideIndex === SLIDE_NOT_FOUND) {
      return;
    }
    
    const slide = this.slides[slideIndex];
    
    // Remove from backend
    this.saveSetting(`${extensionId}.Link${slide.id}`, '');
    this.saveSetting(`${extensionId}.Image${slide.id}`, '');
    
    // Remove from local state
    this.slides.splice(slideIndex, SLIDE_INCREMENT);

    // Ensure at least one slide exists
    if (this.slides.length === ADMIN_CONSTANTS.EMPTY_SLIDES_COUNT) {
      this.addSlide();
    }
    
    m.redraw();
  }

  /**
   * Update slide data
   */
  private updateSlide(slideId: number, field: 'link' | 'image', value: string): void {
    const { extensionId } = this.attrs;
    const slide = this.slides.find(slideItem => slideItem.id === slideId);

    if (!slide) {
      return;
    }

    slide[field] = value;

    // Save to backend
    let settingKey = '';
    if (field === 'link') {
      settingKey = `${extensionId}.Link${slide.id}`;
    } else {
      settingKey = `${extensionId}.Image${slide.id}`;
    }

    this.saveSetting(settingKey, value);
  }

  /**
   * Save setting to backend with debouncing
   */
  private saveSetting(key: string, value: string): void {
    // Clear existing timeout for this key
    const timeoutKey = `saveTimeout_${key}`;
    clearTimeout(this.timeouts[timeoutKey]);

    // Set new timeout
    this.timeouts[timeoutKey] = setTimeout(() => {
      app.data.settings[key] = value;

      app.request({
        method: 'POST',
        url: app.forum.attribute('apiUrl') + '/settings',
        body: {
          [key]: value
        }
      }).catch(() => {
        // Handle save error silently for now
      });
    }, ADMIN_CONSTANTS.SAVE_DEBOUNCE_DELAY);
  }

  view(): unknown {
    return m('div.Form-group', [
      m('label.FormLabel',
        app.translator.trans('wusong8899-header-advertisement.admin.SlideSettings')
      ),
      m('div.helpText',
        app.translator.trans('wusong8899-header-advertisement.admin.SlideSettingsHelp')
      ),

      m('div.DynamicSlideSettings', [
        // Slides list
        this.slides.map((slide, slideIndex) => this.renderSlide(slide, slideIndex)),

        // Add button
        m('div.DynamicSlideSettings-addButton', [
          m(Button, {
            className: 'Button Button--primary',
            icon: 'fas fa-plus',
            onclick: () => this.addSlide()
          }, app.translator.trans('wusong8899-header-advertisement.admin.AddSlide'))
        ])
      ])
    ]);
  }

  /**
   * Render a single slide
   */
  private renderSlide(slide: SlideDataInternal, slideIndex: number): unknown {
    return m('div.DynamicSlideSettings-slide', {
      key: slide.id
    }, [
      m('div.DynamicSlideSettings-slideHeader', [
        m('h4', app.translator.trans('wusong8899-header-advertisement.admin.SlideNumber', { number: slideIndex + SLIDE_INCREMENT })),
        m(Button, {
          className: 'Button Button--danger',
          icon: 'fas fa-trash',
          onclick: () => this.removeSlide(slide.id),
          disabled: this.slides.length === MINIMUM_SLIDES
        }, app.translator.trans('wusong8899-header-advertisement.admin.DeleteSlide'))
      ]),

      m('div.DynamicSlideSettings-slideFields', [
        // Link URL field
        m('div.Form-group', [
          m('label.FormLabel',
            app.translator.trans('wusong8899-header-advertisement.admin.SlideLink')
          ),
          m('input.FormControl', {
            type: 'url',
            placeholder: 'https://example.com',
            value: slide.link,
            oninput: (event: Event) => {
              const target = event.target as HTMLInputElement;
              this.updateSlide(slide.id, 'link', target.value);
            }
          })
        ]),

        // Image URL field
        m('div.Form-group', [
          m('label.FormLabel',
            app.translator.trans('wusong8899-header-advertisement.admin.SlideImage')
          ),
          m('input.FormControl', {
            type: 'url',
            placeholder: 'https://example.com/image.jpg',
            value: slide.image,
            oninput: (event: Event) => {
              const target = event.target as HTMLInputElement;
              this.updateSlide(slide.id, 'image', target.value);
            }
          })
        ])
      ])
    ]);
  }
}
