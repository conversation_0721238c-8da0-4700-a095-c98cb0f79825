/**
 * Application constants for Header Advertisement extension
 */

// Mobile detection constants
export const MO<PERSON>LE_DETECTION = {
  USER_AGENT_SUBSTR_START: 0,
  USER_AGENT_SUBSTR_LENGTH: 4,
} as const;

// Error handling constants
export const ERROR_HANDLING = {
  MAX_ERROR_LOG_ENTRIES: 50,
  DOM_READY_TIMEOUT: 5000,
  SLIDE_NUMBER_MIN: 1,
  SLIDE_NUMBER_MAX: 30,
  TRANSITION_TIME_MIN: 1000,
  TRANSITION_TIME_MAX: 30_000,
  CONFIG_MAX_SLIDES_MIN: 1,
  CONFIG_MAX_SLIDES_MAX: 50,
} as const;

// Admin component constants
export const ADMIN_CONSTANTS = {
  SAVE_DEBOUNCE_DELAY: 500,
  DEFAULT_MAX_SLIDES: 30,
  EMPTY_SLIDES_COUNT: 0,
} as const;

// UI styling constants
export const UI_STYLES = {
  HEADER_ICON_HEIGHT: 24,
  HEADER_ICON_MARGIN_TOP: 8,
} as const;

// Mobile layout constants
export const MOBILE_LAYOUT = {
  SCREEN_WIDTH_MULTIPLIER: 2,
  SCREEN_WIDTH_OFFSET: 50,
  CONTAINER_MARGIN_MULTIPLIER: 0.254,
} as const;

// Slideshow constants
export const SLIDESHOW_CONSTANTS = {
  SLIDE_INCREMENT: 1,
  INITIAL_SLIDE_INDEX: 1,
  VALIDATION_ERRORS_EMPTY: 0,
} as const;

// Array and index constants
export const ARRAY_CONSTANTS = {
  EMPTY_LENGTH: 0,
  FIRST_INDEX: 0,
  NOT_FOUND_INDEX: -1,
  NEXT_ITEM_OFFSET: 1,
  LAST_ITEM_OFFSET: -1,
} as const;

// Timing constants
export const TIMING = {
  CHECK_INTERVAL: 10,
  DATA_CHECK_INTERVAL: 100,
  DEFAULT_TRANSITION_TIME: 5000,
} as const;

// DOM element constants
export const DOM_ELEMENTS = {
  SPLIDE_AD_CONTAINER_ID: 'splideAdContainer',
  HEADER_ICON_ID: 'wusong8899HeaderAdvIcon',
} as const;

// CSS class constants
export const CSS_CLASSES = {
  SPLIDE: 'splide',
  SPLIDE_TRACK: 'splide__track',
  SPLIDE_LIST: 'splide__list',
  SPLIDE_SLIDE: 'splide__slide',
  SPLIDE_ARROW_NEXT: 'splide__arrow--next',
  SPLIDE_ARROW_PREV: 'splide__arrow--prev',
  SPLIDE_PAGINATION: 'splide__pagination',
  AD_SPLIDE: 'adSplide',
} as const;

// CSS selector constants
export const CSS_SELECTORS = {
  APP_NAVIGATION_BACK_CONTROL: '#app-navigation .App-backControl',
  CONTENT_CONTAINER: '#content .container',
  NAV_ITEMS: '.item-nav',
  SPLIDE_PAGINATION_EL: '.splide__pagination',
  SPLIDE_ARROW_NEXT_EL: '.splide__arrow--next',
  SPLIDE_ARROW_PREV_EL: '.splide__arrow--prev',
} as const;



// Extension configuration constants
export const EXTENSION_CONFIG = {
  ID: 'wusong8899-header-advertisement',
  TRANSLATION_PREFIX: 'wusong8899-header-advertisement',
  MAX_SLIDES: 30,
  HEADER_ICON_URL: 'https://ex.cc/assets/files/date/test.png',
} as const;
