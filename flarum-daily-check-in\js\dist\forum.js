(()=>{var e={n:t=>{var n=t&&t.__esModule?()=>t.default:()=>t;return e.d(n,{a:n}),n},d:(t,n)=>{for(var a in n)e.o(n,a)&&!e.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:n[a]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};(()=>{"use strict";e.r(t);const n=flarum.core.compat.extend,a=flarum.core.compat["forum/components/IndexPage"];var o=e.n(a);const r=flarum.core.compat["components/Button"];var c=e.n(r);const s=flarum.core.compat["common/components/Alert"];var i=e.n(s);function u(e,t){return u=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},u(e,t)}function p(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,u(e,t)}const l=flarum.core.compat["components/Modal"];var f=e.n(l);flarum.core.compat["utils/Stream"];var d=function(e){function t(){return e.apply(this,arguments)||this}p(t,e);var n=t.prototype;return n.oninit=function(t){e.prototype.oninit.call(this,t)},n.className=function(){return"checkInResultModal Modal--small"},n.title=function(){return m("div",{className:"checkInResultModal successTitleText"},app.translator.trans("wusong8899-checkin.forum.check-in-success"))},n.content=function(){var e=app.session.user.attribute("totalContinuousCheckIn"),t=app.forum.attribute("forumCheckinSuccessPromptText"),n=app.forum.attribute("forumCheckinSuccessPromptRewardText"),a=app.forum.attribute("forumCheckinRewarMoney"),o="",r="checkInResultModal hideText",c="checkInResultModal hideText";return""!==t&&(r="checkInResultModal successText"),!0==(void 0!==app.forum.attribute("antoinefr-money.moneyname"))&&""!==n&&(o=(app.forum.attribute("antoinefr-money.moneyname")||"[money]").replace("[money]",a),c="checkInResultModal rewardText"),m("div",{className:"Modal-body"},m("div",{className:r},t.replace("[days]",e)),m("div",{className:c},n.replace("[reward]",o)))},t}(f()),h=function(e){function t(){return e.apply(this,arguments)||this}p(t,e);var n=t.prototype;return n.oninit=function(t){e.prototype.oninit.call(this,t)},n.className=function(){return"checkInResultModal Modal--small"},n.title=function(){return m("div",{className:"checkInResultModal failedTitleText"},app.translator.trans("wusong8899-checkin.forum.check-in-failed"))},n.content=function(){return m("div",{className:"Modal-body"},m("div",{className:"checkInResultModal successText"},app.translator.trans("wusong8899-checkin.forum.try-again-later")))},t}(f());app.initializers.add("wusong8899-checkin",(function(){(0,n.extend)(o().prototype,"sidebarItems",(function(e){if(null!==app.session.user&&!0===app.forum.attribute("allowCheckIn")){var t,n=app.current.get("routeName"),a=app.session.user.attribute("totalContinuousCheckIn"),o=app.session.user.attribute("canCheckin"),r=app.session.user.attribute("canCheckinContinuous"),s=(app.session.user.attribute("serverDate"),app.forum.attribute("forumCheckinSuccessPromptType")),u=app.session.user.attribute("checkInCompatibleExtensions"),p=(app.session.user.attribute("lastCheckinTime"),"forum-checkin");if("tag"===n&&-1!==u.indexOf("fof-follow-tags")&&(p="forum-checkin-1"),!0===o){t=app.translator.trans("wusong8899-checkin.forum.check-in"),e.add(p,c().component({icon:"fas fa-calendar",className:"Button CheckInButton--yellow",itemClassName:"App-primaryControl",id:"checkInButton",onclick:function(){app.session.user.save({canCheckin:!1,totalContinuousCheckIn:!0===r?a+1:1}).then((function(){var e=app.forum.attribute("forumCheckinSuccessPromptText"),t=app.forum.attribute("forumCheckinSuccessPromptRewardText"),n=app.session.user.attribute("canCheckin");if(""!==e||""!==t)if(1===s)if(!1===n){var a=app.forum.attribute("forumCheckinRewarMoney"),o=void 0!==app.forum.attribute("antoinefr-money.moneyname"),r="";if(""!==e){var c=e.replace("[days]",app.session.user.attribute("totalContinuousCheckIn"));app.alerts.show(i(),{type:"success"},c)}if(!0===o&&""!==t){r=(app.forum.attribute("antoinefr-money.moneyname")||"[money]").replace("[money]",a);var u=t.replace("[reward]",r);app.alerts.show(i(),{type:"success"},u)}}else app.alerts.show(i(),{type:"error"},app.translator.trans("wusong8899-checkin.forum.check-in-failed")+" "+app.translator.trans("wusong8899-checkin.forum.try-again-later"));else 2===s&&(!1===n?app.modal.show(d):app.modal.show(h))}))}},t),50);var l=app.forum.attribute("forumAutoCheckin"),m=app.forum.attribute("forumAutoCheckinDelay");1===l&&setTimeout((function(){$("#checkInButton").click()}),m)}else t=a<=1?app.translator.trans("wusong8899-checkin.forum.checked-in-day",{count:a}):app.translator.trans("wusong8899-checkin.forum.checked-in-days",{count:a}),e.add(p,c().component({icon:"fas fa-calendar-check",className:"Button CheckInButton--green",itemClassName:"App-primaryControl",disabled:!0},t),50)}}))}))})(),module.exports=t})();
//# sourceMappingURL=forum.js.map