wusong8899-decoration-store:
  lib:
    confirm: 确定
    close: 关闭
    cancel: 取消
    ok: 确定
    none: 无
    item-type: 物品类型
    item-type-all: 全部物品类型
    item-type-avatarFrame: 头像边框
    item-type-profileBackground: 用户卡背景图片
    item-type-usernameColor: 用户名颜色
    item-label: 商品标签
    item-label-owned: 已拥有
    item-label-recommend: 今日推荐
    item-label-popular: 热门商品
    item-title: 商品名称
    item-desc: 商品描述
    item-cost: 价格
    item-cost-free: 免费
    item-amount: 库存
    item-stock-remaining: 剩余
    item-sold: 售出
    item-discount-ribbon: "优惠 {discount}%"
    item-discount-days-text: "折扣时长: {days}天"
    item-discount-days-left: "（剩余{days}天）"
    item-purchase-type: 购买类型
    item-purchase-type-all: 全部购买类型
    item-purchase-type-onetime: 永久拥有
    item-purchase-type-monthly: 按月付费
    item-purchase-type-yearly: 按年付费
    list-load-more: 读取更多
    list-empty: 这里空空如也
    save-error: 保存错误
    save-error-item-expired: 物品已过期
    delete-error: 删除错误
    submit-error-field-empty: 还有必填项没有填写
  forum:
    navigate: 导航
    display-name: 名称
    display-name-default: 社区装饰店
    my-decoration: 我的物品
    view-shopping-cart: 购物车
    processing: 请稍等...
    user-money-amount: 当前拥有
    item-subscribe: 继续订阅
    item-unsubscribe: 取消订阅
    item-soldout: 物品已售光
    item-expired: 已取消订阅
    item-already-have: 已拥有该物品
    item-property-style-text-effect: 效果预览
    item-purchase-status-button-0: 装备物品
    item-purchase-status-button-1: 卸下物品
    item-purchase-title: Item name
    item-purchase-purchase-type: 购买类型
    item-purchase-cost: 购买价格
    item-purchase-subscription-cost: 订阅价格
    item-purchase-item-type: 物品类型
    item-purchase-item-type-avatarFrame: 头像边框
    item-purchase-item-type-profileBackground: 用户卡背景图片
    item-purchase-item-type-usernameColor: 用户名颜色
    item-purchase-item-status: 物品状态
    item-purchase-item-status-0: 未使用
    item-purchase-item-status-1: 正在使用
    item-purchase-subscribe-item-status-0: 订阅中，未使用
    item-purchase-subscribe-item-status-1: 订阅中，正在使用
    item-purchase-date: 购买日期
    item-status-to-0-confirmation: 你确定要卸下这件物品吗？
    item-status-to-1-confirmation: 你确定要装备这件物品吗？
    item-subscription-deduction-days: "订阅扣费还剩{days}天"
    equipment-modal-title: 物品列表
    shopping-cart: 购物车
    shopping-cart-close: 关闭
    purchase-history: 装饰店订单
    purchase-confirmation: 购买确认
    purchase-subscribe-confirmation: "你确定要支付{cost}来重新订阅该物品吗？"
    purchase-unsubscribe-confirmation: 你确定要取消订阅该物品吗？
    purchase-success: 购买成功
    purchase-add-cart: 加入购物车
    purchase-now: 去购物
    purchase-error: 购买时出现错误
    purchase-error-insufficient-fund: 资金不足
    purchase-error-item-alreay-have: 已经拥有该物品
    notification-receive-subscription: 订阅扣费通知
    notifications:
      subscription-expired: "你订阅的 {title} 已过期"
      subscription-expired-excerpt: "需要 {cost} 才能继续订阅"
      subscription-fee-deduction: "支付订阅费用 {title}"
      subscription-fee-deduction-excerpt: "花费 {cost}"
  admin:
    item-type-selection: 选择物品类型
    data-add: 添加
    data-save: 保存
    item-add: 添加商品
    item-edit: 编辑
    item-delete: 删除
    item-activate: 上架
    item-deactivate: 下架
    item-id: ID
    item-discount: 折扣
    item-discount-none: 无
    item-discount-days: 折扣时长（天）
    item-filter-all: 全部
    item-activate-status: 商品状态
    item-activate-status-all: 全部商品状态
    item-activate-status-down: 下架
    item-activate-status-up: 上架
    item-property-style: 样式
    item-property-style-help-text: 你可以在CSS中添加自己的样式，然后在下方输入自定义的样式
    item-property-style-text-effect: 效果预览
    item-property-style-select-from-gallery: 内置样式库
    item-property-image: 图片
    item-property-image-upload: 上传
    item-property-image-select-from-gallery: 内置图库
    item-assigned-at: 添加日期
    item-activate-confirmation: 你确定上架该物品吗?
    item-deactivate-confirmation: 你确定要下架该物品吗?
    item-delete-confirmation: 你确定要删除该物品吗?
    gallery-image-in-use: "{count}件商品正在使用"
    settings:
      display-name: 名称
      display-name-default: 社区商店
      timezone: 时区设置
      timezone-help: 设置时区。默认为 "Asia/Shanghai"
      timezone-default: Asia/Shanghai
