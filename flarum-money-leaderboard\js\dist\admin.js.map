{"version": 3, "file": "admin.js", "mappings": "MACA,IAAIA,ECAqBC,IACH,oBAAXC,QAA0BA,OAAOC,aAC1CC,OAAOC,eAAeJ,EAASC,OAAOC,YAAa,CAAEG,MAAO,WAE7DF,OAAOC,eAAeJ,EAAS,aAAc,CAAEK,OAAO,K,6BCLlBC,OAAOC,KAAKC,OAAe,OCEhEC,IAAIC,aAAaC,IAAI,2BAA2B,WAC9CF,IAAIG,cAAJ,IACO,4BACJC,gBAAgB,CACfC,QAAS,+CACTC,MAAON,IAAIO,WAAWC,MAAM,2DAC5BC,KAAMT,IAAIO,WAAWC,MAAM,gEAC3BE,KAAM,WAEPN,gBAAgB,CACfC,QAAS,wDACTC,MAAON,IAAIO,WAAWC,MAAM,qEAC5BE,KAAM,SACNC,QAAS,CACP,EAAGX,IAAIO,WAAWC,MAAM,oEACxB,EAAGR,IAAIO,WAAWC,MAAM,4EAG3BJ,gBAAgB,CACfC,QAAS,kDACTC,MAAON,IAAIO,WAAWC,MAAM,qEAC5BI,YAAY,GACZC,IAAI,EACJH,KAAM,WAEPN,gBAAgB,CACfC,QAAS,mDACTC,MAAON,IAAIO,WAAWC,MAAM,sEAC5BI,YAAY,GACZC,IAAI,EACJH,KAAM,WAEPN,gBAAgB,CACfC,QAAS,mDACTC,MAAON,IAAIO,WAAWC,MAAM,sEAC5BI,YAAY,GACZC,IAAI,EACJH,KAAM,WAEPI,mBAAmB,CAChBC,KAAM,wBACNT,MAAON,IAAIO,WAAWC,MAAM,mEAC5BQ,WAAY,yCAEd,WACA,Q", "sources": ["webpack://@wusong8899/flarum-daily-check-in/webpack/bootstrap", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/make namespace object", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['extend']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/admin/index.js"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['extend'];", "import {extend, override} from 'flarum/extend';\n\napp.initializers.add('wusong8899-money-leaderboard', () => {\n  app.extensionData\n    .for('wusong8899-money-leaderboard')\n    .registerSetting({\n      setting: 'wusong8899-money-leaderboard.moneyLeaderBoardIcon',\n      label: app.translator.trans('wusong8899-money-leaderboard.admin.settings.leaderboard-icon'),\n      help: app.translator.trans('wusong8899-money-leaderboard.admin.settings.leaderboard-icon-help'),\n      type: 'string',\n    })\n    .registerSetting({\n      setting: 'wusong8899-money-leaderboard.moneyLeaderBoardEntryPosition',\n      label: app.translator.trans('wusong8899-money-leaderboard.admin.settings.leaderboard-entry-position'),\n      type: 'select',\n      options: {\n        0: app.translator.trans('wusong8899-money-leaderboard.admin.settings.leaderboard-entry-sideNav'),\n        1: app.translator.trans('wusong8899-money-leaderboard.admin.settings.leaderboard-entry-topRightMenu')\n      },\n    })\n    .registerSetting({\n      setting: 'wusong8899-money-leaderboard.leaderboardMaxLoadCount',\n      label: app.translator.trans('wusong8899-money-leaderboard.admin.settings.leaderboard-max-load-count'),\n      placeholder:50,\n      min:1,\n      type: 'number',\n    })\n    .registerSetting({\n      setting: 'wusong8899-money-leaderboard.leaderboardInitLoadCount',\n      label: app.translator.trans('wusong8899-money-leaderboard.admin.settings.leaderboard-init-load-count'),\n      placeholder:20,\n      min:1,\n      type: 'number',\n    })\n    .registerSetting({\n      setting: 'wusong8899-money-leaderboard.leaderboardLoadMoreCount',\n      label: app.translator.trans('wusong8899-money-leaderboard.admin.settings.leaderboard-load-more-count'),\n      placeholder:10,\n      min:1,\n      type: 'number',\n    })\n    .registerPermission({\n        icon: 'fas fa-sort-amount-up',\n        label: app.translator.trans('wusong8899-money-leaderboard.admin.permission.allow_view_leaderboard'),\n        permission: 'moneyLeaderboard.allowViewLeaderbaord',\n      },\n      'moderate',\n      90\n    );\n});\n"], "names": ["__webpack_require__", "exports", "Symbol", "toStringTag", "Object", "defineProperty", "value", "flarum", "core", "compat", "app", "initializers", "add", "extensionData", "registerSetting", "setting", "label", "translator", "trans", "help", "type", "options", "placeholder", "min", "registerPermission", "icon", "permission"], "sourceRoot": ""}