---
type: "agent_requested"
description: "Flarum Beta 15 管理后台 (Admin Dashboard) 开发指南"
---

### **主题：Flarum Beta 15 管理后台 (Admin Dashboard) 开发指南**

#### **一、核心概述**

Flarum Beta 15 引入了全新的管理面板和前端 API (`ExtensionData API`)，旨在简化为 Flarum 扩展添加**设置 (Settings)** 和 **权限 (Permissions)** 的流程。开发者可以通过以下三种方式为自己的扩展配置管理页面：

1.  **注册简单设置 (Registering Settings)**：通过`ExtensionData` API 快速添加简单的键值对设置。
2.  **扩展默认页面 (Extending ExtensionPage)**：继承并扩展默认的扩展管理页面，以增加自定义逻辑。
3.  **提供完全自定义页面 (Providing a Custom Page)**：用一个全新的组件替换整个扩展管理页面。

---

#### **二、`ExtensionData` API 使用详解**

这是推荐的、用于添加标准设置和权限的现代化 API。

##### **1. 初始化：指定扩展**

在使用 API 之前，必须先声明要为哪个扩展配置数据。

- **方法**：`app.extensionData.for('extension-id')`
- **`extension-id` 的确定规则**:

  1.  获取 `composer.json` 中的 `name`，例如 `fof/merge-discussions`。
  2.  将所有斜杠 `/` 替换为短横线 `-`，得到 `fof-merge-discussions`。
  3.  如果包名以 `flarum-` 或 `flarum-ext-` 开头，则移除该前缀。例如 `webbinaro/flarum-calendar` 变为 `webbinaro-calendar`。

- **示例代码** (针对虚构扩展 `acme/interstellar`):

  ```javascript
  import { extend } from "flarum/common/extend";

  app.initializers.add("acme-interstellar", (app) => {
    app.extensionData.for("acme-interstellar");
    // ... 在这里链接后续的注册方法
  });
  ```

##### **2. 注册设置 (`registerSetting`)**

此方法用于向扩展页面添加设置项。推荐用于存储在 `settings` 数据表中的简单配置。

###### **2.1. 基于对象的简单设置**

通过传递一个配置对象来定义设置项。

- **方法**：`.registerSetting(settingObject, [priority])`
- **`settingObject` 常用属性**:

  - `setting`: (String) 设置项在数据库 `settings` 表中存储的键名。
  - `label`: (String) 设置项的标签，通常使用翻译函数 `app.translator.trans(...)`。
  - `help`: (String, 可选) 帮助文本，对设置项进行更详细的解释。
  - `type`: (String) 输入框类型。有效值包括：`boolean` (开关), `text`, `number`, `select` 或任何其他有效的 `<input>` `type` 属性值。
  - **其他属性**: 对象中任何未被识别的属性都将作为组件的 `attrs` 传递，可用于设置 `placeholder`, `min`, `max` 等。

- **示例 1：布尔值 (Switch)**

  ```javascript
  .registerSetting({
    setting: 'acme-interstellar.coordinates',
    label: app.translator.trans('acme-interstellar.admin.coordinates_label'),
    help: app.translator.trans('acme-interstellar.admin.coordinates_help'),
    type: 'boolean',
  }, 30) // 可选的优先级
  ```

- **示例 2：下拉选择 (Select)**

  ```javascript
  .registerSetting({
    setting: 'acme-interstellar.fuel_type',
    label: app.translator.trans('acme-interstellar.admin.fuel_type_label'),
    type: 'select',
    options: {
      'LOH': 'Liquid Fuel', // 键是存储值，值是显示标签
      'RDX': 'Solid Fuel',
    },
    default: 'LOH',
  })
  ```

- **示例 3：数字输入 (带额外属性)**
  ```javascript
  .registerSetting({
    setting: 'acme-interstellar.crew_count',
    label: app.translator.trans('acme-interstellar.admin.crew_count_label'),
    type: 'number',
    min: 1,
    max: 10
  })
  ```

###### **2.2. 基于回调的复杂设置**

当需要更复杂的 UI 或逻辑时，可以传递一个返回 JSX 的回调函数。

- **方法**：`.registerSetting(callback)`
- **回调函数特点**:

  - 它将在 `ExtensionPage` 组件的上下文中执行。
  - 需要手动处理设置值的绑定和保存（例如使用 `this.setting('key')`）。
  - 返回的内容必须是有效的 JSX。

- **示例代码**：
  ```javascript
  .registerSetting(function () {
    if (app.session.user.username() === 'RocketMan') {
      return (
        <div className="Form-group">
          <h1>{app.translator.trans('acme-interstellar.admin.you_are_rocket_man_label')}</h1>
          <label className="checkbox">
            <input type="checkbox" bidi={this.setting('acme-interstellar.rocket_man_setting')}/>
            {app.translator.trans('acme-interstellar.admin.rocket_man_setting_label')}
          </label>
        </div>
      );
    }
  })
  ```

##### **3. 注册权限 (`registerPermission`)**

用于注册扩展相关的权限。注册后，该权限会同时出现在扩展自己的管理页面和全局的权限管理页面。

- **方法**：`.registerPermission(permissionObject, category, [priority])`
- **参数**:
  1.  `permissionObject`: 定义权限的详细信息。
  2.  `category`: 权限所属的分类 (参考 `PermissionGrid` 的分类，如 `'start'`, `'reply'`, `'view'`, `'moderate'`)。
  3.  `priority`: (可选) 优先级。
- **`permissionObject` 属性**:

  - `icon`: (String) Font Awesome 图标类名，如 `fas fa-rocket`。
  - `label`: (String) 权限的显示标签。
  - `permission`: (String) 权限在数据库中存储的实际名称，用于权限检查。
  - `tagScoped`: (Boolean) 是否允许该权限在标签（Tags）级别上进行配置。
    - **注意**: 以 `discussion.` 开头的权限默认 `tagScoped` 为 `true`，除非显式设置为 `false`。

- **示例代码**:
  ```javascript
  .registerPermission({
    icon: 'fas fa-rocket',
    label: app.translator.trans('acme-interstellar.admin.permissions.fly_rockets_label'),
    permission: 'discussion.rocket_fly',
    tagScoped: true,
  }, 'start', 95)
  ```

##### **4. 链式调用 (Chaining)**

所有 `ExtensionData` 的注册方法都支持链式调用，使代码更简洁。

- **示例代码**:
  ```javascript
  app.extensionData
    .for("acme-interstellar")
    .registerSetting(/* ... */)
    .registerSetting(/* ... */)
    .registerPermission(/* ... */)
    .registerPermission(/* ... */);
  ```

---

#### **三、自定义扩展页面 (Extending/Overriding the Default Page)**

当 `registerSetting` 无法满足复杂需求时（例如需要处理模型关系或设计完全不同的 UI），可以提供一个自定义的页面组件。

##### **1. 创建页面组件**

创建一个继承自 `flarum/admin/components/ExtensionPage` 或基础 `Page` 的新类。

- **提示**: `ExtensionPage` 类提供了一些有用的方法，比如 `buildSettingComponent`（`registerSetting` 内部使用的工具）。

- **示例代码 (`StarPage.js`)**:

  ```javascript
  import ExtensionPage from "flarum/admin/components/ExtensionPage";

  export default class StarPage extends ExtensionPage {
    content() {
      return <h1>Hello from the settings section!</h1>;
    }
  }
  ```

##### **2. 注册自定义页面**

使用 `.registerPage()` 方法将你的自定义组件注册到扩展。

- **方法**: `app.extensionData.for('extension-id').registerPage(YourPageComponent)`

- **示例代码**:

  ```javascript
  import StarPage from "./components/StarPage";

  app.initializers.add("interstellar", (app) => {
    app.extensionData.for("acme-interstellar").registerPage(StarPage);
  });
  ```

  此后，默认的设置和权限列表将被 `StarPage` 组件的内容所取代。

---

#### **四、`composer.json` 元数据 (Metadata)**

扩展的管理页面会自动从其 `composer.json` 文件中提取并显示额外信息。

| 显示信息        | `composer.json` 键路径 | 备注                               |
| :-------------- | :--------------------- | :--------------------------------- |
| **论坛讨论帖**  | `support.forum`        | 指向 discuss.flarum.org 的链接     |
| **文档**        | `support.docs`         | 指向扩展文档的链接                 |
| **支持 (邮箱)** | `support.email`        | 支持邮箱地址                       |
| **网站**        | `homepage`             | 扩展或作者的官方网站               |
| **捐赠**        | `funding`              | 捐赠链接数组，目前仅使用第一个链接 |
| **源码**        | `support.source`       | 指向源代码仓库的链接               |
