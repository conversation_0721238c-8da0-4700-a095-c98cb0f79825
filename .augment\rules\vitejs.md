---
type: "agent_requested"
description: "本规则旨在指导 AI 编码代理在**所有基于 Vite 的现代 Web 项目**中进行开发、构建和配置。它涵盖了从项目初始化到生产构建的全过程，确保代理能够高效、规范地使用 Vite 的各项功能。"
---

# Vite 项目开发与构建规则

## 1. 目的与适用范围

本规则旨在指导 AI 编码代理在**所有基于 Vite 的现代 Web 项目**中进行开发、构建和配置。它涵盖了从项目初始化到生产构建的全过程，确保代理能够高效、规范地使用 Vite 的各项功能。

- **适用范围**:
  - **项目初始化**: 使用 `pnpm create vite@latest` 等脚手架工具。
  - **开发服务器**: `vite.config.js` / `vite.config.ts` 的配置与管理。
  - **构建流程**: 生产环境构建、预览及相关优化。
  - **插件系统**: Vite 插件的引入、配置与管理。
- **核心理念**: 遵循 Vite 的“约定优于配置”原则，仅在必要时进行扩展和自定义。

## 2. 结构与格式

- **项目根目录**:
  - `index.html` 必须作为应用入口位于项目根目录。
  - `vite.config.js` 或 `vite.config.ts` 文件必须位于项目根目录。
- **原子化规则**: 每条规则清晰、独立，且直接对应 Vite 的一项功能、配置或命令。

## 3. 工具使用与工作循环

- **代理角色**: 你是一名熟练掌握 Vite 的前端构建工程师。
- **环境约束**:
  - **Node.js 版本**: 必须为 `20.19+` 或 `22.12+`，以满足 Vite 及其模板的兼容性要求。
  - **可用命令**: `vite`, `vite dev`, `vite serve`, `vite build`, `vite preview`。
- **工作循环**: **开发 → 构建 → 预览**
  - **计划 (Plan)**: 识别任务目标，如“启动开发服务器”、“添加 React 插件”或“为生产环境构建”。
  - **执行 (Execute)**:
    - **开发**: `pnpm dev` (或 `vite`)。
    - **构建**: `pnpm build` (或 `vite build`)。
    - **预览**: `pnpm preview` (或 `vite preview`)。
  - **验证 (Verify)**:
    - **开发**: 在浏览器中打开服务器地址 (默认为 `http://localhost:5173`)，检查 HMR 是否正常工作。
    - **构建**: 检查 `dist/` 目录是否生成，运行 `pnpm preview` 验证构建产物的功能是否与开发时一致。

## 4. Vite 核心约定

### 4.1 项目入口

- `index.html` 是应用的核心入口，所有`<script type="module" src="...">` 和 `<link href="...">` 都会被 Vite 处理。URL 路径将自动重写，**禁止**使用 `%PUBLIC_URL%` 这样的占位符。

### 4.2 TypeScript 支持

- Vite **只负责转译（transpile） `.ts` 文件，不执行类型检查**。
- **类型检查**:
  - **开发时**: 必须在单独的进程中运行 `tsc --noEmit --watch` 或使用 `vite-plugin-checker` 插件在浏览器中报告类型错误。
  - **构建时**: CI 流程中**必须**在 `vite build` 命令之前或之后运行 `tsc --noEmit`。
- **`tsconfig.json` 关键配置**:
  - `"isolatedModules": true`: **必须设置**，以确保 TS 编译器对与 esbuild 兼容性不佳的特性发出警告。
  - `"useDefineForClassFields": true`: 推荐设置，以符合 ECMAScript 标准。
  - `"skipLibCheck": true`: 推荐保持默认值，以避免检查 `node_modules` 中库的类型。

### 4.3 静态资源处理

- 导入静态资源 (如 `import imgUrl from './img.png'`) 会返回其公共 URL。
- **特殊查询后缀**:
  - `?url`: 显式获取资源 URL。
  - `?raw`: 将资源作为原始字符串导入。
  - `?worker`: 将脚本作为 Web Worker 导入。
  - `?inline`: 构建时内联资源。

### 4.4 CSS

- **默认行为**: 直接导入的 CSS 文件 (`import './style.css'`) 会被注入到页面的 `<style>` 标签中。
- **CSS Modules**: 文件名以 `.module.css` 结尾的被视为 CSS Modules。
- **预处理器**: 要使用 Sass/Less/Stylus，只需安装对应的预处理器包（如 `sass`），无需安装 Vite 插件。

## 5. 配置文件 (`vite.config.js`/`ts`)

- **基本结构**:

  ```javascript
  import { defineConfig } from "vite";
  import react from "@vitejs/plugin-react";

  export default defineConfig({
    plugins: [react()],
    // 其他配置...
  });
  ```

- **插件 (`plugins`)**:
  - 插件是扩展 Vite 功能的主要方式。
  - 插件安装后，必须在 `vite.config.js` 的 `plugins` 数组中引入和调用。
  - **执行顺序**: 使用 `enforce: 'pre'` 或 `enforce: 'post'` 来调整插件与 Vite 核心插件的执行顺序。
  - **条件应用**: 使用 `apply: 'build'` 或 `apply: 'serve'` 使插件仅在特定模式下生效。
- **`esbuild` 配置**: 用于配置 JSX 工厂函数或注入 JSX helpers。
  ```javascript
  esbuild: {
    jsxFactory: 'h',
    jsxFragment: 'Fragment',
    jsxInject: `import React from 'react'`,
  }
  ```
- **构建目标 (`build.target`)**: 默认目标是支持原生 ES 模块的现代浏览器。若需支持旧版浏览器，必须使用 `@vitejs/plugin-legacy` 插件。

## 6. 命令行接口 (CLI)

- **开发服务器**: `vite` 或 `vite serve`
  - `--host`: 指定主机名。
  - `--port <port>`: 指定端口。
  - `--open`: 启动时自动在浏览器中打开。
  - `--force`: 强制重新构建依赖。
- **生产构建**: `vite build`
  - `--outDir <dir>`: 指定输出目录 (默认 `dist`)。
  - `--sourcemap`: 生成 source map。
  - `--minify false`: 禁用代码压缩。
  - `--watch`: 监听文件变化并重新构建。
- **生产预览**: `vite preview`
  - 用于在本地测试生产构建产物，**严禁**用作生产服务器。

## 7. 非目标与拒绝策略

- **非目标**:
  - 使用 Vite 作为生产环境的静态文件服务器（应使用 `vite preview` 进行本地预览，生产环境部署到 Nginx、Vercel 等）。
  - 依赖 Vite 进行类型检查（必须使用 `tsc` 或插件）。
- **拒绝情况**:
  - 项目初始化未使用官方或社区推荐的模板，导致配置不全。
  - 在需要类型安全的项目中，CI 流程缺少 `tsc --noEmit` 步骤。
  - 试图在 Vite 中使用 Webpack 特有的 loader 或插件语法。

## 8. 最小示例

### 示例 1: 创建一个 React + TypeScript 项目并启动开发服务器

**任务**: 使用 Vite 脚手架创建一个 React TS 项目，并成功运行。

**验收标准**:

1. 项目成功创建。
2. 运行 `pnpm dev` 后，浏览器能正常访问并显示默认页面。
3. HMR 功能正常。

**代理工作流**:

1.  **计划**:
    - 使用 `pnpm create vite@latest` 命令。
    - 按照提示选择 `react` 和 `react-ts`。
    - 进入项目目录，安装依赖，启动服务器。
2.  **执行**:

    ```bash
    # 1. 创建项目
    pnpm create vite@latest my-react-app --template react-ts

    # 2. 进入目录并安装依赖
    cd my-react-app
    pnpm install

    # 3. 启动开发服务器
    pnpm dev
    ```

3.  **验证**:
    - 在浏览器中打开 `http://localhost:5173`。
    - 修改 `src/App.tsx` 中的文本，保存后观察浏览器是否无需刷新即可更新内容。

### 示例 2: 为项目添加旧版浏览器支持

**任务**: 为现有 Vite 项目添加 `@vitejs/plugin-legacy` 以支持旧版浏览器。

**验收标准**:

1. 插件被安装并配置在 `vite.config.js` 中。
2. 运行 `vite build` 后，`dist/` 目录中包含 polyfill 和兼容性转换后的代码。

**代理工作流**:

1.  **计划**:
    - 安装 `@vitejs/plugin-legacy`。
    - 修改 `vite.config.js` 文件，在 `plugins` 数组中添加 legacy 插件。
2.  **执行**:

    - **安装插件**: `pnpm add -D @vitejs/plugin-legacy`
    - **修改 `vite.config.js`**:

      ```javascript
      import { defineConfig } from "vite";
      import react from "@vitejs/plugin-react";
      import legacy from "@vitejs/plugin-legacy";

      export default defineConfig({
        plugins: [
          react(),
          legacy({
            targets: ["defaults", "not IE 11"],
          }),
        ],
      });
      ```

3.  **验证**:
    - 运行 `pnpm build`。
    - 检查 `dist/index.html`，确认其中包含了针对旧版浏览器的 polyfill 脚本。
