<?php

namespace wusong8899\Client1LinksQueue\Controllers;

use wusong8899\Client1LinksQueue\Serializer\LinksQueueSerializer;
use wusong8899\Client1LinksQueue\Model\LinksQueue;

use <PERSON>larum\Api\Controller\AbstractCreateController;
use Flarum\Foundation\ValidationException;
use Flarum\Locale\Translator;
use Psr\Http\Message\ServerRequestInterface;
use Tobscure\JsonApi\Document;
use Illuminate\Support\Arr;

class LinksQueueDeleteController extends AbstractCreateController
{
    public $serializer = LinksQueueSerializer::class;
    protected $translator;

    public function __construct(Translator $translator)
    {
        $this->translator = $translator;
    }

    protected function data(ServerRequestInterface $request, Document $document)
    {
        $actor = $request->getAttribute('actor');
        $actor->assertAdmin();
        $linkQueueID = Arr::get($request->getQueryParams(), 'id');

        if (!isset($linkQueueID)) {
            $errorMessage = 'wusong8899-links-queue.admin.delete-error';
        } else {
            $errorMessage = "";
            $linkQueueData = LinksQueue::find($linkQueueID);

            if (!isset($linkQueueData)) {
                $errorMessage = 'wusong8899-links-queue.admin.delete-error';
            } else {
                $linkQueueData->delete();
                return $linkQueueData;
            }
        }

        if ($errorMessage !== "") {
            throw new ValidationException(['message' => $this->translator->trans($errorMessage)]);
        }
    }
}
