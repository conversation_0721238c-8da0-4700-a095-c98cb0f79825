# Daily Check In 每日签到

![License](https://img.shields.io/badge/license-MIT-blue.svg)

A [Flarum](http://flarum.org) extension. Add a button in the index page that allow user to daily check in the forum and receive reward.  
一个 Flarum 扩展。在论坛首页加入签到按钮，允许用户进行每日签到并获得奖励。

### Installation

Install with composer:

```sh
composer require wusong8899/flarum-daily-check-in
```

### Updating

```sh
composer update wusong8899/flarum-daily-check-in
php flarum migrate
php flarum cache:clear
```
