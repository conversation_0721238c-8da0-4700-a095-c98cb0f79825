{"version": 3, "file": "admin.js", "mappings": "MACA,IAAIA,ECAqBC,IACH,oBAAXC,QAA0BA,OAAOC,aAC1CC,OAAOC,eAAeJ,EAASC,OAAOC,YAAa,CAAEG,MAAO,WAE7DF,OAAOC,eAAeJ,EAAS,aAAc,CAAEK,OAAO,K,6BCLlBC,OAAOC,KAAKC,OAAe,OCEhEC,IAAIC,aAAaC,IAAI,iBAAiB,WACpCF,IAAIG,cAAJ,IACO,yBACJC,iBAAgB,WACf,OACE,SAAKC,UAAU,cACb,eAAQL,IAAIM,WAAWC,MAAM,8CAC7B,SAAKC,MAAM,YAAYR,IAAIM,WAAWC,MAAM,0DAC5C,WAAOE,KAAK,SAASJ,UAAU,cAAcK,KAAK,MAAMC,KAAMC,KAAKC,QAAQ,gDAIhFT,gBAAgB,CACfS,QAAS,sCACTC,MAAOd,IAAIM,WAAWC,MAAM,yCAC5BE,KAAM,WAEPL,gBAAgB,CACfS,QAAS,kCACTC,MAAOd,IAAIM,WAAWC,MAAM,8CAC5BE,KAAM,WAEPL,gBAAgB,CACfS,QAAS,uCACTC,MAAOd,IAAIM,WAAWC,MAAM,oDAC5BE,KAAM,WAEPL,gBAAgB,CACfS,QAAS,+CACTC,MAAOd,IAAIM,WAAWC,MAAM,6DAC5BE,KAAM,SACNM,QAAS,CACP,EAAGf,IAAIM,WAAWC,MAAM,qCACxB,EAAGP,IAAIM,WAAWC,MAAM,sCACxB,EAAGP,IAAIM,WAAWC,MAAM,yCAG3BH,iBAAgB,WACf,OACE,SAAKC,UAAU,cACb,eAAQL,IAAIM,WAAWC,MAAM,8DAC7B,SAAKC,MAAM,YAAYR,IAAIM,WAAWC,MAAM,sEAC5C,WAAOE,KAAK,SAASJ,UAAU,cAAcK,KAAK,MAAMC,KAAMC,KAAKC,QAAQ,sDAIhFT,iBAAgB,WACf,OACE,SAAKC,UAAU,cACb,eAAQL,IAAIM,WAAWC,MAAM,qEAC7B,SAAKC,MAAM,YAAYR,IAAIM,WAAWC,MAAM,0DAC5C,SAAKC,MAAM,YAAYR,IAAIM,WAAWC,MAAM,6EAC5C,WAAOE,KAAK,SAASJ,UAAU,cAAcK,KAAK,MAAMC,KAAMC,KAAKC,QAAQ,4DAIhFG,mBACC,CACEC,KAAM,iBACNH,MAAOd,IAAIM,WAAWC,MAAM,+CAC5BW,WAAY,wBAEd,WACA,Q", "sources": ["webpack://@wusong8899/flarum-daily-check-in/webpack/bootstrap", "webpack://@wusong8899/flarum-daily-check-in/webpack/runtime/make namespace object", "webpack://@wusong8899/flarum-daily-check-in/external root \"flarum.core.compat['extend']\"", "webpack://@wusong8899/flarum-daily-check-in/./src/admin/index.js"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['extend'];", "import {extend, override} from 'flarum/extend';\n\napp.initializers.add('wusong8899-checkin', () => {\n  app.extensionData\n    .for('wusong8899-daily-check-in')\n    .registerSetting(function () {\n      return (\n        <div className=\"Form-group\">\n          <label>{app.translator.trans('wusong8899-checkin.admin.settings.reward-money')}</label>\n          <div class=\"helpText\">{app.translator.trans('wusong8899-checkin.admin.settings.reward-money-requirement')}</div>\n          <input type=\"number\" className=\"FormControl\" step=\"any\" bidi={this.setting('wusong8899-forum-checkin.checkinRewardMoney')} />\n        </div>\n      );\n    })\n    .registerSetting({\n      setting: 'wusong8899-forum-checkin.checkinTimeZone',\n      label: app.translator.trans('wusong8899-checkin.admin.settings.timezone'),\n      type: 'number',\n    })\n    .registerSetting({\n      setting: 'wusong8899-forum-checkin.autoCheckIn',\n      label: app.translator.trans('wusong8899-checkin.admin.settings.auto-check-in'),\n      type: 'switch',\n    })\n    .registerSetting({\n      setting: 'wusong8899-forum-checkin.autoCheckInDelay',\n      label: app.translator.trans('wusong8899-checkin.admin.settings.auto-check-in-delay'),\n      type: 'number',\n    })\n    .registerSetting({\n      setting: 'wusong8899-forum-checkin.checkinSuccessPromptType',\n      label: app.translator.trans('wusong8899-checkin.admin.settings.check-in-success-prompt-type'),\n      type: 'select',\n      options: {\n        0: app.translator.trans('wusong8899-checkin.admin.settings.None'),\n        1: app.translator.trans('wusong8899-checkin.admin.settings.Alert'),\n        2: app.translator.trans('wusong8899-checkin.admin.settings.Modal')\n      },\n    })\n    .registerSetting(function () {\n      return (\n        <div className=\"Form-group\">\n          <label>{app.translator.trans('wusong8899-checkin.admin.settings.check-in-success-prompt-text')}</label>\n          <div class=\"helpText\">{app.translator.trans('wusong8899-checkin.admin.settings.check-in-success-prompt-example-text')}</div>\n          <input type=\"string\" className=\"FormControl\" step=\"any\" bidi={this.setting('wusong8899-forum-checkin.checkinSuccessPromptText')} />\n        </div>\n      );\n    })\n    .registerSetting(function () {\n      return (\n        <div className=\"Form-group\">\n          <label>{app.translator.trans('wusong8899-checkin.admin.settings.check-in-success-prompt-reward-text')}</label>\n          <div class=\"helpText\">{app.translator.trans('wusong8899-checkin.admin.settings.reward-money-requirement')}</div>\n          <div class=\"helpText\">{app.translator.trans('wusong8899-checkin.admin.settings.check-in-success-prompt-example-reward-text')}</div>\n          <input type=\"string\" className=\"FormControl\" step=\"any\" bidi={this.setting('wusong8899-forum-checkin.checkinSuccessPromptRewardText')} />\n        </div>\n      );\n    })\n    .registerPermission(\n      {\n        icon: 'fas fa-id-card',\n        label: app.translator.trans('wusong8899-checkin.admin.settings.allow-check-in'),\n        permission: 'checkin.allowCheckIn',\n      },\n      'moderate',\n      90\n    )\n});\n"], "names": ["__webpack_require__", "exports", "Symbol", "toStringTag", "Object", "defineProperty", "value", "flarum", "core", "compat", "app", "initializers", "add", "extensionData", "registerSetting", "className", "translator", "trans", "class", "type", "step", "bidi", "this", "setting", "label", "options", "registerPermission", "icon", "permission"], "sourceRoot": ""}