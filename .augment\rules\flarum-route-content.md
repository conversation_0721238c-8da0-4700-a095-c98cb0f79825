---
type: "agent_requested"
description: "本规则旨在指导 AI 编码代理进行 Flarum 扩展中与 **路由、API 端点、页面注册和内容生成** 相关的开发工作。它涵盖了从后端 PHP 到前端 JavaScript 的完整请求-响应生命周期。"
---

# Flarum 扩展路由与内容开发规则

## 1. 目的与适用范围

本规则旨在指导 AI 编码代理进行 Flarum 扩展中与 **路由、API 端点、页面注册和内容生成** 相关的开发工作。它涵盖了从后端 PHP 到前端 JavaScript 的完整请求-响应生命周期。

- **主要范围**:
  - **核心配置文件**: `extend.php` (用于注册所有路由、视图和内容修改器)。
  - **后端代码**: `src/` 目录, 特别是 `Http/Controllers/`, `Api/Controllers/`, `Api/Serializers/`。
  - **视图文件**: `views/` 目录 (用于 Blade 模板)。
  - **前端路由定义**: `js/src/` 下的入口文件 (如 `index.ts`) 和页面组件 (`pages/`)。
- **关联文件**: 本规则与《Flarum 扩展前端开发规则》紧密相关。前端组件的具体实现应遵循另一份文档。

## 2. 结构与格式

- **代码组织**: 严格遵循 Flarum 推荐的后端 `src/` 和前端 `js/src/` 目录结构。
- **原子化规则**: 每条规则都应清晰、独立且可验证。
- **简洁性**: 保持规则精炼，避免冗余信息。

## 3. 工具使用与工作循环

- **代理角色**: 你是一名 Flarum 扩展全栈开发者，负责处理 HTTP 请求、定义 API 和创建新页面。
- **环境约束**:
  - **本地环境**: 已安装 PHP, Composer, Node.js, `pnpm`。
  - **包管理器**: 后端使用 `composer`；前端使用 `pnpm`。
  - **可用命令**: `composer`, `flarum-cli` (例如: `flarum-cli make backend route`)。
- **工作循环**: 严格遵循 **计划 → 执行 → 验证 → 迭代** 循环，特别是对于跨越前后端的任务。
  - **计划 (Plan)**: 将任务拆解为后端和前端步骤。例如：1. 定义 API 路由；2. 创建 API 控制器；3. 定义前端页面路由；4. 创建前端页面组件；5. 组件调用 API。
  - **执行 (Execute)**: 每次只执行一个原子操作。
  - **验证 (Verify)**: 每步操作后，验证其结果。例如，使用 `curl` 或浏览器访问新路由验证后端响应，刷新前端页面验证更改。
  - **验收标准**: 在开始任务前，明确定义任务完成的标志。例如：“`/my-page` 路由成功加载，并通过 `/api/my-data` 获取并正确显示数据”。

## 4. 后端 (PHP) 约定

### 4.1 路由定义 (`extend.php`)

- **路由集合**: Flarum 有三个路由集合：`forum` (前端页面和工具), `admin` (后台页面), `api` (JSON:API)。
- **API 路由**:
  - 使用 `(new Flarum\Extend\Routes('api'))->get('/path', 'my-ext.route.name', MyApiController::class);`
  - 路由名称 (`my-ext.route.name`) 必须是唯一的，并以扩展名作为命名空间。
- **新页面路由**:
  - **必须**使用 `Frontend` Extender 来注册，这会同时处理后端路由和前端引导。
  - `(new Flarum\Extend\Frontend('forum'))->route('/my-page', 'my-ext.page.name');`
- **内容修改**:
  - 为特定页面修改 HTML 文档 (如标题、meta 标签)，在 `route()` 方法后链式调用内容修改闭包。
  - `->route('/my-page', 'my-ext.page.name', function (Flarum\Frontend\Document $document) { $document->title = 'My New Page'; });`
  - 全局内容修改使用 `->content(function (Document $document) { ... });`。

### 4.2 控制器 (Controllers)

- **接口**: 所有控制器必须实现 `Psr\Http\Server\RequestHandlerInterface`。
- **依赖注入**: 控制器通过构造函数注入所需的服务，如 `Flarum\Http\UrlGenerator` 或 `Illuminate\Contracts\View\Factory`。
- **授权**:
  - **必须**在处理逻辑前验证用户权限。
  - 使用 `$request->getAttribute('actor')->assertCan('my-ext.permissionName');`。
  - 若无此行，视为严重安全漏洞。
- **参数获取**:
  - 路由参数 (如 `/users/{id}`) 和查询参数都通过 `$request->getQueryParams()` 获取。
  - 使用 `Illuminate\Support\Arr::get()` 安全地访问数组参数。
- **响应类型**:
  - **API 控制器**: 遵循 JSON:API 规范，通常扩展自 Flarum 的 `Abstract*Controller` 类。
  - **HTML 页面**: 返回 `Laminas\Diactoros\Response\HtmlResponse`。

### 4.3 视图 (Views) 与 URL 生成

- **Blade 视图**:
  - 1. 在 `extend.php` 注册命名空间: `(new Flarum\Extend\View)->namespace('my-ext', __DIR__.'/views');`
  - 2. 在控制器中注入 `Factory` 并渲染: `new HtmlResponse($this->view->make('my-ext::template-name')->render());`
- **URL 生成**:
  - 注入 `Flarum\Http\UrlGenerator` 实例。
  - 使用 `$this->url->to('forum')->route('my-ext.route.name', ['id' => 123]);` 生成 URL。

## 5. 前端 (JS/TS) 约定

### 5.1 路由定义

- **位置**: 在 `js/src/{forum|admin}/index.ts` 的 `app.initializers.add` 回调中定义。
- **方法**: 使用 `app.routes` 对象或 `Routes` extender 进行注册。
  - `app.routes['my-ext.page.name'] = { path: '/my-page', component: MyPageComponent };`
- **命名匹配**: JS 路由的名称 (`my-ext.page.name`) **必须**与 `extend.php` 中定义的 `Frontend` 路由名称完全一致。
- **参数**: 路由参数 (如 `/my-page/:id`) 会自动传递到页面组件的 `this.attrs` 中。

### 5.2 链接与导航

- **禁止硬编码**: **严禁**在 JSX/TSX 中使用 `<a href="/hardcoded/path">` 进行内部页面导航。
- **必须使用 `Link` 组件**: 为了利用 SPA 的优势，**必须**使用 `flarum/common/components/Link`。
  - `import Link from 'flarum/common/components/Link';`
  - `<Link href={app.route('my-ext.page.name', { id: 1 })}>Go to Page</Link>`
- **外部链接**: 对于外部链接, 使用 `<Link external={true} href="https://google.com">Google</Link>`。

## 6. 安全与合规

- **CSRF 保护**: Flarum 中间件自动处理，无需手动添加 Token。
- **授权 (Backend)**: 所有 API 和控制器在执行任何操作或返回数据前，**必须**检查 `actor` 的权限。
- **输入校验 (Backend)**: 对所有来自 `Request` 的输入（查询参数、请求体）进行严格验证，防止注入。

## 7. 非目标与拒绝策略

- **非目标**:
  - 实现具体的前端 UI 组件逻辑（由《前端开发规则》覆盖）。
  - 数据库模型和迁移（虽然与 API 相关，但属于数据层，另有规则）。
- **拒绝条件**:
  - 请求添加的后端路由或控制器缺少权限检查 (`assertCan`)。
  - 请求在前端使用 `<a href>` 进行内部页面跳转，应被修正为使用 `<Link>` 组件。
  - 任务描述中，API 端点和前端页面的概念混淆不清，需请求澄清。

## 8. 最小示例

### 示例: 添加一个自定义的后端路由并返回 HTML

**任务**: 创建一个简单的后端路由 `/hello-html`，它返回一个 "Hello, World!" 的 H1 标题。

**验收标准**:

1.  在浏览器中访问 `https://my-flarum.com/hello-html` 能看到 "Hello, World!"。
2.  该路由在 `extend.php` 中被正确注册。
3.  对应的控制器被创建并返回 `HtmlResponse`。

**代理工作流**:

1.  **计划**:
    - 创建控制器 `src/Http/Controllers/HelloHtmlController.php`。
    - 在 `extend.php` 中使用 `Routes` extender 注册一个 `forum` 路由。
2.  **执行**:

    - **`HelloHtmlController.php`**:

      ```php
      <?php
      namespace MyExt\Http\Controllers;
      use Laminas\Diactoros\Response\HtmlResponse;
      use Psr\Http\Message\ResponseInterface as Response;
      use Psr\Http\Message\ServerRequestInterface as Request;
      use Psr\Http\Server\RequestHandlerInterface;

      class HelloHtmlController implements RequestHandlerInterface {
          public function handle(Request $request): Response {
              // 在真实场景中，这里应该有权限检查
              // $request->getAttribute('actor')->assertCan('viewHelloPage');
              return new HtmlResponse('<h1>Hello, World!</h1>');
          }
      }
      ```

    - **`extend.php`**:

      ```php
      use MyExt\Http\Controllers\HelloHtmlController;
      use Flarum\Extend;

      return [
          (new Extend\Routes('forum'))
              ->get('/hello-html', 'my-ext.hello-html', HelloHtmlController::class),
      ];
      ```

3.  **验证**:
    - 清除 Flarum 缓存。
    - 使用浏览器或 `curl` 访问 `/hello-html`，确认响应内容。
