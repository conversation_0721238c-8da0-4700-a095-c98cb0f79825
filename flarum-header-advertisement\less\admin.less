/* Header Advertisement Admin Styles */

.Form-group {
  margin-bottom: 20px;
}

.FormLabel {
  font-weight: 600;
  margin-bottom: 8px;
  display: block;
}

.helpText {
  color: #666;
  font-size: 0.9em;
  margin-bottom: 10px;
}

.FormControl {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.FormControl:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Dynamic Slide Settings */
.DynamicSlideSettings {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  background: #f9f9f9;
}

.DynamicSlideSettings-slide {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
}

.DynamicSlideSettings-slideHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.DynamicSlideSettings-slideHeader h4 {
  margin: 0;
  color: #333;
}

.DynamicSlideSettings-slideFields {
  display: grid;
  gap: 15px;
}

.DynamicSlideSettings-addButton {
  text-align: center;
  margin-top: 20px;
}

.Button--danger {
  background-color: #dc3545;
  border-color: #dc3545;
  color: #fff;
}

.Button--danger:hover {
  background-color: #c82333;
  border-color: #bd2130;
}

.Button--primary {
  background-color: #007bff;
  border-color: #007bff;
  color: #fff;
}

.Button--primary:hover {
  background-color: #0056b3;
  border-color: #004085;
}
