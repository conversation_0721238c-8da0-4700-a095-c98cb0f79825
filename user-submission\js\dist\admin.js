(function(t,c,d,e,b,g,h,i){"use strict";const r=class r extends g{constructor(){super(...arguments),this.loading=!1}oninit(s){super.oninit(s),this.itemData=this.attrs.itemData,this.loading=!1}className(){return"Modal--small"}title(){return t.translator.trans("wusong8899-user-submission.admin.submission-review")}content(){return m("div",{className:"Modal-body"},m("div",{className:"Form-group",style:"text-align: center;"},e.component({style:"min-width:66px;",className:"Button Button--primary",disabled:this.loading,onclick:s=>{this.reviewConfirm(s,"approved")}},t.translator.trans("wusong8899-user-submission.lib.accept"))," ",e.component({style:"min-width:66px;",className:"<PERSON><PERSON>--danger",disabled:this.loading,onclick:s=>{this.reviewConfirm(s,"rejected")}},t.translator.trans("wusong8899-user-submission.lib.decline"))," ",e.component({style:"min-width:66px;",className:"Button",disabled:this.loading,onclick:()=>{this.hide()}},t.translator.trans("wusong8899-user-submission.lib.cancel"))))}reviewConfirm(s,n){s.preventDefault(),this.loading=!0,this.itemData.save({reviewResult:n}).then(()=>{this.hide(),this.loading=!1}).catch(()=>{this.loading=!1})}};r.isDismissibleViaBackdropClick=!1,r.isDismissibleViaCloseButton=!0;let a=r;class w extends b{view(){const{itemData:s}=this.attrs,n=s.amount(),v=s.platform(),y=s.platform_account(),p=s.user_account(),x=s.fromUser(),_=s.review_result(),N=t.translator.trans(_==="approved"?"wusong8899-user-submission.lib.list-submission-accept":"wusong8899-user-submission.lib.list-submission-decline"),R=s.assigned_at(),o=s.reviewed_at();return m("div",{className:"biddingRankSettingContainer"},m("div",{style:"float:right"},m("div",{style:"display: flex; gap: 5px; align-items: center;"},o&&m("div",null,e.component({style:"font-weight:bold;",disabled:!0,className:"Button"},t.translator.trans("wusong8899-user-submission.admin.list-reviewed"))),!o&&m("div",null,e.component({style:"font-weight:bold;",className:"Button Button--primary",onclick:()=>{this.reviewItem(s)}},t.translator.trans("wusong8899-user-submission.admin.list-review"))),m("div",null,e.component({className:"Button Button--danger",icon:"fas fa-trash",title:t.translator.trans("wusong8899-user-submission.admin.delete-submission"),onclick:()=>{this.deleteItem(s)}})))),m("div",null,m("b",null,t.translator.trans("wusong8899-user-submission.lib.list-username"),": "),h(x)," | ",m("b",null,t.translator.trans("wusong8899-user-submission.lib.list-amount"),": "),n," | ",m("b",null,t.translator.trans("wusong8899-user-submission.lib.list-assignedAt"),": "),R),m("div",null,m("b",null,t.translator.trans("wusong8899-user-submission.lib.list-platform"),": "),v," | ",m("b",null,t.translator.trans("wusong8899-user-submission.lib.list-platformAccount"),": "),y," | ",m("b",null,t.translator.trans("wusong8899-user-submission.lib.list-userAccount"),": "),p),o&&m("div",null,m("b",null,t.translator.trans("wusong8899-user-submission.lib.list-reviewResult"),": "),N," | ",m("b",null,t.translator.trans("wusong8899-user-submission.lib.list-reviewAt"),": "),o))}reviewItem(s){t.modal.show(a,{itemData:s})}deleteItem(s){confirm(t.translator.trans("wusong8899-user-submission.admin.confirm-delete"))&&t.request({method:"DELETE",url:t.forum.attribute("apiUrl")+"/userSubmissionList/"+s.id()}).then(()=>{window.location.reload()}).catch(()=>{alert(t.translator.trans("wusong8899-user-submission.admin.delete-failed"))})}}class f extends c{constructor(){super(...arguments),this.loading=!0,this.moreResults=!1,this.userSubmissionList=[]}oninit(s){super.oninit(s),this.loading=!0,this.moreResults=!1,this.userSubmissionList=[],this.loadResults()}content(){let s;return this.loading&&(s=d.component({size:"large"})),m("div",{className:"ExtensionPage-settings FlarumBadgesPage"},m("div",{className:"container"},m("ul",{style:"padding:0px;list-style-type: none;"},this.userSubmissionList.map(n=>m("li",{itemID:n.id(),style:"margin-top:5px;background: var(--body-bg);"},w.component({itemData:n})))),!this.loading&&this.userSubmissionList.length===0&&m("div",null,m("div",{style:"font-size:1.4em;color: var(--muted-more-color);text-align: center;line-height: 100px;"},t.translator.trans("wusong8899-user-submission.lib.list-empty"))),!s&&this.hasMoreResults()&&m("div",{style:"text-align:center;padding:20px"},m(e,{className:"Button Button--primary",disabled:this.loading,loading:this.loading,onclick:()=>this.loadMore()},t.translator.trans("wusong8899-user-submission.lib.list-load-more"))),s&&m("div",{className:"UserSubmission-loadMore"},s)))}hasMoreResults(){return this.moreResults}loadMore(){this.loading=!0,this.loadResults(this.userSubmissionList.length)}parseResults(s){return this.moreResults=!!s.payload?.links?.next,this.userSubmissionList.push(...s),this.loading=!1,m.redraw(),s}loadResults(s=0){return t.store.find("userSubmissionList",{page:{offset:s}}).catch(()=>[]).then(this.parseResults.bind(this))}}class l extends i{}Object.assign(l.prototype,{id:i.attribute("id"),amount:i.attribute("amount"),platform:i.attribute("platform"),platform_account:i.attribute("platform_account"),user_account:i.attribute("user_account"),submission_user_id:i.attribute("submission_user_id"),review_user_id:i.attribute("review_user_id"),review_result:i.attribute("review_result"),assigned_at:i.attribute("assigned_at"),reviewed_at:i.attribute("reviewed_at"),fromUser:i.hasOne("fromUser"),reviewUser:i.hasOne("reviewUser")}),t.initializers.add("wusong8899-user-submission",()=>{t.store.models.userSubmissionList=l,t.extensionData.for("wusong8899-user-submission").registerPage(f)})})(flarum.core.compat["admin/app"],flarum.core.compat["components/ExtensionPage"],flarum.core.compat["components/LoadingIndicator"],flarum.core.compat["components/Button"],flarum.core.compat.Component,flarum.core.compat["components/Modal"],flarum.core.compat["helpers/username"],flarum.core.compat.Model);
//# sourceMappingURL=admin.js.map

module.exports={};