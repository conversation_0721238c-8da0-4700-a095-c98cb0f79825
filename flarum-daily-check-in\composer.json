{"name": "wusong8899/flarum-daily-check-in", "description": "A Flarum extension. Add a button in the index page that allow user to daily check in the forum and receive reward.", "type": "flarum-extension", "license": "MIT", "require": {"flarum/core": "^1.0"}, "support": {"issues": "https://github.com/wusong8899/flarum-daily-check-in/issues", "source": "https://github.com/wusong8899/flarum-daily-check-in"}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"wusong8899\\checkin\\": "src/"}}, "authors": [{"name": "wusong8899"}], "extra": {"flarum-extension": {"title": "wusong8899 Daily Check In", "icon": {"name": "fas fa-calendar-check", "backgroundColor": "#238c59", "color": "#fff"}}}}