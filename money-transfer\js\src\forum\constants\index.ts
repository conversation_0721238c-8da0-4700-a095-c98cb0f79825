/**
 * Constants for the money transfer extension
 */

// Default values
export const DEFAULT_MONEY_AMOUNT = 0;
export const DEFAULT_USER_COUNT = 0;
export const MINIMUM_TRANSFER_AMOUNT = 0;

// UI Constants
export const MODAL_PADDING_BOTTOM = '20px';

// Permission constants
export const PERMISSION_TRANSFER_MONEY = 'transferMoney.allowUseTransferMoney';

// Default money name placeholder
export const DEFAULT_MONEY_NAME = '[money]';

// Money attribute key
export const MONEY_ATTRIBUTE_KEY = 'antoinefr-money.moneyname';

// User selection prefix
export const USER_SELECTION_PREFIX = 'users:';

// Default user selection count
export const DEFAULT_USER_SELECTION_COUNT = 1;

// Search modal constants
export const RANDOM_STRING_BASE = 36;
export const RANDOM_STRING_START_INDEX = 2;
export const SEARCH_TYPING_DELAY = 900;
export const MIN_SEARCH_LENGTH = 3;
export const EMPTY_ARRAY_LENGTH = 0;
export const FIRST_ARRAY_INDEX = 0;
export const SECOND_ARRAY_INDEX = 1;

// Pagination constants
export const STANDARD_ITEMS_PER_PAGE = 20;

// UI Constants
export const TEXTAREA_MAX_LENGTH = 255;
export const BUTTON_WIDTH = '66px';
export const CONTAINER_HEIGHT = '34px';

// Route constants
export const ROUTE_TAGS = 'tags';

// CSS Display values
export const CSS_DISPLAY_NONE = 'none';
export const CSS_VISIBILITY_HIDDEN = 'hidden';

// Extension constants
export const EXTENSION_FLARUM_SUSPEND = 'flarum-suspend';
export const CUSTOMIZATION_ENABLED = '1';
