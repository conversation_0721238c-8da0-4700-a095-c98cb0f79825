{"name": "wusong8899/view-history", "description": "A Flarum extension. Allow user to see the view history of discussions.", "type": "flarum-extension", "license": "MIT", "require": {"flarum/core": "^1.0"}, "support": {"issues": "https://github.com/wusong8899/view-history/issues", "source": "https://github.com/wusong8899/view-history"}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"wusong8899\\viewHistory\\": "src/"}}, "authors": [{"name": "wusong8899"}], "extra": {"flarum-extension": {"title": "wusong8899 View History", "icon": {"name": "fas fa-history", "backgroundColor": "#238c59", "color": "#fff"}}}}